import type { PropType } from 'vue';
export declare const makeVDialogTransitionProps: <Defaults extends {
    target?: unknown;
} = {}>(defaults?: Defaults | undefined) => {
    target: unknown extends Defaults["target"] ? PropType<HTMLElement | [x: number, y: number]> : {
        type: PropType<unknown extends Defaults["target"] ? HTMLElement | [x: number, y: number] : HTMLElement | [x: number, y: number] | Defaults["target"]>;
        default: unknown extends Defaults["target"] ? HTMLElement | [x: number, y: number] : Defaults["target"] | NonNullable<HTMLElement | [x: number, y: number]>;
    };
};
export declare const VDialogTransition: {
    new (...args: any[]): import("vue").CreateComponentPublicInstanceWithMixins<{} & {
        target?: HTMLElement | [x: number, y: number] | undefined;
    } & {
        $children?: import("vue").VNodeChild | {
            default?: (() => import("vue").VNodeChild) | undefined;
        } | (() => import("vue").VNodeChild);
        'v-slots'?: {
            default?: false | (() => import("vue").VNodeChild) | undefined;
        } | undefined;
    } & {
        "v-slot:default"?: false | (() => import("vue").VNodeChild) | undefined;
    }, () => JSX.Element, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, {}, true, {}, import("vue").SlotsType<Partial<{
        default: () => import("vue").VNode[];
    }>>, import("vue").GlobalComponents, import("vue").GlobalDirectives, string, {}, any, import("vue").ComponentProvideOptions, {
        P: {};
        B: {};
        D: {};
        C: {};
        M: {};
        Defaults: {};
    }, {} & {
        target?: HTMLElement | [x: number, y: number] | undefined;
    } & {
        $children?: import("vue").VNodeChild | {
            default?: (() => import("vue").VNodeChild) | undefined;
        } | (() => import("vue").VNodeChild);
        'v-slots'?: {
            default?: false | (() => import("vue").VNodeChild) | undefined;
        } | undefined;
    } & {
        "v-slot:default"?: false | (() => import("vue").VNodeChild) | undefined;
    }, () => JSX.Element, {}, {}, {}, {}>;
    __isFragment?: never;
    __isTeleport?: never;
    __isSuspense?: never;
} & import("vue").ComponentOptionsBase<{} & {
    target?: HTMLElement | [x: number, y: number] | undefined;
} & {
    $children?: import("vue").VNodeChild | {
        default?: (() => import("vue").VNodeChild) | undefined;
    } | (() => import("vue").VNodeChild);
    'v-slots'?: {
        default?: false | (() => import("vue").VNodeChild) | undefined;
    } | undefined;
} & {
    "v-slot:default"?: false | (() => import("vue").VNodeChild) | undefined;
}, () => JSX.Element, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, {}, {}, string, import("vue").SlotsType<Partial<{
    default: () => import("vue").VNode[];
}>>, import("vue").GlobalComponents, import("vue").GlobalDirectives, string, import("vue").ComponentProvideOptions> & import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps & import("../../util/index.js").FilterPropsOptions<{
    target: PropType<HTMLElement | [x: number, y: number]>;
}, import("vue").ExtractPropTypes<{
    target: PropType<HTMLElement | [x: number, y: number]>;
}>>;
export type VDialogTransition = InstanceType<typeof VDialogTransition>;
