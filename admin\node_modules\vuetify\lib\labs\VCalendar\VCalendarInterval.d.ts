type Interval = {
    label: string;
    start: unknown;
    end: unknown;
    events: any[];
};
export type VCalendarIntervalSlots = {
    intervalBody: {
        interval: Interval;
    };
    intervalEvent: {
        height: string;
        margin: string;
        eventClass: string;
        event: any;
        interval: Interval;
    };
    intervalTitle: {
        interval: Interval;
    };
};
export declare const makeVCalendarIntervalProps: <Defaults extends {
    day?: unknown;
    dayIndex?: unknown;
    events?: unknown;
    intervalDivisions?: unknown;
    intervalDuration?: unknown;
    intervalHeight?: unknown;
    intervalFormat?: unknown;
    intervalStart?: unknown;
} = {}>(defaults?: Defaults | undefined) => {
    day: unknown extends Defaults["day"] ? {
        type: ObjectConstructor;
        default: () => {};
    } : Omit<{
        type: ObjectConstructor;
        default: () => {};
    }, "type" | "default"> & {
        type: import("vue").PropType<unknown extends Defaults["day"] ? Record<string, any> : Record<string, any> | Defaults["day"]>;
        default: unknown extends Defaults["day"] ? Record<string, any> : Record<string, any> | Defaults["day"];
    };
    dayIndex: unknown extends Defaults["dayIndex"] ? NumberConstructor : {
        type: import("vue").PropType<unknown extends Defaults["dayIndex"] ? number : number | Defaults["dayIndex"]>;
        default: unknown extends Defaults["dayIndex"] ? number : number | Defaults["dayIndex"];
    };
    events: unknown extends Defaults["events"] ? {
        (arrayLength: number): any[];
        (...items: any[]): any[];
        new (arrayLength: number): any[];
        new (...items: any[]): any[];
        isArray(arg: any): arg is any[];
        readonly prototype: any[];
        from<T>(arrayLike: ArrayLike<T>): T[];
        from<T, U>(arrayLike: ArrayLike<T>, mapfn: (v: T, k: number) => U, thisArg?: any): U[];
        from<T>(iterable: Iterable<T> | ArrayLike<T>): T[];
        from<T, U_1>(iterable: Iterable<T> | ArrayLike<T>, mapfn: (v: T, k: number) => U_1, thisArg?: any): U_1[];
        of<T>(...items: T[]): T[];
        fromAsync<T>(iterableOrArrayLike: AsyncIterable<T> | Iterable<T | PromiseLike<T>> | ArrayLike<T | PromiseLike<T>>): Promise<T[]>;
        fromAsync<T, U_2>(iterableOrArrayLike: AsyncIterable<T> | Iterable<T> | ArrayLike<T>, mapFn: (value: Awaited<T>, index: number) => U_2, thisArg?: any): Promise<Awaited<U_2>[]>;
        readonly [Symbol.species]: ArrayConstructor;
    } : {
        type: import("vue").PropType<unknown extends Defaults["events"] ? any[] : any[] | Defaults["events"]>;
        default: unknown extends Defaults["events"] ? any[] : any[] | Defaults["events"];
    };
    intervalDivisions: unknown extends Defaults["intervalDivisions"] ? {
        type: NumberConstructor;
        default: number;
    } : Omit<{
        type: NumberConstructor;
        default: number;
    }, "type" | "default"> & {
        type: import("vue").PropType<unknown extends Defaults["intervalDivisions"] ? number : number | Defaults["intervalDivisions"]>;
        default: unknown extends Defaults["intervalDivisions"] ? number : number | Defaults["intervalDivisions"];
    };
    intervalDuration: unknown extends Defaults["intervalDuration"] ? {
        type: NumberConstructor;
        default: number;
    } : Omit<{
        type: NumberConstructor;
        default: number;
    }, "type" | "default"> & {
        type: import("vue").PropType<unknown extends Defaults["intervalDuration"] ? number : number | Defaults["intervalDuration"]>;
        default: unknown extends Defaults["intervalDuration"] ? number : number | Defaults["intervalDuration"];
    };
    intervalHeight: unknown extends Defaults["intervalHeight"] ? {
        type: NumberConstructor;
        default: number;
    } : Omit<{
        type: NumberConstructor;
        default: number;
    }, "type" | "default"> & {
        type: import("vue").PropType<unknown extends Defaults["intervalHeight"] ? number : number | Defaults["intervalHeight"]>;
        default: unknown extends Defaults["intervalHeight"] ? number : number | Defaults["intervalHeight"];
    };
    intervalFormat: unknown extends Defaults["intervalFormat"] ? {
        type: (FunctionConstructor | StringConstructor)[];
        default: string;
    } : Omit<{
        type: (FunctionConstructor | StringConstructor)[];
        default: string;
    }, "type" | "default"> & {
        type: import("vue").PropType<unknown extends Defaults["intervalFormat"] ? string | Function : string | Function | Defaults["intervalFormat"]>;
        default: unknown extends Defaults["intervalFormat"] ? string | Function : Defaults["intervalFormat"] | NonNullable<string | Function>;
    };
    intervalStart: unknown extends Defaults["intervalStart"] ? {
        type: NumberConstructor;
        default: number;
    } : Omit<{
        type: NumberConstructor;
        default: number;
    }, "type" | "default"> & {
        type: import("vue").PropType<unknown extends Defaults["intervalStart"] ? number : number | Defaults["intervalStart"]>;
        default: unknown extends Defaults["intervalStart"] ? number : number | Defaults["intervalStart"];
    };
};
export declare const VCalendarInterval: {
    new (...args: any[]): import("vue").CreateComponentPublicInstanceWithMixins<{
        index: number;
        day: Record<string, any>;
        intervalDivisions: number;
        intervalDuration: number;
        intervalHeight: number;
        intervalFormat: string | Function;
        intervalStart: number;
    } & {
        events?: any[] | undefined;
        dayIndex?: number | undefined;
    } & {
        $children?: {} | import("vue").VNodeChild | {
            intervalBody?: ((arg: {
                interval: Interval;
            }) => import("vue").VNodeChild) | undefined;
            intervalEvent?: ((arg: {
                height: string;
                margin: string;
                eventClass: string;
                event: any;
                interval: Interval;
            }) => import("vue").VNodeChild) | undefined;
            intervalTitle?: ((arg: {
                interval: Interval;
            }) => import("vue").VNodeChild) | undefined;
        };
        'v-slots'?: {
            intervalBody?: false | ((arg: {
                interval: Interval;
            }) => import("vue").VNodeChild) | undefined;
            intervalEvent?: false | ((arg: {
                height: string;
                margin: string;
                eventClass: string;
                event: any;
                interval: Interval;
            }) => import("vue").VNodeChild) | undefined;
            intervalTitle?: false | ((arg: {
                interval: Interval;
            }) => import("vue").VNodeChild) | undefined;
        } | undefined;
    } & {
        "v-slot:intervalBody"?: false | ((arg: {
            interval: Interval;
        }) => import("vue").VNodeChild) | undefined;
        "v-slot:intervalEvent"?: false | ((arg: {
            height: string;
            margin: string;
            eventClass: string;
            event: any;
            interval: Interval;
        }) => import("vue").VNodeChild) | undefined;
        "v-slot:intervalTitle"?: false | ((arg: {
            interval: Interval;
        }) => import("vue").VNodeChild) | undefined;
    }, {
        interval: import("vue").ComputedRef<{
            label: string;
            start: unknown;
            end: unknown;
            events: any[];
        }>;
    }, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, {
        day: Record<string, any>;
        intervalDivisions: number;
        intervalDuration: number;
        intervalHeight: number;
        intervalFormat: string | Function;
        intervalStart: number;
    }, true, {}, import("vue").SlotsType<Partial<{
        intervalBody: (arg: {
            interval: Interval;
        }) => import("vue").VNode[];
        intervalEvent: (arg: {
            height: string;
            margin: string;
            eventClass: string;
            event: any;
            interval: Interval;
        }) => import("vue").VNode[];
        intervalTitle: (arg: {
            interval: Interval;
        }) => import("vue").VNode[];
    }>>, import("vue").GlobalComponents, import("vue").GlobalDirectives, string, {}, any, import("vue").ComponentProvideOptions, {
        P: {};
        B: {};
        D: {};
        C: {};
        M: {};
        Defaults: {};
    }, {
        index: number;
        day: Record<string, any>;
        intervalDivisions: number;
        intervalDuration: number;
        intervalHeight: number;
        intervalFormat: string | Function;
        intervalStart: number;
    } & {
        events?: any[] | undefined;
        dayIndex?: number | undefined;
    } & {
        $children?: {} | import("vue").VNodeChild | {
            intervalBody?: ((arg: {
                interval: Interval;
            }) => import("vue").VNodeChild) | undefined;
            intervalEvent?: ((arg: {
                height: string;
                margin: string;
                eventClass: string;
                event: any;
                interval: Interval;
            }) => import("vue").VNodeChild) | undefined;
            intervalTitle?: ((arg: {
                interval: Interval;
            }) => import("vue").VNodeChild) | undefined;
        };
        'v-slots'?: {
            intervalBody?: false | ((arg: {
                interval: Interval;
            }) => import("vue").VNodeChild) | undefined;
            intervalEvent?: false | ((arg: {
                height: string;
                margin: string;
                eventClass: string;
                event: any;
                interval: Interval;
            }) => import("vue").VNodeChild) | undefined;
            intervalTitle?: false | ((arg: {
                interval: Interval;
            }) => import("vue").VNodeChild) | undefined;
        } | undefined;
    } & {
        "v-slot:intervalBody"?: false | ((arg: {
            interval: Interval;
        }) => import("vue").VNodeChild) | undefined;
        "v-slot:intervalEvent"?: false | ((arg: {
            height: string;
            margin: string;
            eventClass: string;
            event: any;
            interval: Interval;
        }) => import("vue").VNodeChild) | undefined;
        "v-slot:intervalTitle"?: false | ((arg: {
            interval: Interval;
        }) => import("vue").VNodeChild) | undefined;
    }, {
        interval: import("vue").ComputedRef<{
            label: string;
            start: unknown;
            end: unknown;
            events: any[];
        }>;
    }, {}, {}, {}, {
        day: Record<string, any>;
        intervalDivisions: number;
        intervalDuration: number;
        intervalHeight: number;
        intervalFormat: string | Function;
        intervalStart: number;
    }>;
    __isFragment?: never;
    __isTeleport?: never;
    __isSuspense?: never;
} & import("vue").ComponentOptionsBase<{
    index: number;
    day: Record<string, any>;
    intervalDivisions: number;
    intervalDuration: number;
    intervalHeight: number;
    intervalFormat: string | Function;
    intervalStart: number;
} & {
    events?: any[] | undefined;
    dayIndex?: number | undefined;
} & {
    $children?: {} | import("vue").VNodeChild | {
        intervalBody?: ((arg: {
            interval: Interval;
        }) => import("vue").VNodeChild) | undefined;
        intervalEvent?: ((arg: {
            height: string;
            margin: string;
            eventClass: string;
            event: any;
            interval: Interval;
        }) => import("vue").VNodeChild) | undefined;
        intervalTitle?: ((arg: {
            interval: Interval;
        }) => import("vue").VNodeChild) | undefined;
    };
    'v-slots'?: {
        intervalBody?: false | ((arg: {
            interval: Interval;
        }) => import("vue").VNodeChild) | undefined;
        intervalEvent?: false | ((arg: {
            height: string;
            margin: string;
            eventClass: string;
            event: any;
            interval: Interval;
        }) => import("vue").VNodeChild) | undefined;
        intervalTitle?: false | ((arg: {
            interval: Interval;
        }) => import("vue").VNodeChild) | undefined;
    } | undefined;
} & {
    "v-slot:intervalBody"?: false | ((arg: {
        interval: Interval;
    }) => import("vue").VNodeChild) | undefined;
    "v-slot:intervalEvent"?: false | ((arg: {
        height: string;
        margin: string;
        eventClass: string;
        event: any;
        interval: Interval;
    }) => import("vue").VNodeChild) | undefined;
    "v-slot:intervalTitle"?: false | ((arg: {
        interval: Interval;
    }) => import("vue").VNodeChild) | undefined;
}, {
    interval: import("vue").ComputedRef<{
        label: string;
        start: unknown;
        end: unknown;
        events: any[];
    }>;
}, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, {
    day: Record<string, any>;
    intervalDivisions: number;
    intervalDuration: number;
    intervalHeight: number;
    intervalFormat: string | Function;
    intervalStart: number;
}, {}, string, import("vue").SlotsType<Partial<{
    intervalBody: (arg: {
        interval: Interval;
    }) => import("vue").VNode[];
    intervalEvent: (arg: {
        height: string;
        margin: string;
        eventClass: string;
        event: any;
        interval: Interval;
    }) => import("vue").VNode[];
    intervalTitle: (arg: {
        interval: Interval;
    }) => import("vue").VNode[];
}>>, import("vue").GlobalComponents, import("vue").GlobalDirectives, string, import("vue").ComponentProvideOptions> & import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps & import("../../util/index.js").FilterPropsOptions<{
    day: {
        type: ObjectConstructor;
        default: () => {};
    };
    dayIndex: NumberConstructor;
    events: {
        (arrayLength: number): any[];
        (...items: any[]): any[];
        new (arrayLength: number): any[];
        new (...items: any[]): any[];
        isArray(arg: any): arg is any[];
        readonly prototype: any[];
        from<T>(arrayLike: ArrayLike<T>): T[];
        from<T, U>(arrayLike: ArrayLike<T>, mapfn: (v: T, k: number) => U, thisArg?: any): U[];
        from<T>(iterable: Iterable<T> | ArrayLike<T>): T[];
        from<T, U>(iterable: Iterable<T> | ArrayLike<T>, mapfn: (v: T, k: number) => U, thisArg?: any): U[];
        of<T>(...items: T[]): T[];
        fromAsync<T>(iterableOrArrayLike: AsyncIterable<T> | Iterable<T | PromiseLike<T>> | ArrayLike<T | PromiseLike<T>>): Promise<T[]>;
        fromAsync<T, U>(iterableOrArrayLike: AsyncIterable<T> | Iterable<T> | ArrayLike<T>, mapFn: (value: Awaited<T>, index: number) => U, thisArg?: any): Promise<Awaited<U>[]>;
        readonly [Symbol.species]: ArrayConstructor;
    };
    intervalDivisions: {
        type: NumberConstructor;
        default: number;
    };
    intervalDuration: {
        type: NumberConstructor;
        default: number;
    };
    intervalHeight: {
        type: NumberConstructor;
        default: number;
    };
    intervalFormat: {
        type: (FunctionConstructor | StringConstructor)[];
        default: string;
    };
    intervalStart: {
        type: NumberConstructor;
        default: number;
    };
    index: {
        type: NumberConstructor;
        required: true;
    };
}, import("vue").ExtractPropTypes<{
    day: {
        type: ObjectConstructor;
        default: () => {};
    };
    dayIndex: NumberConstructor;
    events: {
        (arrayLength: number): any[];
        (...items: any[]): any[];
        new (arrayLength: number): any[];
        new (...items: any[]): any[];
        isArray(arg: any): arg is any[];
        readonly prototype: any[];
        from<T>(arrayLike: ArrayLike<T>): T[];
        from<T, U>(arrayLike: ArrayLike<T>, mapfn: (v: T, k: number) => U, thisArg?: any): U[];
        from<T>(iterable: Iterable<T> | ArrayLike<T>): T[];
        from<T, U>(iterable: Iterable<T> | ArrayLike<T>, mapfn: (v: T, k: number) => U, thisArg?: any): U[];
        of<T>(...items: T[]): T[];
        fromAsync<T>(iterableOrArrayLike: AsyncIterable<T> | Iterable<T | PromiseLike<T>> | ArrayLike<T | PromiseLike<T>>): Promise<T[]>;
        fromAsync<T, U>(iterableOrArrayLike: AsyncIterable<T> | Iterable<T> | ArrayLike<T>, mapFn: (value: Awaited<T>, index: number) => U, thisArg?: any): Promise<Awaited<U>[]>;
        readonly [Symbol.species]: ArrayConstructor;
    };
    intervalDivisions: {
        type: NumberConstructor;
        default: number;
    };
    intervalDuration: {
        type: NumberConstructor;
        default: number;
    };
    intervalHeight: {
        type: NumberConstructor;
        default: number;
    };
    intervalFormat: {
        type: (FunctionConstructor | StringConstructor)[];
        default: string;
    };
    intervalStart: {
        type: NumberConstructor;
        default: number;
    };
    index: {
        type: NumberConstructor;
        required: true;
    };
}>>;
export type VCalendarInterval = InstanceType<typeof VCalendarInterval>;

