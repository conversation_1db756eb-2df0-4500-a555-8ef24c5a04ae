const express = require('express');
const router = express.Router();
const {
  register,
  login,
  getCurrentUser,
  updateProfile,
  registerValidation,
  loginValidation
} = require('../controllers/authController');
const { authenticateToken } = require('../middleware/auth');

// 用户注册
router.post('/register', registerValidation, register);

// 用户登录
router.post('/login', loginValidation, login);

// 获取当前用户信息
router.get('/me', authenticateToken, getCurrentUser);

// 更新用户信息
router.put('/profile', authenticateToken, updateProfile);

module.exports = router;
