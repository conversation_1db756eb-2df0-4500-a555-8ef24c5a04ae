const express = require('express');
const router = express.Router();
const {
  getActiveBanners,
  getBannerList,
  createBanner,
  updateBanner,
  deleteBanner,
  bannerValidation
} = require('../controllers/bannerController');
const { authenticateToken, requireAdmin } = require('../middleware/auth');

// 公开路由
router.get('/active', getActiveBanners);

// 需要管理员权限的路由
router.get('/', authenticateToken, requireAdmin, getBannerList);
router.post('/', authenticateToken, requireAdmin, bannerValidation, createBanner);
router.put('/:id', authenticateToken, requireAdmin, updateBanner);
router.delete('/:id', authenticateToken, requireAdmin, deleteBanner);

module.exports = router;
