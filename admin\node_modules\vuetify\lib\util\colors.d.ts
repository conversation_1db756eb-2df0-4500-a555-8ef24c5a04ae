export declare const red: {
    base: string;
    lighten5: string;
    lighten4: string;
    lighten3: string;
    lighten2: string;
    lighten1: string;
    darken1: string;
    darken2: string;
    darken3: string;
    darken4: string;
    accent1: string;
    accent2: string;
    accent3: string;
    accent4: string;
};
export declare const pink: {
    base: string;
    lighten5: string;
    lighten4: string;
    lighten3: string;
    lighten2: string;
    lighten1: string;
    darken1: string;
    darken2: string;
    darken3: string;
    darken4: string;
    accent1: string;
    accent2: string;
    accent3: string;
    accent4: string;
};
export declare const purple: {
    base: string;
    lighten5: string;
    lighten4: string;
    lighten3: string;
    lighten2: string;
    lighten1: string;
    darken1: string;
    darken2: string;
    darken3: string;
    darken4: string;
    accent1: string;
    accent2: string;
    accent3: string;
    accent4: string;
};
export declare const deepPurple: {
    base: string;
    lighten5: string;
    lighten4: string;
    lighten3: string;
    lighten2: string;
    lighten1: string;
    darken1: string;
    darken2: string;
    darken3: string;
    darken4: string;
    accent1: string;
    accent2: string;
    accent3: string;
    accent4: string;
};
export declare const indigo: {
    base: string;
    lighten5: string;
    lighten4: string;
    lighten3: string;
    lighten2: string;
    lighten1: string;
    darken1: string;
    darken2: string;
    darken3: string;
    darken4: string;
    accent1: string;
    accent2: string;
    accent3: string;
    accent4: string;
};
export declare const blue: {
    base: string;
    lighten5: string;
    lighten4: string;
    lighten3: string;
    lighten2: string;
    lighten1: string;
    darken1: string;
    darken2: string;
    darken3: string;
    darken4: string;
    accent1: string;
    accent2: string;
    accent3: string;
    accent4: string;
};
export declare const lightBlue: {
    base: string;
    lighten5: string;
    lighten4: string;
    lighten3: string;
    lighten2: string;
    lighten1: string;
    darken1: string;
    darken2: string;
    darken3: string;
    darken4: string;
    accent1: string;
    accent2: string;
    accent3: string;
    accent4: string;
};
export declare const cyan: {
    base: string;
    lighten5: string;
    lighten4: string;
    lighten3: string;
    lighten2: string;
    lighten1: string;
    darken1: string;
    darken2: string;
    darken3: string;
    darken4: string;
    accent1: string;
    accent2: string;
    accent3: string;
    accent4: string;
};
export declare const teal: {
    base: string;
    lighten5: string;
    lighten4: string;
    lighten3: string;
    lighten2: string;
    lighten1: string;
    darken1: string;
    darken2: string;
    darken3: string;
    darken4: string;
    accent1: string;
    accent2: string;
    accent3: string;
    accent4: string;
};
export declare const green: {
    base: string;
    lighten5: string;
    lighten4: string;
    lighten3: string;
    lighten2: string;
    lighten1: string;
    darken1: string;
    darken2: string;
    darken3: string;
    darken4: string;
    accent1: string;
    accent2: string;
    accent3: string;
    accent4: string;
};
export declare const lightGreen: {
    base: string;
    lighten5: string;
    lighten4: string;
    lighten3: string;
    lighten2: string;
    lighten1: string;
    darken1: string;
    darken2: string;
    darken3: string;
    darken4: string;
    accent1: string;
    accent2: string;
    accent3: string;
    accent4: string;
};
export declare const lime: {
    base: string;
    lighten5: string;
    lighten4: string;
    lighten3: string;
    lighten2: string;
    lighten1: string;
    darken1: string;
    darken2: string;
    darken3: string;
    darken4: string;
    accent1: string;
    accent2: string;
    accent3: string;
    accent4: string;
};
export declare const yellow: {
    base: string;
    lighten5: string;
    lighten4: string;
    lighten3: string;
    lighten2: string;
    lighten1: string;
    darken1: string;
    darken2: string;
    darken3: string;
    darken4: string;
    accent1: string;
    accent2: string;
    accent3: string;
    accent4: string;
};
export declare const amber: {
    base: string;
    lighten5: string;
    lighten4: string;
    lighten3: string;
    lighten2: string;
    lighten1: string;
    darken1: string;
    darken2: string;
    darken3: string;
    darken4: string;
    accent1: string;
    accent2: string;
    accent3: string;
    accent4: string;
};
export declare const orange: {
    base: string;
    lighten5: string;
    lighten4: string;
    lighten3: string;
    lighten2: string;
    lighten1: string;
    darken1: string;
    darken2: string;
    darken3: string;
    darken4: string;
    accent1: string;
    accent2: string;
    accent3: string;
    accent4: string;
};
export declare const deepOrange: {
    base: string;
    lighten5: string;
    lighten4: string;
    lighten3: string;
    lighten2: string;
    lighten1: string;
    darken1: string;
    darken2: string;
    darken3: string;
    darken4: string;
    accent1: string;
    accent2: string;
    accent3: string;
    accent4: string;
};
export declare const brown: {
    base: string;
    lighten5: string;
    lighten4: string;
    lighten3: string;
    lighten2: string;
    lighten1: string;
    darken1: string;
    darken2: string;
    darken3: string;
    darken4: string;
};
export declare const blueGrey: {
    base: string;
    lighten5: string;
    lighten4: string;
    lighten3: string;
    lighten2: string;
    lighten1: string;
    darken1: string;
    darken2: string;
    darken3: string;
    darken4: string;
};
export declare const grey: {
    base: string;
    lighten5: string;
    lighten4: string;
    lighten3: string;
    lighten2: string;
    lighten1: string;
    darken1: string;
    darken2: string;
    darken3: string;
    darken4: string;
};
export declare const shades: {
    black: string;
    white: string;
    transparent: string;
};
declare const _default: {
    red: {
        base: string;
        lighten5: string;
        lighten4: string;
        lighten3: string;
        lighten2: string;
        lighten1: string;
        darken1: string;
        darken2: string;
        darken3: string;
        darken4: string;
        accent1: string;
        accent2: string;
        accent3: string;
        accent4: string;
    };
    pink: {
        base: string;
        lighten5: string;
        lighten4: string;
        lighten3: string;
        lighten2: string;
        lighten1: string;
        darken1: string;
        darken2: string;
        darken3: string;
        darken4: string;
        accent1: string;
        accent2: string;
        accent3: string;
        accent4: string;
    };
    purple: {
        base: string;
        lighten5: string;
        lighten4: string;
        lighten3: string;
        lighten2: string;
        lighten1: string;
        darken1: string;
        darken2: string;
        darken3: string;
        darken4: string;
        accent1: string;
        accent2: string;
        accent3: string;
        accent4: string;
    };
    deepPurple: {
        base: string;
        lighten5: string;
        lighten4: string;
        lighten3: string;
        lighten2: string;
        lighten1: string;
        darken1: string;
        darken2: string;
        darken3: string;
        darken4: string;
        accent1: string;
        accent2: string;
        accent3: string;
        accent4: string;
    };
    indigo: {
        base: string;
        lighten5: string;
        lighten4: string;
        lighten3: string;
        lighten2: string;
        lighten1: string;
        darken1: string;
        darken2: string;
        darken3: string;
        darken4: string;
        accent1: string;
        accent2: string;
        accent3: string;
        accent4: string;
    };
    blue: {
        base: string;
        lighten5: string;
        lighten4: string;
        lighten3: string;
        lighten2: string;
        lighten1: string;
        darken1: string;
        darken2: string;
        darken3: string;
        darken4: string;
        accent1: string;
        accent2: string;
        accent3: string;
        accent4: string;
    };
    lightBlue: {
        base: string;
        lighten5: string;
        lighten4: string;
        lighten3: string;
        lighten2: string;
        lighten1: string;
        darken1: string;
        darken2: string;
        darken3: string;
        darken4: string;
        accent1: string;
        accent2: string;
        accent3: string;
        accent4: string;
    };
    cyan: {
        base: string;
        lighten5: string;
        lighten4: string;
        lighten3: string;
        lighten2: string;
        lighten1: string;
        darken1: string;
        darken2: string;
        darken3: string;
        darken4: string;
        accent1: string;
        accent2: string;
        accent3: string;
        accent4: string;
    };
    teal: {
        base: string;
        lighten5: string;
        lighten4: string;
        lighten3: string;
        lighten2: string;
        lighten1: string;
        darken1: string;
        darken2: string;
        darken3: string;
        darken4: string;
        accent1: string;
        accent2: string;
        accent3: string;
        accent4: string;
    };
    green: {
        base: string;
        lighten5: string;
        lighten4: string;
        lighten3: string;
        lighten2: string;
        lighten1: string;
        darken1: string;
        darken2: string;
        darken3: string;
        darken4: string;
        accent1: string;
        accent2: string;
        accent3: string;
        accent4: string;
    };
    lightGreen: {
        base: string;
        lighten5: string;
        lighten4: string;
        lighten3: string;
        lighten2: string;
        lighten1: string;
        darken1: string;
        darken2: string;
        darken3: string;
        darken4: string;
        accent1: string;
        accent2: string;
        accent3: string;
        accent4: string;
    };
    lime: {
        base: string;
        lighten5: string;
        lighten4: string;
        lighten3: string;
        lighten2: string;
        lighten1: string;
        darken1: string;
        darken2: string;
        darken3: string;
        darken4: string;
        accent1: string;
        accent2: string;
        accent3: string;
        accent4: string;
    };
    yellow: {
        base: string;
        lighten5: string;
        lighten4: string;
        lighten3: string;
        lighten2: string;
        lighten1: string;
        darken1: string;
        darken2: string;
        darken3: string;
        darken4: string;
        accent1: string;
        accent2: string;
        accent3: string;
        accent4: string;
    };
    amber: {
        base: string;
        lighten5: string;
        lighten4: string;
        lighten3: string;
        lighten2: string;
        lighten1: string;
        darken1: string;
        darken2: string;
        darken3: string;
        darken4: string;
        accent1: string;
        accent2: string;
        accent3: string;
        accent4: string;
    };
    orange: {
        base: string;
        lighten5: string;
        lighten4: string;
        lighten3: string;
        lighten2: string;
        lighten1: string;
        darken1: string;
        darken2: string;
        darken3: string;
        darken4: string;
        accent1: string;
        accent2: string;
        accent3: string;
        accent4: string;
    };
    deepOrange: {
        base: string;
        lighten5: string;
        lighten4: string;
        lighten3: string;
        lighten2: string;
        lighten1: string;
        darken1: string;
        darken2: string;
        darken3: string;
        darken4: string;
        accent1: string;
        accent2: string;
        accent3: string;
        accent4: string;
    };
    brown: {
        base: string;
        lighten5: string;
        lighten4: string;
        lighten3: string;
        lighten2: string;
        lighten1: string;
        darken1: string;
        darken2: string;
        darken3: string;
        darken4: string;
    };
    blueGrey: {
        base: string;
        lighten5: string;
        lighten4: string;
        lighten3: string;
        lighten2: string;
        lighten1: string;
        darken1: string;
        darken2: string;
        darken3: string;
        darken4: string;
    };
    grey: {
        base: string;
        lighten5: string;
        lighten4: string;
        lighten3: string;
        lighten2: string;
        lighten1: string;
        darken1: string;
        darken2: string;
        darken3: string;
        darken4: string;
    };
    shades: {
        black: string;
        white: string;
        transparent: string;
    };
};
export default _default;
