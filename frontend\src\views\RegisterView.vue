<template>
  <v-container class="fill-height" fluid>
    <v-row align="center" justify="center">
      <v-col cols="12" sm="8" md="6" lg="4">
        <v-card class="elevation-12">
          <v-card-title class="text-center pa-6">
            <h1 class="text-h4">注册爱生网</h1>
          </v-card-title>
          
          <v-card-text>
            <v-form ref="form" v-model="valid" @submit.prevent="handleRegister">
              <v-text-field
                v-model="registerForm.username"
                :rules="usernameRules"
                label="用户名"
                prepend-inner-icon="mdi-account"
                variant="outlined"
                required
              ></v-text-field>

              <v-text-field
                v-model="registerForm.email"
                :rules="emailRules"
                label="邮箱"
                prepend-inner-icon="mdi-email"
                variant="outlined"
                required
              ></v-text-field>

              <v-text-field
                v-model="registerForm.nickname"
                :rules="nicknameRules"
                label="昵称"
                prepend-inner-icon="mdi-account-circle"
                variant="outlined"
                required
              ></v-text-field>

              <v-text-field
                v-model="registerForm.password"
                :rules="passwordRules"
                :type="showPassword ? 'text' : 'password'"
                label="密码"
                prepend-inner-icon="mdi-lock"
                :append-inner-icon="showPassword ? 'mdi-eye' : 'mdi-eye-off'"
                @click:append-inner="showPassword = !showPassword"
                variant="outlined"
                required
              ></v-text-field>

              <v-text-field
                v-model="confirmPassword"
                :rules="confirmPasswordRules"
                :type="showConfirmPassword ? 'text' : 'password'"
                label="确认密码"
                prepend-inner-icon="mdi-lock-check"
                :append-inner-icon="showConfirmPassword ? 'mdi-eye' : 'mdi-eye-off'"
                @click:append-inner="showConfirmPassword = !showConfirmPassword"
                variant="outlined"
                required
              ></v-text-field>

              <v-alert
                v-if="errorMessage"
                type="error"
                class="mb-4"
                dismissible
                @click:close="errorMessage = ''"
              >
                {{ errorMessage }}
              </v-alert>

              <v-btn
                :loading="authStore.loading"
                :disabled="!valid"
                color="primary"
                size="large"
                type="submit"
                block
                class="mb-4"
              >
                注册
              </v-btn>

              <div class="text-center">
                <span class="text-body-2">已有账户？</span>
                <router-link to="/login" class="text-primary text-decoration-none">
                  立即登录
                </router-link>
              </div>
            </v-form>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '../stores/auth'

const router = useRouter()
const authStore = useAuthStore()

const form = ref()
const valid = ref(false)
const showPassword = ref(false)
const showConfirmPassword = ref(false)
const errorMessage = ref('')
const confirmPassword = ref('')

const registerForm = reactive({
  username: '',
  email: '',
  nickname: '',
  password: ''
})

const usernameRules = [
  (v: string) => !!v || '用户名不能为空',
  (v: string) => v.length >= 3 || '用户名至少3个字符',
  (v: string) => v.length <= 50 || '用户名最多50个字符',
  (v: string) => /^[a-zA-Z0-9_]+$/.test(v) || '用户名只能包含字母、数字和下划线'
]

const emailRules = [
  (v: string) => !!v || '邮箱不能为空',
  (v: string) => /.+@.+\..+/.test(v) || '请输入有效的邮箱地址'
]

const nicknameRules = [
  (v: string) => !!v || '昵称不能为空',
  (v: string) => v.length >= 1 || '昵称至少1个字符',
  (v: string) => v.length <= 50 || '昵称最多50个字符'
]

const passwordRules = [
  (v: string) => !!v || '密码不能为空',
  (v: string) => v.length >= 6 || '密码至少6个字符'
]

const confirmPasswordRules = [
  (v: string) => !!v || '请确认密码',
  (v: string) => v === registerForm.password || '两次输入的密码不一致'
]

const handleRegister = async () => {
  if (!valid.value) return

  const result = await authStore.register(registerForm)
  
  if (result.success) {
    // 注册成功，跳转到首页
    router.push('/')
  } else {
    errorMessage.value = result.message || '注册失败'
  }
}
</script>

<style scoped>
.text-decoration-none {
  text-decoration: none;
}
</style>
