import { Box } from "../../util/box.js";
import type { PropType, Ref } from 'vue';
import type { Anchor } from "../../util/index.js";
export interface LocationStrategyData {
    contentEl: Ref<HTMLElement | undefined>;
    target: Ref<HTMLElement | [x: number, y: number] | undefined>;
    isActive: Ref<boolean>;
    isRtl: Ref<boolean>;
}
export type LocationStrategyFunction = (data: LocationStrategyData, props: StrategyProps, contentStyles: Ref<Record<string, string>>) => undefined | {
    updateLocation: (e?: Event) => void;
};
declare const locationStrategies: {
    static: typeof staticLocationStrategy;
    connected: typeof connectedLocationStrategy;
};
export interface StrategyProps {
    locationStrategy: keyof typeof locationStrategies | LocationStrategyFunction;
    location: Anchor;
    origin: Anchor | 'auto' | 'overlap';
    offset?: number | string | number[];
    maxHeight?: number | string;
    maxWidth?: number | string;
    minHeight?: number | string;
    minWidth?: number | string;
}
export declare const makeLocationStrategyProps: <Defaults extends {
    locationStrategy?: unknown;
    location?: unknown;
    origin?: unknown;
    offset?: unknown;
} = {}>(defaults?: Defaults | undefined) => {
    locationStrategy: unknown extends Defaults["locationStrategy"] ? {
        type: PropType<StrategyProps["locationStrategy"]>;
        default: string;
        validator: (val: any) => boolean;
    } : Omit<{
        type: PropType<StrategyProps["locationStrategy"]>;
        default: string;
        validator: (val: any) => boolean;
    }, "type" | "default"> & {
        type: PropType<unknown extends Defaults["locationStrategy"] ? "connected" | "static" | LocationStrategyFunction : "connected" | "static" | LocationStrategyFunction | Defaults["locationStrategy"]>;
        default: unknown extends Defaults["locationStrategy"] ? "connected" | "static" | LocationStrategyFunction : NonNullable<"connected" | "static" | LocationStrategyFunction> | Defaults["locationStrategy"];
    };
    location: unknown extends Defaults["location"] ? {
        type: PropType<StrategyProps["location"]>;
        default: string;
    } : Omit<{
        type: PropType<StrategyProps["location"]>;
        default: string;
    }, "type" | "default"> & {
        type: PropType<unknown extends Defaults["location"] ? Anchor : Anchor | Defaults["location"]>;
        default: unknown extends Defaults["location"] ? Anchor : NonNullable<Anchor> | Defaults["location"];
    };
    origin: unknown extends Defaults["origin"] ? {
        type: PropType<StrategyProps["origin"]>;
        default: string;
    } : Omit<{
        type: PropType<StrategyProps["origin"]>;
        default: string;
    }, "type" | "default"> & {
        type: PropType<unknown extends Defaults["origin"] ? "auto" | Anchor | "overlap" : "auto" | Anchor | "overlap" | Defaults["origin"]>;
        default: unknown extends Defaults["origin"] ? "auto" | Anchor | "overlap" : NonNullable<"auto" | Anchor | "overlap"> | Defaults["origin"];
    };
    offset: unknown extends Defaults["offset"] ? PropType<string | number | number[] | undefined> : {
        type: PropType<unknown extends Defaults["offset"] ? string | number | number[] | undefined : string | number | number[] | Defaults["offset"] | undefined>;
        default: unknown extends Defaults["offset"] ? string | number | number[] | undefined : NonNullable<string | number | number[] | undefined> | Defaults["offset"];
    };
};
export declare function useLocationStrategies(props: StrategyProps, data: LocationStrategyData): {
    contentStyles: Ref<{}, {}>;
    updateLocation: Ref<((e: Event) => void) | undefined, ((e: Event) => void) | undefined>;
};
declare function staticLocationStrategy(): void;
declare function connectedLocationStrategy(data: LocationStrategyData, props: StrategyProps, contentStyles: Ref<Record<string, string>>): {
    updateLocation: () => {
        available: {
            x: number;
            y: number;
        };
        contentBox: Box;
        flipped: {
            x: boolean;
            y: boolean;
        };
    } | undefined;
};

