{"version": 3, "sources": ["../../vuetify/src/directives/intersect/index.ts", "../../vuetify/src/directives/ripple/index.ts", "../../vuetify/src/directives/click-outside/index.ts", "../../vuetify/src/components/VOverlay/VOverlay.tsx", "../../vuetify/src/components/VOverlay/util/point.ts", "../../vuetify/src/components/VOverlay/locationStrategies.ts", "../../vuetify/src/components/VOverlay/requestNewFrame.ts", "../../vuetify/src/components/VOverlay/scrollStrategies.ts", "../../vuetify/src/components/VMenu/shared.ts", "../../vuetify/src/composables/delay.ts", "../../vuetify/src/components/VOverlay/useActivator.tsx", "../../vuetify/src/composables/color.ts", "../../vuetify/src/composables/dimensions.ts", "../../vuetify/src/composables/hydration.ts", "../../vuetify/src/composables/lazy.ts", "../../vuetify/src/composables/router.tsx", "../../vuetify/src/composables/scopeId.ts", "../../vuetify/src/composables/stack.ts", "../../vuetify/src/composables/teleport.ts", "../../vuetify/src/composables/transition.ts", "../../vuetify/src/directives/touch/index.ts", "../../vuetify/src/components/VTooltip/VTooltip.tsx", "../../vuetify/src/composables/forwardRefs.ts"], "sourcesContent": ["// Utilities\nimport { SUPPORTS_INTERSECTION } from '@/util'\n\n// Types\nimport type {\n  DirectiveBinding,\n} from 'vue'\n\ntype ObserveHandler = (\n  isIntersecting: boolean,\n  entries: IntersectionObserverEntry[],\n  observer: IntersectionObserver,\n) => void\n\nexport interface ObserveDirectiveBinding extends Omit<DirectiveBinding, 'modifiers' | 'value'> {\n  value?: ObserveHandler | { handler: ObserveHandler, options?: IntersectionObserverInit }\n  modifiers: {\n    once?: boolean\n    quiet?: boolean\n  }\n}\n\nfunction mounted (el: HTMLElement, binding: ObserveDirectiveBinding) {\n  if (!SUPPORTS_INTERSECTION) return\n\n  const modifiers = binding.modifiers || {}\n  const value = binding.value\n  const { handler, options } = typeof value === 'object'\n    ? value\n    : { handler: value, options: {} }\n\n  const observer = new IntersectionObserver((\n    entries: IntersectionObserverEntry[] = [],\n    observer: IntersectionObserver\n  ) => {\n    const _observe = el._observe?.[binding.instance!.$.uid]\n    if (!_observe) return // Just in case, should never fire\n\n    const isIntersecting = entries.some(entry => entry.isIntersecting)\n\n    // If is not quiet or has already been\n    // initted, invoke the user callback\n    if (\n      handler && (\n        !modifiers.quiet ||\n        _observe.init\n      ) && (\n        !modifiers.once ||\n        isIntersecting ||\n        _observe.init\n      )\n    ) {\n      handler(isIntersecting, entries, observer)\n    }\n\n    if (isIntersecting && modifiers.once) unmounted(el, binding)\n    else _observe.init = true\n  }, options)\n\n  el._observe = Object(el._observe)\n  el._observe![binding.instance!.$.uid] = { init: false, observer }\n\n  observer.observe(el)\n}\n\nfunction unmounted (el: HTMLElement, binding: ObserveDirectiveBinding) {\n  const observe = el._observe?.[binding.instance!.$.uid]\n  if (!observe) return\n\n  observe.observer.unobserve(el)\n  delete el._observe![binding.instance!.$.uid]\n}\n\nexport const Intersect = {\n  mounted,\n  unmounted,\n}\n\nexport default Intersect\n", "// Styles\nimport './VRipple.sass'\n\n// Utilities\nimport { isObject, keyCodes } from '@/util'\n\n// Types\nimport type { DirectiveBinding } from 'vue'\n\nconst stopSymbol = Symbol('rippleStop')\n\ntype VuetifyRippleEvent = (MouseEvent | TouchEvent | KeyboardEvent) & { [stopSymbol]?: boolean }\n\nconst DELAY_RIPPLE = 80\n\nfunction transform (el: HTMLElement, value: string) {\n  el.style.transform = value\n  el.style.webkitTransform = value\n}\n\ninterface RippleOptions {\n  class?: string\n  center?: boolean\n  circle?: boolean\n}\n\nexport interface RippleDirectiveBinding extends Omit<DirectiveBinding, 'modifiers' | 'value'> {\n  value?: boolean | { class: string }\n  modifiers: {\n    center?: boolean\n    circle?: boolean\n    stop?: boolean\n  }\n}\n\nfunction isTouchEvent (e: VuetifyRippleEvent): e is TouchEvent {\n  return e.constructor.name === 'TouchEvent'\n}\n\nfunction isKeyboardEvent (e: VuetifyRippleEvent): e is KeyboardEvent {\n  return e.constructor.name === 'KeyboardEvent'\n}\n\nconst calculate = (\n  e: VuetifyRippleEvent,\n  el: HTMLElement,\n  value: RippleOptions = {}\n) => {\n  let localX = 0\n  let localY = 0\n\n  if (!isKeyboardEvent(e)) {\n    const offset = el.getBoundingClientRect()\n    const target = isTouchEvent(e) ? e.touches[e.touches.length - 1] : e\n\n    localX = target.clientX - offset.left\n    localY = target.clientY - offset.top\n  }\n\n  let radius = 0\n  let scale = 0.3\n  if (el._ripple?.circle) {\n    scale = 0.15\n    radius = el.clientWidth / 2\n    radius = value.center ? radius : radius + Math.sqrt((localX - radius) ** 2 + (localY - radius) ** 2) / 4\n  } else {\n    radius = Math.sqrt(el.clientWidth ** 2 + el.clientHeight ** 2) / 2\n  }\n\n  const centerX = `${(el.clientWidth - (radius * 2)) / 2}px`\n  const centerY = `${(el.clientHeight - (radius * 2)) / 2}px`\n\n  const x = value.center ? centerX : `${localX - radius}px`\n  const y = value.center ? centerY : `${localY - radius}px`\n\n  return { radius, scale, x, y, centerX, centerY }\n}\n\nconst ripples = {\n  /* eslint-disable max-statements */\n  show (\n    e: VuetifyRippleEvent,\n    el: HTMLElement,\n    value: RippleOptions = {}\n  ) {\n    if (!el?._ripple?.enabled) {\n      return\n    }\n\n    const container = document.createElement('span')\n    const animation = document.createElement('span')\n\n    container.appendChild(animation)\n    container.className = 'v-ripple__container'\n\n    if (value.class) {\n      container.className += ` ${value.class}`\n    }\n\n    const { radius, scale, x, y, centerX, centerY } = calculate(e, el, value)\n\n    const size = `${radius * 2}px`\n    animation.className = 'v-ripple__animation'\n    animation.style.width = size\n    animation.style.height = size\n\n    el.appendChild(container)\n\n    const computed = window.getComputedStyle(el)\n    if (computed && computed.position === 'static') {\n      el.style.position = 'relative'\n      el.dataset.previousPosition = 'static'\n    }\n\n    animation.classList.add('v-ripple__animation--enter')\n    animation.classList.add('v-ripple__animation--visible')\n    transform(animation, `translate(${x}, ${y}) scale3d(${scale},${scale},${scale})`)\n    animation.dataset.activated = String(performance.now())\n\n    requestAnimationFrame(() => {\n      requestAnimationFrame(() => {\n        animation.classList.remove('v-ripple__animation--enter')\n        animation.classList.add('v-ripple__animation--in')\n        transform(animation, `translate(${centerX}, ${centerY}) scale3d(1,1,1)`)\n      })\n    })\n  },\n\n  hide (el: HTMLElement | null) {\n    if (!el?._ripple?.enabled) return\n\n    const ripples = el.getElementsByClassName('v-ripple__animation')\n\n    if (ripples.length === 0) return\n    const animation = ripples[ripples.length - 1]\n\n    if (animation.dataset.isHiding) return\n    else animation.dataset.isHiding = 'true'\n\n    const diff = performance.now() - Number(animation.dataset.activated)\n    const delay = Math.max(250 - diff, 0)\n\n    setTimeout(() => {\n      animation.classList.remove('v-ripple__animation--in')\n      animation.classList.add('v-ripple__animation--out')\n\n      setTimeout(() => {\n        const ripples = el.getElementsByClassName('v-ripple__animation')\n        if (ripples.length === 1 && el.dataset.previousPosition) {\n          el.style.position = el.dataset.previousPosition\n          delete el.dataset.previousPosition\n        }\n\n        if (animation.parentNode?.parentNode === el) el.removeChild(animation.parentNode)\n      }, 300)\n    }, delay)\n  },\n}\n\nfunction isRippleEnabled (value: any): value is true {\n  return typeof value === 'undefined' || !!value\n}\n\nfunction rippleShow (e: VuetifyRippleEvent) {\n  const value: RippleOptions = {}\n  const element = e.currentTarget as HTMLElement | undefined\n\n  if (!element?._ripple || element._ripple.touched || e[stopSymbol]) return\n\n  // Don't allow the event to trigger ripples on any other elements\n  e[stopSymbol] = true\n\n  if (isTouchEvent(e)) {\n    element._ripple.touched = true\n    element._ripple.isTouch = true\n  } else {\n    // It's possible for touch events to fire\n    // as mouse events on Android/iOS, this\n    // will skip the event call if it has\n    // already been registered as touch\n    if (element._ripple.isTouch) return\n  }\n\n  value.center = element._ripple.centered || isKeyboardEvent(e)\n  if (element._ripple.class) {\n    value.class = element._ripple.class\n  }\n\n  if (isTouchEvent(e)) {\n    // already queued that shows or hides the ripple\n    if (element._ripple.showTimerCommit) return\n\n    element._ripple.showTimerCommit = () => {\n      ripples.show(e, element, value)\n    }\n    element._ripple.showTimer = window.setTimeout(() => {\n      if (element?._ripple?.showTimerCommit) {\n        element._ripple.showTimerCommit()\n        element._ripple.showTimerCommit = null\n      }\n    }, DELAY_RIPPLE)\n  } else {\n    ripples.show(e, element, value)\n  }\n}\n\nfunction rippleStop (e: VuetifyRippleEvent) {\n  e[stopSymbol] = true\n}\n\nfunction rippleHide (e: Event) {\n  const element = e.currentTarget as HTMLElement | null\n  if (!element?._ripple) return\n\n  window.clearTimeout(element._ripple.showTimer)\n\n  // The touch interaction occurs before the show timer is triggered.\n  // We still want to show ripple effect.\n  if (e.type === 'touchend' && element._ripple.showTimerCommit) {\n    element._ripple.showTimerCommit()\n    element._ripple.showTimerCommit = null\n\n    // re-queue ripple hiding\n    element._ripple.showTimer = window.setTimeout(() => {\n      rippleHide(e)\n    })\n    return\n  }\n\n  window.setTimeout(() => {\n    if (element._ripple) {\n      element._ripple.touched = false\n    }\n  })\n  ripples.hide(element)\n}\n\nfunction rippleCancelShow (e: MouseEvent | TouchEvent) {\n  const element = e.currentTarget as HTMLElement | undefined\n\n  if (!element?._ripple) return\n\n  if (element._ripple.showTimerCommit) {\n    element._ripple.showTimerCommit = null\n  }\n\n  window.clearTimeout(element._ripple.showTimer)\n}\n\nlet keyboardRipple = false\n\nfunction keyboardRippleShow (e: KeyboardEvent) {\n  if (!keyboardRipple && (e.keyCode === keyCodes.enter || e.keyCode === keyCodes.space)) {\n    keyboardRipple = true\n    rippleShow(e)\n  }\n}\n\nfunction keyboardRippleHide (e: KeyboardEvent) {\n  keyboardRipple = false\n  rippleHide(e)\n}\n\nfunction focusRippleHide (e: FocusEvent) {\n  if (keyboardRipple) {\n    keyboardRipple = false\n    rippleHide(e)\n  }\n}\n\nfunction updateRipple (el: HTMLElement, binding: RippleDirectiveBinding, wasEnabled: boolean) {\n  const { value, modifiers } = binding\n  const enabled = isRippleEnabled(value)\n  if (!enabled) {\n    ripples.hide(el)\n  }\n\n  el._ripple = el._ripple ?? {}\n  el._ripple.enabled = enabled\n  el._ripple.centered = modifiers.center\n  el._ripple.circle = modifiers.circle\n  if (isObject(value) && value.class) {\n    el._ripple.class = value.class\n  }\n\n  if (enabled && !wasEnabled) {\n    if (modifiers.stop) {\n      el.addEventListener('touchstart', rippleStop, { passive: true })\n      el.addEventListener('mousedown', rippleStop)\n      return\n    }\n\n    el.addEventListener('touchstart', rippleShow, { passive: true })\n    el.addEventListener('touchend', rippleHide, { passive: true })\n    el.addEventListener('touchmove', rippleCancelShow, { passive: true })\n    el.addEventListener('touchcancel', rippleHide)\n\n    el.addEventListener('mousedown', rippleShow)\n    el.addEventListener('mouseup', rippleHide)\n    el.addEventListener('mouseleave', rippleHide)\n\n    el.addEventListener('keydown', keyboardRippleShow)\n    el.addEventListener('keyup', keyboardRippleHide)\n\n    el.addEventListener('blur', focusRippleHide)\n\n    // Anchor tags can be dragged, causes other hides to fail - #1537\n    el.addEventListener('dragstart', rippleHide, { passive: true })\n  } else if (!enabled && wasEnabled) {\n    removeListeners(el)\n  }\n}\n\nfunction removeListeners (el: HTMLElement) {\n  el.removeEventListener('mousedown', rippleShow)\n  el.removeEventListener('touchstart', rippleShow)\n  el.removeEventListener('touchend', rippleHide)\n  el.removeEventListener('touchmove', rippleCancelShow)\n  el.removeEventListener('touchcancel', rippleHide)\n  el.removeEventListener('mouseup', rippleHide)\n  el.removeEventListener('mouseleave', rippleHide)\n  el.removeEventListener('keydown', keyboardRippleShow)\n  el.removeEventListener('keyup', keyboardRippleHide)\n  el.removeEventListener('dragstart', rippleHide)\n  el.removeEventListener('blur', focusRippleHide)\n}\n\nfunction mounted (el: HTMLElement, binding: RippleDirectiveBinding) {\n  updateRipple(el, binding, false)\n}\n\nfunction unmounted (el: HTMLElement) {\n  delete el._ripple\n  removeListeners(el)\n}\n\nfunction updated (el: HTMLElement, binding: RippleDirectiveBinding) {\n  if (binding.value === binding.oldValue) {\n    return\n  }\n\n  const wasEnabled = isRippleEnabled(binding.oldValue)\n  updateRipple(el, binding, wasEnabled)\n}\n\nexport const Ripple = {\n  mounted,\n  unmounted,\n  updated,\n}\n\nexport default Ripple\n", "// Utilities\nimport { attachedRoot } from '@/util'\n\n// Types\nimport type { DirectiveBinding } from 'vue'\n\ninterface ClickOutsideBindingArgs {\n  handler: (e: MouseEvent) => void\n  closeConditional?: (e: Event) => boolean\n  include?: () => HTMLElement[]\n}\n\ninterface ClickOutsideDirectiveBinding extends DirectiveBinding {\n  value: ((e: MouseEvent) => void) | ClickOutsideBindingArgs\n}\n\nfunction defaultConditional () {\n  return true\n}\n\nfunction checkEvent (e: MouseEvent, el: HTMLElement, binding: ClickOutsideDirectiveBinding): boolean {\n  // The include element callbacks below can be expensive\n  // so we should avoid calling them when we're not active.\n  // Explicitly check for false to allow fallback compatibility\n  // with non-toggleable components\n  if (!e || checkIsActive(e, binding) === false) return false\n\n  // If we're clicking inside the shadowroot, then the app root doesn't get the same\n  // level of introspection as to _what_ we're clicking. We want to check to see if\n  // our target is the shadowroot parent container, and if it is, ignore.\n  const root = attachedRoot(el)\n  if (\n    typeof ShadowRoot !== 'undefined' &&\n    root instanceof ShadowRoot &&\n    root.host === e.target\n  ) return false\n\n  // Check if additional elements were passed to be included in check\n  // (click must be outside all included elements, if any)\n  const elements = ((typeof binding.value === 'object' && binding.value.include) || (() => []))()\n  // Add the root element for the component this directive was defined on\n  elements.push(el)\n\n  // Check if it's a click outside our elements, and then if our callback returns true.\n  // Non-toggleable components should take action in their callback and return falsy.\n  // Toggleable can return true if it wants to deactivate.\n  // Note that, because we're in the capture phase, this callback will occur before\n  // the bubbling click event on any outside elements.\n  return !elements.some(el => el?.contains(e.target as Node))\n}\n\nfunction checkIsActive (e: MouseEvent, binding: ClickOutsideDirectiveBinding): boolean | void {\n  const isActive = (typeof binding.value === 'object' && binding.value.closeConditional) || defaultConditional\n\n  return isActive(e)\n}\n\nfunction directive (e: MouseEvent, el: HTMLElement, binding: ClickOutsideDirectiveBinding) {\n  const handler = typeof binding.value === 'function' ? binding.value : binding.value.handler\n\n  // Clicks in the Shadow DOM change their target while using setTimeout, so the original target is saved here\n  e.shadowTarget = e.target\n\n  el._clickOutside!.lastMousedownWasOutside && checkEvent(e, el, binding) && setTimeout(() => {\n    checkIsActive(e, binding) && handler && handler(e)\n  }, 0)\n}\n\nfunction handleShadow (el: HTMLElement, callback: Function): void {\n  const root = attachedRoot(el)\n\n  callback(document)\n\n  if (typeof ShadowRoot !== 'undefined' && root instanceof ShadowRoot) {\n    callback(root)\n  }\n}\n\nexport const ClickOutside = {\n  // [data-app] may not be found\n  // if using bind, inserted makes\n  // sure that the root element is\n  // available, iOS does not support\n  // clicks on body\n  mounted (el: HTMLElement, binding: ClickOutsideDirectiveBinding) {\n    const onClick = (e: Event) => directive(e as MouseEvent, el, binding)\n    const onMousedown = (e: Event) => {\n      el._clickOutside!.lastMousedownWasOutside = checkEvent(e as MouseEvent, el, binding)\n    }\n\n    handleShadow(el, (app: HTMLElement) => {\n      app.addEventListener('click', onClick, true)\n      app.addEventListener('mousedown', onMousedown, true)\n    })\n    if (!el._clickOutside) {\n      el._clickOutside = {\n        lastMousedownWasOutside: false,\n      }\n    }\n\n    el._clickOutside[binding.instance!.$.uid] = {\n      onClick,\n      onMousedown,\n    }\n  },\n\n  beforeUnmount (el: HTMLElement, binding: ClickOutsideDirectiveBinding) {\n    if (!el._clickOutside) return\n\n    handleShadow(el, (app: HTMLElement) => {\n      if (!app || !el._clickOutside?.[binding.instance!.$.uid]) return\n\n      const { onClick, onMousedown } = el._clickOutside[binding.instance!.$.uid]!\n\n      app.removeEventListener('click', onClick, true)\n      app.removeEventListener('mousedown', onMousedown, true)\n    })\n\n    delete el._clickOutside[binding.instance!.$.uid]\n  },\n}\n\nexport default ClickOutside\n", "// Styles\nimport './VOverlay.sass'\n\n// Composables\nimport { makeLocationStrategyProps, useLocationStrategies } from './locationStrategies'\nimport { makeScrollStrategyProps, useScrollStrategies } from './scrollStrategies'\nimport { makeActivatorProps, useActivator } from './useActivator'\nimport { useBackgroundColor } from '@/composables/color'\nimport { makeComponentProps } from '@/composables/component'\nimport { makeDimensionProps, useDimension } from '@/composables/dimensions'\nimport { useHydration } from '@/composables/hydration'\nimport { makeLazyProps, useLazy } from '@/composables/lazy'\nimport { useRtl } from '@/composables/locale'\nimport { useProxiedModel } from '@/composables/proxiedModel'\nimport { useBackButton, useRouter } from '@/composables/router'\nimport { useScopeId } from '@/composables/scopeId'\nimport { useStack } from '@/composables/stack'\nimport { useTeleport } from '@/composables/teleport'\nimport { makeThemeProps, provideTheme } from '@/composables/theme'\nimport { useToggleScope } from '@/composables/toggleScope'\nimport { makeTransitionProps, MaybeTransition } from '@/composables/transition'\n\n// Directives\nimport vClickOutside from '@/directives/click-outside'\n\n// Utilities\nimport {\n  computed,\n  mergeProps,\n  onBeforeUnmount,\n  ref,\n  Teleport,\n  Transition,\n  watch,\n} from 'vue'\nimport {\n  animate,\n  convertToUnit,\n  genericComponent,\n  getCurrentInstance,\n  getScrollParent,\n  IN_BROWSER,\n  propsFactory,\n  standardEasing,\n  useRender,\n} from '@/util'\n\n// Types\nimport type { PropType, Ref } from 'vue'\nimport type { BackgroundColorData } from '@/composables/color'\nimport type { TemplateRef } from '@/util'\n\ninterface ScrimProps {\n  [key: string]: unknown\n  modelValue: boolean\n  color: BackgroundColorData\n}\nfunction Scrim (props: ScrimProps) {\n  const { modelValue, color, ...rest } = props\n  return (\n    <Transition name=\"fade-transition\" appear>\n      { props.modelValue && (\n        <div\n          class={[\n            'v-overlay__scrim',\n            props.color.backgroundColorClasses.value,\n          ]}\n          style={ props.color.backgroundColorStyles.value }\n          { ...rest }\n        />\n      )}\n    </Transition>\n  )\n}\n\nexport type OverlaySlots = {\n  default: { isActive: Ref<boolean> }\n  activator: { isActive: boolean, props: Record<string, any>, targetRef: TemplateRef }\n}\n\nexport const makeVOverlayProps = propsFactory({\n  absolute: Boolean,\n  attach: [Boolean, String, Object] as PropType<boolean | string | Element>,\n  closeOnBack: {\n    type: Boolean,\n    default: true,\n  },\n  contained: Boolean,\n  contentClass: null,\n  contentProps: null,\n  disabled: Boolean,\n  opacity: [Number, String],\n  noClickAnimation: Boolean,\n  modelValue: Boolean,\n  persistent: Boolean,\n  scrim: {\n    type: [Boolean, String],\n    default: true,\n  },\n  zIndex: {\n    type: [Number, String],\n    default: 2000,\n  },\n\n  ...makeActivatorProps(),\n  ...makeComponentProps(),\n  ...makeDimensionProps(),\n  ...makeLazyProps(),\n  ...makeLocationStrategyProps(),\n  ...makeScrollStrategyProps(),\n  ...makeThemeProps(),\n  ...makeTransitionProps(),\n}, 'VOverlay')\n\nexport const VOverlay = genericComponent<OverlaySlots>()({\n  name: 'VOverlay',\n\n  directives: { vClickOutside },\n\n  inheritAttrs: false,\n\n  props: {\n    _disableGlobalStack: Boolean,\n\n    ...makeVOverlayProps(),\n  },\n\n  emits: {\n    'click:outside': (e: MouseEvent) => true,\n    'update:modelValue': (value: boolean) => true,\n    keydown: (e: KeyboardEvent) => true,\n    afterEnter: () => true,\n    afterLeave: () => true,\n  },\n\n  setup (props, { slots, attrs, emit }) {\n    const vm = getCurrentInstance('VOverlay')\n    const root = ref<HTMLElement>()\n    const scrimEl = ref<HTMLElement>()\n    const contentEl = ref<HTMLElement>()\n    const model = useProxiedModel(props, 'modelValue')\n    const isActive = computed({\n      get: () => model.value,\n      set: v => {\n        if (!(v && props.disabled)) model.value = v\n      },\n    })\n    const { themeClasses } = provideTheme(props)\n    const { rtlClasses, isRtl } = useRtl()\n    const { hasContent, onAfterLeave: _onAfterLeave } = useLazy(props, isActive)\n    const scrimColor = useBackgroundColor(() => {\n      return typeof props.scrim === 'string' ? props.scrim : null\n    })\n    const { globalTop, localTop, stackStyles } = useStack(isActive, () => props.zIndex, props._disableGlobalStack)\n    const {\n      activatorEl, activatorRef,\n      target, targetEl, targetRef,\n      activatorEvents,\n      contentEvents,\n      scrimEvents,\n    } = useActivator(props, { isActive, isTop: localTop, contentEl })\n    const { teleportTarget } = useTeleport(() => {\n      const target = props.attach || props.contained\n      if (target) return target\n      const rootNode = activatorEl?.value?.getRootNode() || vm.proxy?.$el?.getRootNode()\n      if (rootNode instanceof ShadowRoot) return rootNode\n      return false\n    })\n    const { dimensionStyles } = useDimension(props)\n    const isMounted = useHydration()\n    const { scopeId } = useScopeId()\n\n    watch(() => props.disabled, v => {\n      if (v) isActive.value = false\n    })\n\n    const { contentStyles, updateLocation } = useLocationStrategies(props, {\n      isRtl,\n      contentEl,\n      target,\n      isActive,\n    })\n    useScrollStrategies(props, {\n      root,\n      contentEl,\n      targetEl,\n      isActive,\n      updateLocation,\n    })\n\n    function onClickOutside (e: MouseEvent) {\n      emit('click:outside', e)\n\n      if (!props.persistent) isActive.value = false\n      else animateClick()\n    }\n\n    function closeConditional (e: Event) {\n      return isActive.value && globalTop.value && (\n        // If using scrim, only close if clicking on it rather than anything opened on top\n        !props.scrim || e.target === scrimEl.value || (e instanceof MouseEvent && e.shadowTarget === scrimEl.value)\n      )\n    }\n\n    IN_BROWSER && watch(isActive, val => {\n      if (val) {\n        window.addEventListener('keydown', onKeydown)\n      } else {\n        window.removeEventListener('keydown', onKeydown)\n      }\n    }, { immediate: true })\n\n    onBeforeUnmount(() => {\n      if (!IN_BROWSER) return\n\n      window.removeEventListener('keydown', onKeydown)\n    })\n\n    function onKeydown (e: KeyboardEvent) {\n      if (e.key === 'Escape' && globalTop.value) {\n        if (!contentEl.value?.contains(document.activeElement)) {\n          emit('keydown', e)\n        }\n        if (!props.persistent) {\n          isActive.value = false\n          if (contentEl.value?.contains(document.activeElement)) {\n            activatorEl.value?.focus()\n          }\n        } else animateClick()\n      }\n    }\n    function onKeydownSelf (e: KeyboardEvent) {\n      if (e.key === 'Escape' && !globalTop.value) return\n\n      emit('keydown', e)\n    }\n\n    const router = useRouter()\n    useToggleScope(() => props.closeOnBack, () => {\n      useBackButton(router, next => {\n        if (globalTop.value && isActive.value) {\n          next(false)\n          if (!props.persistent) isActive.value = false\n          else animateClick()\n        } else {\n          next()\n        }\n      })\n    })\n\n    const top = ref<number>()\n    watch(() => isActive.value && (props.absolute || props.contained) && teleportTarget.value == null, val => {\n      if (val) {\n        const scrollParent = getScrollParent(root.value)\n        if (scrollParent && scrollParent !== document.scrollingElement) {\n          top.value = scrollParent.scrollTop\n        }\n      }\n    })\n\n    // Add a quick \"bounce\" animation to the content\n    function animateClick () {\n      if (props.noClickAnimation) return\n\n      contentEl.value && animate(contentEl.value, [\n        { transformOrigin: 'center' },\n        { transform: 'scale(1.03)' },\n        { transformOrigin: 'center' },\n      ], {\n        duration: 150,\n        easing: standardEasing,\n      })\n    }\n\n    function onAfterEnter () {\n      emit('afterEnter')\n    }\n\n    function onAfterLeave () {\n      _onAfterLeave()\n      emit('afterLeave')\n    }\n\n    useRender(() => (\n      <>\n        { slots.activator?.({\n          isActive: isActive.value,\n          targetRef,\n          props: mergeProps({\n            ref: activatorRef,\n          }, activatorEvents.value, props.activatorProps),\n        })}\n\n        { isMounted.value && hasContent.value && (\n          <Teleport\n            disabled={ !teleportTarget.value }\n            to={ teleportTarget.value }\n          >\n            <div\n              class={[\n                'v-overlay',\n                {\n                  'v-overlay--absolute': props.absolute || props.contained,\n                  'v-overlay--active': isActive.value,\n                  'v-overlay--contained': props.contained,\n                },\n                themeClasses.value,\n                rtlClasses.value,\n                props.class,\n              ]}\n              style={[\n                stackStyles.value,\n                {\n                  '--v-overlay-opacity': props.opacity,\n                  top: convertToUnit(top.value),\n                },\n                props.style,\n              ]}\n              ref={ root }\n              onKeydown={ onKeydownSelf }\n              { ...scopeId }\n              { ...attrs }\n            >\n              <Scrim\n                color={ scrimColor }\n                modelValue={ isActive.value && !!props.scrim }\n                ref={ scrimEl }\n                { ...scrimEvents.value }\n              />\n              <MaybeTransition\n                appear\n                persisted\n                transition={ props.transition }\n                target={ target.value }\n                onAfterEnter={ onAfterEnter }\n                onAfterLeave={ onAfterLeave }\n              >\n                <div\n                  ref={ contentEl }\n                  v-show={ isActive.value }\n                  v-click-outside={{ handler: onClickOutside, closeConditional, include: () => [activatorEl.value] }}\n                  class={[\n                    'v-overlay__content',\n                    props.contentClass,\n                  ]}\n                  style={[\n                    dimensionStyles.value,\n                    contentStyles.value,\n                  ]}\n                  { ...contentEvents.value }\n                  { ...props.contentProps }\n                >\n                  { slots.default?.({ isActive }) }\n                </div>\n              </MaybeTransition>\n            </div>\n          </Teleport>\n        )}\n      </>\n    ))\n\n    return {\n      activatorEl,\n      scrimEl,\n      target,\n      animateClick,\n      contentEl,\n      globalTop,\n      localTop,\n      updateLocation,\n    }\n  },\n})\n\nexport type VOverlay = InstanceType<typeof VOverlay>\n", "// Types\nimport type { ParsedAnchor } from '@/util'\nimport type { Box } from '@/util/box'\n\ntype Point = { x: number, y: number }\ndeclare class As<T extends string> {\n  private as: T\n}\ntype ElementPoint = Point & As<'element'>\ntype ViewportPoint = Point & As<'viewport'>\ntype Offset = Point & As<'offset'>\n\n/** Convert a point in local space to viewport space */\nexport function elementToViewport (point: ElementPoint, offset: Offset | Box) {\n  return {\n    x: point.x + offset.x,\n    y: point.y + offset.y,\n  } as ViewportPoint\n}\n\n/** Convert a point in viewport space to local space */\nexport function viewportToElement (point: ViewportPoint, offset: Offset | Box) {\n  return {\n    x: point.x - offset.x,\n    y: point.y - offset.y,\n  } as ElementPoint\n}\n\n/** Get the difference between two points */\nexport function getOffset<T extends Point> (a: T, b: T) {\n  return {\n    x: a.x - b.x,\n    y: a.y - b.y,\n  } as Offset\n}\n\n/** Convert an anchor object to a point in local space */\nexport function anchorToPoint (anchor: ParsedAnchor, box: Box): ViewportPoint {\n  if (anchor.side === 'top' || anchor.side === 'bottom') {\n    const { side, align } = anchor\n\n    const x: number =\n      align === 'left' ? 0\n      : align === 'center' ? box.width / 2\n      : align === 'right' ? box.width\n      : align\n    const y: number =\n      side === 'top' ? 0\n      : side === 'bottom' ? box.height\n      : side\n\n    return elementToViewport({ x, y } as ElementPoint, box)\n  } else if (anchor.side === 'left' || anchor.side === 'right') {\n    const { side, align } = anchor\n\n    const x: number =\n      side === 'left' ? 0\n      : side === 'right' ? box.width\n      : side\n    const y: number =\n      align === 'top' ? 0\n      : align === 'center' ? box.height / 2\n      : align === 'bottom' ? box.height\n      : align\n\n    return elementToViewport({ x, y } as ElementPoint, box)\n  }\n\n  return elementToViewport({\n    x: box.width / 2,\n    y: box.height / 2,\n  } as ElementPoint, box)\n}\n", "// Composables\nimport { useToggleScope } from '@/composables/toggleScope'\n\n// Utilities\nimport { computed, nextTick, onScopeDispose, ref, watch } from 'vue'\nimport { anchorToPoint, getOffset } from './util/point'\nimport {\n  CircularBuffer,\n  clamp,\n  consoleError,\n  convertToUnit,\n  deepEqual,\n  destructComputed,\n  flipAlign,\n  flipCorner,\n  flipSide,\n  getAxis,\n  getScrollParents,\n  IN_BROWSER,\n  isFixedPosition,\n  nullifyTransforms,\n  parseAnchor,\n  propsFactory,\n} from '@/util'\nimport { Box, getElementBox, getOverflow, getTargetBox } from '@/util/box'\n\n// Types\nimport type { PropType, Ref } from 'vue'\nimport type { Anchor } from '@/util'\n\nexport interface LocationStrategyData {\n  contentEl: Ref<HTMLElement | undefined>\n  target: Ref<HTMLElement | [x: number, y: number] | undefined>\n  isActive: Ref<boolean>\n  isRtl: Ref<boolean>\n}\n\nexport type LocationStrategyFunction = (\n  data: LocationStrategyData,\n  props: StrategyProps,\n  contentStyles: Ref<Record<string, string>>\n) => undefined | { updateLocation: (e?: Event) => void }\n\nconst locationStrategies = {\n  static: staticLocationStrategy, // specific viewport position, usually centered\n  connected: connectedLocationStrategy, // connected to a certain element\n}\n\nexport interface StrategyProps {\n  locationStrategy: keyof typeof locationStrategies | LocationStrategyFunction\n  location: Anchor\n  origin: Anchor | 'auto' | 'overlap'\n  offset?: number | string | number[]\n  maxHeight?: number | string\n  maxWidth?: number | string\n  minHeight?: number | string\n  minWidth?: number | string\n}\n\nexport const makeLocationStrategyProps = propsFactory({\n  locationStrategy: {\n    type: [String, Function] as PropType<StrategyProps['locationStrategy']>,\n    default: 'static',\n    validator: (val: any) => typeof val === 'function' || val in locationStrategies,\n  },\n  location: {\n    type: String as PropType<StrategyProps['location']>,\n    default: 'bottom',\n  },\n  origin: {\n    type: String as PropType<StrategyProps['origin']>,\n    default: 'auto',\n  },\n  offset: [Number, String, Array] as PropType<StrategyProps['offset']>,\n}, 'VOverlay-location-strategies')\n\nexport function useLocationStrategies (\n  props: StrategyProps,\n  data: LocationStrategyData\n) {\n  const contentStyles = ref({})\n  const updateLocation = ref<(e: Event) => void>()\n\n  if (IN_BROWSER) {\n    useToggleScope(() => !!(data.isActive.value && props.locationStrategy), reset => {\n      watch(() => props.locationStrategy, reset)\n      onScopeDispose(() => {\n        window.removeEventListener('resize', onResize)\n        visualViewport?.removeEventListener('resize', onVisualResize)\n        visualViewport?.removeEventListener('scroll', onVisualScroll)\n        updateLocation.value = undefined\n      })\n\n      window.addEventListener('resize', onResize, { passive: true })\n      visualViewport?.addEventListener('resize', onVisualResize, { passive: true })\n      visualViewport?.addEventListener('scroll', onVisualScroll, { passive: true })\n\n      if (typeof props.locationStrategy === 'function') {\n        updateLocation.value = props.locationStrategy(data, props, contentStyles)?.updateLocation\n      } else {\n        updateLocation.value = locationStrategies[props.locationStrategy](data, props, contentStyles)?.updateLocation\n      }\n    })\n  }\n\n  function onResize (e: Event) {\n    updateLocation.value?.(e)\n  }\n\n  function onVisualResize (e: Event) {\n    updateLocation.value?.(e)\n  }\n\n  function onVisualScroll (e: Event) {\n    updateLocation.value?.(e)\n  }\n\n  return {\n    contentStyles,\n    updateLocation,\n  }\n}\n\nfunction staticLocationStrategy () {\n  // TODO\n}\n\n/** Get size of element ignoring max-width/max-height */\nfunction getIntrinsicSize (el: HTMLElement, isRtl: boolean) {\n  // const scrollables = new Map<Element, [number, number]>()\n  // el.querySelectorAll('*').forEach(el => {\n  //   const x = el.scrollLeft\n  //   const y = el.scrollTop\n  //   if (x || y) {\n  //     scrollables.set(el, [x, y])\n  //   }\n  // })\n\n  // const initialMaxWidth = el.style.maxWidth\n  // const initialMaxHeight = el.style.maxHeight\n  // el.style.removeProperty('max-width')\n  // el.style.removeProperty('max-height')\n\n  /* eslint-disable-next-line sonarjs/prefer-immediate-return */\n  const contentBox = nullifyTransforms(el)\n\n  if (isRtl) {\n    contentBox.x += parseFloat(el.style.right || 0)\n  } else {\n    contentBox.x -= parseFloat(el.style.left || 0)\n  }\n  contentBox.y -= parseFloat(el.style.top || 0)\n\n  // el.style.maxWidth = initialMaxWidth\n  // el.style.maxHeight = initialMaxHeight\n  // scrollables.forEach((position, el) => {\n  //   el.scrollTo(...position)\n  // })\n\n  return contentBox\n}\n\nfunction connectedLocationStrategy (data: LocationStrategyData, props: StrategyProps, contentStyles: Ref<Record<string, string>>) {\n  const activatorFixed = Array.isArray(data.target.value) || isFixedPosition(data.target.value)\n  if (activatorFixed) {\n    Object.assign(contentStyles.value, {\n      position: 'fixed',\n      top: 0,\n      [data.isRtl.value ? 'right' : 'left']: 0,\n    })\n  }\n\n  const { preferredAnchor, preferredOrigin } = destructComputed(() => {\n    const parsedAnchor = parseAnchor(props.location, data.isRtl.value)\n    const parsedOrigin =\n      props.origin === 'overlap' ? parsedAnchor\n      : props.origin === 'auto' ? flipSide(parsedAnchor)\n      : parseAnchor(props.origin, data.isRtl.value)\n\n    // Some combinations of props may produce an invalid origin\n    if (parsedAnchor.side === parsedOrigin.side && parsedAnchor.align === flipAlign(parsedOrigin).align) {\n      return {\n        preferredAnchor: flipCorner(parsedAnchor),\n        preferredOrigin: flipCorner(parsedOrigin),\n      }\n    } else {\n      return {\n        preferredAnchor: parsedAnchor,\n        preferredOrigin: parsedOrigin,\n      }\n    }\n  })\n\n  const [minWidth, minHeight, maxWidth, maxHeight] =\n    (['minWidth', 'minHeight', 'maxWidth', 'maxHeight'] as const).map(key => {\n      return computed(() => {\n        const val = parseFloat(props[key]!)\n        return isNaN(val) ? Infinity : val\n      })\n    })\n\n  const offset = computed(() => {\n    if (Array.isArray(props.offset)) {\n      return props.offset\n    }\n    if (typeof props.offset === 'string') {\n      const offset = props.offset.split(' ').map(parseFloat)\n      if (offset.length < 2) offset.push(0)\n      return offset\n    }\n    return typeof props.offset === 'number' ? [props.offset, 0] : [0, 0]\n  })\n\n  let observe = false\n  let lastFrame = -1\n  const flipped = new CircularBuffer<{ x: boolean, y: boolean }>(4)\n  const observer = new ResizeObserver(() => {\n    if (!observe) return\n\n    // Detect consecutive frames\n    requestAnimationFrame(newTime => {\n      if (newTime !== lastFrame) flipped.clear()\n      requestAnimationFrame(newNewTime => {\n        lastFrame = newNewTime\n      })\n    })\n\n    if (flipped.isFull) {\n      const values = flipped.values()\n      if (deepEqual(values.at(-1), values.at(-3))) {\n        // Flipping is causing a container resize loop\n        return\n      }\n    }\n\n    const result = updateLocation()\n    if (result) flipped.push(result.flipped)\n  })\n\n  watch([data.target, data.contentEl], ([newTarget, newContentEl], [oldTarget, oldContentEl]) => {\n    if (oldTarget && !Array.isArray(oldTarget)) observer.unobserve(oldTarget)\n    if (newTarget && !Array.isArray(newTarget)) observer.observe(newTarget)\n\n    if (oldContentEl) observer.unobserve(oldContentEl)\n    if (newContentEl) observer.observe(newContentEl)\n  }, {\n    immediate: true,\n  })\n\n  onScopeDispose(() => {\n    observer.disconnect()\n  })\n\n  let targetBox = new Box({ x: 0, y: 0, width: 0, height: 0 })\n\n  // eslint-disable-next-line max-statements\n  function updateLocation () {\n    observe = false\n    requestAnimationFrame(() => observe = true)\n\n    if (!data.target.value || !data.contentEl.value) return\n\n    if (\n      Array.isArray(data.target.value) ||\n      data.target.value.offsetParent ||\n      data.target.value.getClientRects().length\n    ) {\n      targetBox = getTargetBox(data.target.value)\n    } // Otherwise target element is hidden, use last known value\n\n    const contentBox = getIntrinsicSize(data.contentEl.value, data.isRtl.value)\n    const scrollParents = getScrollParents(data.contentEl.value)\n    const viewportMargin = 12\n\n    if (!scrollParents.length) {\n      scrollParents.push(document.documentElement)\n      if (!(data.contentEl.value.style.top && data.contentEl.value.style.left)) {\n        contentBox.x -= parseFloat(document.documentElement.style.getPropertyValue('--v-body-scroll-x') || 0)\n        contentBox.y -= parseFloat(document.documentElement.style.getPropertyValue('--v-body-scroll-y') || 0)\n      }\n    }\n\n    const viewport = scrollParents.reduce<Box>((box: Box | undefined, el) => {\n      const scrollBox = getElementBox(el)\n\n      if (box) {\n        return new Box({\n          x: Math.max(box.left, scrollBox.left),\n          y: Math.max(box.top, scrollBox.top),\n          width: Math.min(box.right, scrollBox.right) - Math.max(box.left, scrollBox.left),\n          height: Math.min(box.bottom, scrollBox.bottom) - Math.max(box.top, scrollBox.top),\n        })\n      }\n      return scrollBox\n    }, undefined!)\n    viewport.x += viewportMargin\n    viewport.y += viewportMargin\n    viewport.width -= viewportMargin * 2\n    viewport.height -= viewportMargin * 2\n\n    let placement = {\n      anchor: preferredAnchor.value,\n      origin: preferredOrigin.value,\n    }\n\n    function checkOverflow (_placement: typeof placement) {\n      const box = new Box(contentBox)\n      const targetPoint = anchorToPoint(_placement.anchor, targetBox)\n      const contentPoint = anchorToPoint(_placement.origin, box)\n\n      let { x, y } = getOffset(targetPoint, contentPoint)\n\n      switch (_placement.anchor.side) {\n        case 'top': y -= offset.value[0]; break\n        case 'bottom': y += offset.value[0]; break\n        case 'left': x -= offset.value[0]; break\n        case 'right': x += offset.value[0]; break\n      }\n\n      switch (_placement.anchor.align) {\n        case 'top': y -= offset.value[1]; break\n        case 'bottom': y += offset.value[1]; break\n        case 'left': x -= offset.value[1]; break\n        case 'right': x += offset.value[1]; break\n      }\n\n      box.x += x\n      box.y += y\n\n      box.width = Math.min(box.width, maxWidth.value)\n      box.height = Math.min(box.height, maxHeight.value)\n\n      const overflows = getOverflow(box, viewport)\n\n      return { overflows, x, y }\n    }\n\n    let x = 0; let y = 0\n    const available = { x: 0, y: 0 }\n    const flipped = { x: false, y: false }\n    let resets = -1\n    while (true) {\n      if (resets++ > 10) {\n        consoleError('Infinite loop detected in connectedLocationStrategy')\n        break\n      }\n\n      const { x: _x, y: _y, overflows } = checkOverflow(placement)\n\n      x += _x\n      y += _y\n\n      contentBox.x += _x\n      contentBox.y += _y\n\n      // flip\n      {\n        const axis = getAxis(placement.anchor)\n        const hasOverflowX = overflows.x.before || overflows.x.after\n        const hasOverflowY = overflows.y.before || overflows.y.after\n\n        let reset = false\n        ;['x', 'y'].forEach(key => {\n          if (\n            (key === 'x' && hasOverflowX && !flipped.x) ||\n            (key === 'y' && hasOverflowY && !flipped.y)\n          ) {\n            const newPlacement = { anchor: { ...placement.anchor }, origin: { ...placement.origin } }\n            const flip = key === 'x'\n              ? axis === 'y' ? flipAlign : flipSide\n              : axis === 'y' ? flipSide : flipAlign\n            newPlacement.anchor = flip(newPlacement.anchor)\n            newPlacement.origin = flip(newPlacement.origin)\n            const { overflows: newOverflows } = checkOverflow(newPlacement)\n            if (\n              (newOverflows[key].before <= overflows[key].before &&\n                newOverflows[key].after <= overflows[key].after) ||\n              (newOverflows[key].before + newOverflows[key].after <\n                (overflows[key].before + overflows[key].after) / 2)\n            ) {\n              placement = newPlacement\n              reset = flipped[key] = true\n            }\n          }\n        })\n        if (reset) continue\n      }\n\n      // shift\n      if (overflows.x.before) {\n        x += overflows.x.before\n        contentBox.x += overflows.x.before\n      }\n      if (overflows.x.after) {\n        x -= overflows.x.after\n        contentBox.x -= overflows.x.after\n      }\n      if (overflows.y.before) {\n        y += overflows.y.before\n        contentBox.y += overflows.y.before\n      }\n      if (overflows.y.after) {\n        y -= overflows.y.after\n        contentBox.y -= overflows.y.after\n      }\n\n      // size\n      {\n        const overflows = getOverflow(contentBox, viewport)\n        available.x = viewport.width - overflows.x.before - overflows.x.after\n        available.y = viewport.height - overflows.y.before - overflows.y.after\n\n        x += overflows.x.before\n        contentBox.x += overflows.x.before\n        y += overflows.y.before\n        contentBox.y += overflows.y.before\n      }\n\n      break\n    }\n\n    const axis = getAxis(placement.anchor)\n\n    Object.assign(contentStyles.value, {\n      '--v-overlay-anchor-origin': `${placement.anchor.side} ${placement.anchor.align}`,\n      transformOrigin: `${placement.origin.side} ${placement.origin.align}`,\n      // transform: `translate(${pixelRound(x)}px, ${pixelRound(y)}px)`,\n      top: convertToUnit(pixelRound(y)),\n      left: data.isRtl.value ? undefined : convertToUnit(pixelRound(x)),\n      right: data.isRtl.value ? convertToUnit(pixelRound(-x)) : undefined,\n      minWidth: convertToUnit(axis === 'y' ? Math.min(minWidth.value, targetBox.width) : minWidth.value),\n      maxWidth: convertToUnit(pixelCeil(clamp(available.x, minWidth.value === Infinity ? 0 : minWidth.value, maxWidth.value))),\n      maxHeight: convertToUnit(pixelCeil(clamp(available.y, minHeight.value === Infinity ? 0 : minHeight.value, maxHeight.value))),\n    })\n\n    return {\n      available,\n      contentBox,\n      flipped,\n    }\n  }\n\n  watch(\n    () => [\n      preferredAnchor.value,\n      preferredOrigin.value,\n      props.offset,\n      props.minWidth,\n      props.minHeight,\n      props.maxWidth,\n      props.maxHeight,\n    ],\n    () => updateLocation(),\n  )\n\n  nextTick(() => {\n    const result = updateLocation()\n\n    // TODO: overflowing content should only require a single updateLocation call\n    // Icky hack to make sure the content is positioned consistently\n    if (!result) return\n    const { available, contentBox } = result\n    if (contentBox.height > available.y) {\n      requestAnimationFrame(() => {\n        updateLocation()\n        requestAnimationFrame(() => {\n          updateLocation()\n        })\n      })\n    }\n  })\n\n  return { updateLocation }\n}\n\nfunction pixelRound (val: number) {\n  return Math.round(val * devicePixelRatio) / devicePixelRatio\n}\n\nfunction pixelCeil (val: number) {\n  return Math.ceil(val * devicePixelRatio) / devicePixelRatio\n}\n", "let clean = true\nconst frames = [] as any[]\n\n/**\n * Schedule a task to run in an animation frame on its own\n * This is useful for heavy tasks that may cause jank if all ran together\n */\nexport function requestNewFrame (cb: () => void) {\n  if (!clean || frames.length) {\n    frames.push(cb)\n    run()\n  } else {\n    clean = false\n    cb()\n    run()\n  }\n}\n\nlet raf = -1\nfunction run () {\n  cancelAnimationFrame(raf)\n  raf = requestAnimationFrame(() => {\n    const frame = frames.shift()\n    if (frame) frame()\n\n    if (frames.length) run()\n    else clean = true\n  })\n}\n", "// Utilities\nimport { effectScope, onScopeDispose, watchEffect } from 'vue'\nimport { requestNewFrame } from './requestNewFrame'\nimport { convertToUnit, getScrollParents, hasScrollbar, IN_BROWSER, propsFactory } from '@/util'\n\n// Types\nimport type { EffectScope, PropType, Ref } from 'vue'\n\nexport interface ScrollStrategyData {\n  root: Ref<HTMLElement | undefined>\n  contentEl: Ref<HTMLElement | undefined>\n  targetEl: Ref<HTMLElement | undefined>\n  isActive: Ref<boolean>\n  updateLocation: Ref<((e: Event) => void) | undefined>\n}\n\nexport type ScrollStrategyFunction = (data: ScrollStrategyData, props: StrategyProps, scope: EffectScope) => void\n\nconst scrollStrategies = {\n  none: null,\n  close: closeScrollStrategy,\n  block: blockScrollStrategy,\n  reposition: repositionScrollStrategy,\n}\n\nexport interface StrategyProps {\n  scrollStrategy: keyof typeof scrollStrategies | ScrollStrategyFunction\n  contained: boolean | undefined\n}\n\nexport const makeScrollStrategyProps = propsFactory({\n  scrollStrategy: {\n    type: [String, Function] as PropType<StrategyProps['scrollStrategy']>,\n    default: 'block',\n    validator: (val: any) => typeof val === 'function' || val in scrollStrategies,\n  },\n}, 'VOverlay-scroll-strategies')\n\nexport function useScrollStrategies (\n  props: StrategyProps,\n  data: ScrollStrategyData\n) {\n  if (!IN_BROWSER) return\n\n  let scope: EffectScope | undefined\n  watchEffect(async () => {\n    scope?.stop()\n\n    if (!(data.isActive.value && props.scrollStrategy)) return\n\n    scope = effectScope()\n    await new Promise(resolve => setTimeout(resolve))\n    scope.active && scope.run(() => {\n      if (typeof props.scrollStrategy === 'function') {\n        props.scrollStrategy(data, props, scope!)\n      } else {\n        scrollStrategies[props.scrollStrategy]?.(data, props, scope!)\n      }\n    })\n  })\n\n  onScopeDispose(() => {\n    scope?.stop()\n  })\n}\n\nfunction closeScrollStrategy (data: ScrollStrategyData) {\n  function onScroll (e: Event) {\n    data.isActive.value = false\n  }\n\n  bindScroll(data.targetEl.value ?? data.contentEl.value, onScroll)\n}\n\nfunction blockScrollStrategy (data: ScrollStrategyData, props: StrategyProps) {\n  const offsetParent = data.root.value?.offsetParent\n  const scrollElements = [...new Set([\n    ...getScrollParents(data.targetEl.value, props.contained ? offsetParent : undefined),\n    ...getScrollParents(data.contentEl.value, props.contained ? offsetParent : undefined),\n  ])].filter(el => !el.classList.contains('v-overlay-scroll-blocked'))\n  const scrollbarWidth = window.innerWidth - document.documentElement.offsetWidth\n\n  const scrollableParent = (el => hasScrollbar(el) && el)(offsetParent || document.documentElement)\n  if (scrollableParent) {\n    data.root.value!.classList.add('v-overlay--scroll-blocked')\n  }\n\n  scrollElements.forEach((el, i) => {\n    el.style.setProperty('--v-body-scroll-x', convertToUnit(-el.scrollLeft))\n    el.style.setProperty('--v-body-scroll-y', convertToUnit(-el.scrollTop))\n\n    if (el !== document.documentElement) {\n      el.style.setProperty('--v-scrollbar-offset', convertToUnit(scrollbarWidth))\n    }\n\n    el.classList.add('v-overlay-scroll-blocked')\n  })\n\n  onScopeDispose(() => {\n    scrollElements.forEach((el, i) => {\n      const x = parseFloat(el.style.getPropertyValue('--v-body-scroll-x'))\n      const y = parseFloat(el.style.getPropertyValue('--v-body-scroll-y'))\n\n      const scrollBehavior = el.style.scrollBehavior\n\n      el.style.scrollBehavior = 'auto'\n      el.style.removeProperty('--v-body-scroll-x')\n      el.style.removeProperty('--v-body-scroll-y')\n      el.style.removeProperty('--v-scrollbar-offset')\n      el.classList.remove('v-overlay-scroll-blocked')\n\n      el.scrollLeft = -x\n      el.scrollTop = -y\n\n      el.style.scrollBehavior = scrollBehavior\n    })\n    if (scrollableParent) {\n      data.root.value!.classList.remove('v-overlay--scroll-blocked')\n    }\n  })\n}\n\nfunction repositionScrollStrategy (data: ScrollStrategyData, props: StrategyProps, scope: EffectScope) {\n  let slow = false\n  let raf = -1\n  let ric = -1\n\n  function update (e: Event) {\n    requestNewFrame(() => {\n      const start = performance.now()\n      data.updateLocation.value?.(e)\n      const time = performance.now() - start\n      slow = time / (1000 / 60) > 2\n    })\n  }\n\n  ric = (typeof requestIdleCallback === 'undefined' ? (cb: Function) => cb() : requestIdleCallback)(() => {\n    scope.run(() => {\n      bindScroll(data.targetEl.value ?? data.contentEl.value, e => {\n        if (slow) {\n          // If the position calculation is slow,\n          // defer updates until scrolling is finished.\n          // Browsers usually fire one scroll event per frame so\n          // we just wait until we've got two frames without an event\n          cancelAnimationFrame(raf)\n          raf = requestAnimationFrame(() => {\n            raf = requestAnimationFrame(() => {\n              update(e)\n            })\n          })\n        } else {\n          update(e)\n        }\n      })\n    })\n  })\n\n  onScopeDispose(() => {\n    typeof cancelIdleCallback !== 'undefined' && cancelIdleCallback(ric)\n    cancelAnimationFrame(raf)\n  })\n}\n\n/** @private */\nfunction bindScroll (el: HTMLElement | undefined, onScroll: (e: Event) => void) {\n  const scrollElements = [document, ...getScrollParents(el)]\n  scrollElements.forEach(el => {\n    el.addEventListener('scroll', onScroll, { passive: true })\n  })\n\n  onScopeDispose(() => {\n    scrollElements.forEach(el => {\n      el.removeEventListener('scroll', onScroll)\n    })\n  })\n}\n", "// Types\nimport type { InjectionKey } from 'vue'\n\ninterface MenuProvide {\n  register (): void\n  unregister (): void\n  closeParents (e?: MouseEvent): void\n}\n\nexport const VMenuSymbol: InjectionKey<MenuProvide> = Symbol.for('vuetify:v-menu')\n", "// Utilities\nimport { defer, propsFactory } from '@/util'\n\n// Types\nexport interface DelayProps {\n  closeDelay?: number | string\n  openDelay?: number | string\n}\n\n// Composables\nexport const makeDelayProps = propsFactory({\n  closeDelay: [Number, String],\n  openDelay: [Number, String],\n}, 'delay')\n\nexport function useDelay (props: DelayProps, cb?: (value: boolean) => void) {\n  let clearDelay: (() => void) = () => {}\n\n  function runDelay (isOpening: boolean) {\n    clearDelay?.()\n\n    const delay = Number(isOpening ? props.openDelay : props.closeDelay)\n\n    return new Promise(resolve => {\n      clearDelay = defer(delay, () => {\n        cb?.(isOpening)\n        resolve(isOpening)\n      })\n    })\n  }\n\n  function runOpenDelay () {\n    return runDelay(true)\n  }\n\n  function runCloseDelay () {\n    return runDelay(false)\n  }\n\n  return {\n    clearDelay,\n    runOpenDelay,\n    runCloseDelay,\n  }\n}\n", "// Components\nimport { VMenuSymbol } from '@/components/VMenu/shared'\n\n// Composables\nimport { makeDelayProps, useDelay } from '@/composables/delay'\n\n// Utilities\nimport {\n  computed,\n  effectScope,\n  inject,\n  mergeProps,\n  nextTick,\n  onScopeDispose,\n  ref,\n  watch,\n  watchEffect,\n} from 'vue'\nimport {\n  bindProps,\n  getCurrentInstance,\n  IN_BROWSER,\n  matchesSelector,\n  propsFactory,\n  templateRef,\n  unbindProps,\n} from '@/util'\n\n// Types\nimport type {\n  ComponentInternalInstance,\n  ComponentPublicInstance,\n  EffectScope,\n  PropType,\n  Ref,\n} from 'vue'\nimport type { DelayProps } from '@/composables/delay'\n\ninterface ActivatorProps extends DelayProps {\n  target: 'parent' | 'cursor' | (string & {}) | Element | ComponentPublicInstance | [x: number, y: number] | undefined\n  activator: 'parent' | (string & {}) | Element | ComponentPublicInstance | undefined\n  activatorProps: Record<string, any>\n\n  openOnClick: boolean | undefined\n  openOnHover: boolean\n  openOnFocus: boolean | undefined\n\n  closeOnContentClick: boolean\n}\n\nexport const makeActivatorProps = propsFactory({\n  target: [String, Object] as PropType<ActivatorProps['target']>,\n  activator: [String, Object] as PropType<ActivatorProps['activator']>,\n  activatorProps: {\n    type: Object as PropType<ActivatorProps['activatorProps']>,\n    default: () => ({}),\n  },\n\n  openOnClick: {\n    type: Boolean,\n    default: undefined,\n  },\n  openOnHover: Boolean,\n  openOnFocus: {\n    type: Boolean,\n    default: undefined,\n  },\n\n  closeOnContentClick: Boolean,\n\n  ...makeDelayProps(),\n}, 'VOverlay-activator')\n\nexport function useActivator (\n  props: ActivatorProps,\n  { isActive, isTop, contentEl }: {\n    isActive: Ref<boolean>\n    isTop: Ref<boolean>\n    contentEl: Ref<HTMLElement | undefined>\n  }\n) {\n  const vm = getCurrentInstance('useActivator')\n  const activatorEl = ref<HTMLElement>()\n\n  let isHovered = false\n  let isFocused = false\n  let firstEnter = true\n\n  const openOnFocus = computed(() => props.openOnFocus || (props.openOnFocus == null && props.openOnHover))\n  const openOnClick = computed(() => props.openOnClick || (props.openOnClick == null && !props.openOnHover && !openOnFocus.value))\n\n  const { runOpenDelay, runCloseDelay } = useDelay(props, value => {\n    if (\n      value === (\n        (props.openOnHover && isHovered) ||\n        (openOnFocus.value && isFocused)\n      ) && !(props.openOnHover && isActive.value && !isTop.value)\n    ) {\n      if (isActive.value !== value) {\n        firstEnter = true\n      }\n      isActive.value = value\n    }\n  })\n\n  const cursorTarget = ref<[x: number, y: number]>()\n  const availableEvents = {\n    onClick: (e: MouseEvent) => {\n      e.stopPropagation()\n      activatorEl.value = (e.currentTarget || e.target) as HTMLElement\n      if (!isActive.value) {\n        cursorTarget.value = [e.clientX, e.clientY]\n      }\n      isActive.value = !isActive.value\n    },\n    onMouseenter: (e: MouseEvent) => {\n      if (e.sourceCapabilities?.firesTouchEvents) return\n\n      isHovered = true\n      activatorEl.value = (e.currentTarget || e.target) as HTMLElement\n      runOpenDelay()\n    },\n    onMouseleave: (e: MouseEvent) => {\n      isHovered = false\n      runCloseDelay()\n    },\n    onFocus: (e: FocusEvent) => {\n      if (matchesSelector(e.target as HTMLElement, ':focus-visible') === false) return\n\n      isFocused = true\n      e.stopPropagation()\n      activatorEl.value = (e.currentTarget || e.target) as HTMLElement\n\n      runOpenDelay()\n    },\n    onBlur: (e: FocusEvent) => {\n      isFocused = false\n      e.stopPropagation()\n\n      runCloseDelay()\n    },\n  }\n\n  const activatorEvents = computed(() => {\n    const events: Partial<typeof availableEvents> = {}\n\n    if (openOnClick.value) {\n      events.onClick = availableEvents.onClick\n    }\n    if (props.openOnHover) {\n      events.onMouseenter = availableEvents.onMouseenter\n      events.onMouseleave = availableEvents.onMouseleave\n    }\n    if (openOnFocus.value) {\n      events.onFocus = availableEvents.onFocus\n      events.onBlur = availableEvents.onBlur\n    }\n\n    return events\n  })\n\n  const contentEvents = computed(() => {\n    const events: Record<string, EventListener> = {}\n\n    if (props.openOnHover) {\n      events.onMouseenter = () => {\n        isHovered = true\n        runOpenDelay()\n      }\n      events.onMouseleave = () => {\n        isHovered = false\n        runCloseDelay()\n      }\n    }\n\n    if (openOnFocus.value) {\n      events.onFocusin = () => {\n        isFocused = true\n        runOpenDelay()\n      }\n      events.onFocusout = () => {\n        isFocused = false\n        runCloseDelay()\n      }\n    }\n\n    if (props.closeOnContentClick) {\n      const menu = inject(VMenuSymbol, null)\n      events.onClick = () => {\n        isActive.value = false\n        menu?.closeParents()\n      }\n    }\n\n    return events\n  })\n\n  const scrimEvents = computed(() => {\n    const events: Record<string, EventListener> = {}\n\n    if (props.openOnHover) {\n      events.onMouseenter = () => {\n        if (firstEnter) {\n          isHovered = true\n          firstEnter = false\n          runOpenDelay()\n        }\n      }\n      events.onMouseleave = () => {\n        isHovered = false\n        runCloseDelay()\n      }\n    }\n\n    return events\n  })\n\n  watch(isTop, val => {\n    if (val && (\n      (props.openOnHover && !isHovered && (!openOnFocus.value || !isFocused)) ||\n      (openOnFocus.value && !isFocused && (!props.openOnHover || !isHovered))\n    ) && !contentEl.value?.contains(document.activeElement)) {\n      isActive.value = false\n    }\n  })\n\n  watch(isActive, val => {\n    if (!val) {\n      setTimeout(() => {\n        cursorTarget.value = undefined\n      })\n    }\n  }, { flush: 'post' })\n\n  const activatorRef = templateRef()\n  watchEffect(() => {\n    if (!activatorRef.value) return\n\n    nextTick(() => {\n      activatorEl.value = activatorRef.el\n    })\n  })\n\n  const targetRef = templateRef()\n  const target = computed(() => {\n    if (props.target === 'cursor' && cursorTarget.value) return cursorTarget.value\n    if (targetRef.value) return targetRef.el\n    return getTarget(props.target, vm) || activatorEl.value\n  })\n  const targetEl = computed(() => {\n    return Array.isArray(target.value)\n      ? undefined\n      : target.value\n  })\n\n  let scope: EffectScope\n  watch(() => !!props.activator, val => {\n    if (val && IN_BROWSER) {\n      scope = effectScope()\n      scope.run(() => {\n        _useActivator(props, vm, { activatorEl, activatorEvents })\n      })\n    } else if (scope) {\n      scope.stop()\n    }\n  }, { flush: 'post', immediate: true })\n\n  onScopeDispose(() => {\n    scope?.stop()\n  })\n\n  return { activatorEl, activatorRef, target, targetEl, targetRef, activatorEvents, contentEvents, scrimEvents }\n}\n\nfunction _useActivator (\n  props: ActivatorProps,\n  vm: ComponentInternalInstance,\n  { activatorEl, activatorEvents }: Pick<ReturnType<typeof useActivator>, 'activatorEl' | 'activatorEvents'>\n) {\n  watch(() => props.activator, (val, oldVal) => {\n    if (oldVal && val !== oldVal) {\n      const activator = getActivator(oldVal)\n      activator && unbindActivatorProps(activator)\n    }\n    if (val) {\n      nextTick(() => bindActivatorProps())\n    }\n  }, { immediate: true })\n\n  watch(() => props.activatorProps, () => {\n    bindActivatorProps()\n  })\n\n  onScopeDispose(() => {\n    unbindActivatorProps()\n  })\n\n  function bindActivatorProps (el = getActivator(), _props = props.activatorProps) {\n    if (!el) return\n\n    bindProps(el, mergeProps(activatorEvents.value, _props))\n  }\n\n  function unbindActivatorProps (el = getActivator(), _props = props.activatorProps) {\n    if (!el) return\n\n    unbindProps(el, mergeProps(activatorEvents.value, _props))\n  }\n\n  function getActivator (selector = props.activator): HTMLElement | undefined {\n    const activator = getTarget(selector, vm)\n\n    // The activator should only be a valid element (Ignore comments and text nodes)\n    activatorEl.value = activator?.nodeType === Node.ELEMENT_NODE ? activator : undefined\n\n    return activatorEl.value\n  }\n}\n\nfunction getTarget<T extends 'parent' | string | Element | ComponentPublicInstance | [x: number, y: number] | undefined> (\n  selector: T,\n  vm: ComponentInternalInstance\n): HTMLElement | undefined | (T extends any[] ? [x: number, y: number] : never) {\n  if (!selector) return\n\n  let target\n  if (selector === 'parent') {\n    let el = vm?.proxy?.$el?.parentNode\n    while (el?.hasAttribute('data-no-activator')) {\n      el = el.parentNode\n    }\n    target = el\n  } else if (typeof selector === 'string') {\n    // Selector\n    target = document.querySelector(selector)\n  } else if ('$el' in selector) {\n    // Component (ref)\n    target = selector.$el\n  } else {\n    // HTMLElement | Element | [x, y]\n    target = selector\n  }\n\n  return target\n}\n", "// Utilities\nimport { toValue } from 'vue'\nimport { destructComputed, getForeground, isCssColor, isParsableColor, parseColor } from '@/util'\n\n// Types\nimport type { CSSProperties, MaybeRefOrGetter, Ref } from 'vue'\n\ntype ColorValue = string | false | null | undefined\n\nexport interface TextColorData {\n  textColorClasses: Ref<string[]>\n  textColorStyles: Ref<CSSProperties>\n}\n\nexport interface BackgroundColorData {\n  backgroundColorClasses: Ref<string[]>\n  backgroundColorStyles: Ref<CSSProperties>\n}\n\n// Composables\nexport function useColor (colors: MaybeRefOrGetter<{ background?: ColorValue, text?: ColorValue }>) {\n  return destructComputed(() => {\n    const _colors = toValue(colors)\n    const classes: string[] = []\n    const styles: CSSProperties = {}\n\n    if (_colors.background) {\n      if (isCssColor(_colors.background)) {\n        styles.backgroundColor = _colors.background\n\n        if (!_colors.text && isParsableColor(_colors.background)) {\n          const backgroundColor = parseColor(_colors.background)\n          if (backgroundColor.a == null || backgroundColor.a === 1) {\n            const textColor = getForeground(backgroundColor)\n\n            styles.color = textColor\n            styles.caretColor = textColor\n          }\n        }\n      } else {\n        classes.push(`bg-${_colors.background}`)\n      }\n    }\n\n    if (_colors.text) {\n      if (isCssColor(_colors.text)) {\n        styles.color = _colors.text\n        styles.caretColor = _colors.text\n      } else {\n        classes.push(`text-${_colors.text}`)\n      }\n    }\n\n    return { colorClasses: classes, colorStyles: styles }\n  })\n}\n\nexport function useTextColor (color: MaybeRefOrGetter<ColorValue>): TextColorData {\n  const {\n    colorClasses: textColorClasses,\n    colorStyles: textColorStyles,\n  } = useColor(() => ({\n    text: toValue(color),\n  }))\n\n  return { textColorClasses, textColorStyles }\n}\n\nexport function useBackgroundColor (color: MaybeRefOrGetter<ColorValue>): BackgroundColorData {\n  const {\n    colorClasses: backgroundColorClasses,\n    colorStyles: backgroundColorStyles,\n  } = useColor(() => ({\n    background: toValue(color),\n  }))\n\n  return { backgroundColorClasses, backgroundColorStyles }\n}\n", "// Utilities\nimport { computed } from 'vue'\nimport { convertToUnit, propsFactory } from '@/util'\n\n// Types\nexport interface DimensionProps {\n  height?: number | string\n  maxHeight?: number | string\n  maxWidth?: number | string\n  minHeight?: number | string\n  minWidth?: number | string\n  width?: number | string\n}\n\n// Composables\nexport const makeDimensionProps = propsFactory({\n  height: [Number, String],\n  maxHeight: [Number, String],\n  maxWidth: [Number, String],\n  minHeight: [Number, String],\n  minWidth: [Number, String],\n  width: [Number, String],\n}, 'dimension')\n\nexport function useDimension (props: DimensionProps) {\n  const dimensionStyles = computed(() => {\n    const styles: Record<string, any> = {}\n\n    const height = convertToUnit(props.height)\n    const maxHeight = convertToUnit(props.maxHeight)\n    const maxWidth = convertToUnit(props.maxWidth)\n    const minHeight = convertToUnit(props.minHeight)\n    const minWidth = convertToUnit(props.minWidth)\n    const width = convertToUnit(props.width)\n\n    if (height != null) styles.height = height\n    if (maxHeight != null) styles.maxHeight = maxHeight\n    if (maxWidth != null) styles.maxWidth = maxWidth\n    if (minHeight != null) styles.minHeight = minHeight\n    if (minWidth != null) styles.minWidth = minWidth\n    if (width != null) styles.width = width\n\n    return styles\n  })\n\n  return { dimensionStyles }\n}\n", "// Composables\nimport { useDisplay } from '@/composables/display'\n\n// Utilities\nimport { onMounted, shallowRef } from 'vue'\nimport { IN_BROWSER } from '@/util'\n\nexport function useHydration () {\n  if (!IN_BROWSER) return shallowRef(false)\n\n  const { ssr } = useDisplay()\n\n  if (ssr) {\n    const isMounted = shallowRef(false)\n    onMounted(() => {\n      isMounted.value = true\n    })\n    return isMounted\n  } else {\n    return shallowRef(true)\n  }\n}\n", "// Utilities\nimport { shallowRef, toRef, watch } from 'vue'\nimport { propsFactory } from '@/util'\n\n// Types\nimport type { Ref } from 'vue'\n\nexport const makeLazyProps = propsFactory({\n  eager: <PERSON><PERSON><PERSON>,\n}, 'lazy')\n\nexport function useLazy (props: { eager: boolean }, active: Ref<boolean>) {\n  const isBooted = shallowRef(false)\n  const hasContent = toRef(() => isBooted.value || props.eager || active.value)\n\n  watch(active, () => isBooted.value = true)\n\n  function onAfterLeave () {\n    if (!props.eager) isBooted.value = false\n  }\n\n  return { isBooted, hasContent, onAfterLeave }\n}\n", "// Utilities\nimport {\n  computed,\n  nextTick,\n  onScopeDispose, reactive,\n  resolveDynamicComponent,\n  toRef,\n} from 'vue'\nimport { deepEqual, getCurrentInstance, hasEvent, IN_BROWSER, propsFactory } from '@/util'\n\n// Types\nimport type { PropType, Ref, SetupContext } from 'vue'\nimport type {\n  RouterLink as _RouterLink,\n  useLink as _useLink,\n  NavigationGuardNext,\n  RouteLocationNormalizedLoaded,\n  RouteLocationRaw,\n  Router,\n} from 'vue-router'\nimport type { EventProp } from '@/util'\n\nexport function useRoute (): Ref<RouteLocationNormalizedLoaded | undefined> {\n  const vm = getCurrentInstance('useRoute')\n\n  return computed(() => vm?.proxy?.$route)\n}\n\nexport function useRouter (): Router | undefined {\n  return getCurrentInstance('useRouter')?.proxy?.$router\n}\n\nexport interface LinkProps {\n  href: string | undefined\n  replace: boolean | undefined\n  to: RouteLocationRaw | undefined\n  exact: boolean | undefined\n}\n\nexport interface LinkListeners {\n  onClick?: EventProp | undefined\n  onClickOnce?: EventProp | undefined\n}\n\nexport interface UseLink extends Omit<Partial<ReturnType<typeof _useLink>>, 'href'> {\n  isLink: Readonly<Ref<boolean>>\n  isClickable: Readonly<Ref<boolean>>\n  href: Ref<string | undefined>\n  linkProps: Record<string, string | undefined>\n}\n\nexport function useLink (props: LinkProps & LinkListeners, attrs: SetupContext['attrs']): UseLink {\n  const RouterLink = resolveDynamicComponent('RouterLink') as typeof _RouterLink | string\n\n  const isLink = toRef(() => !!(props.href || props.to))\n  const isClickable = computed(() => {\n    return isLink?.value || hasEvent(attrs, 'click') || hasEvent(props, 'click')\n  })\n\n  if (typeof RouterLink === 'string' || !('useLink' in RouterLink)) {\n    const href = toRef(() => props.href)\n    return {\n      isLink,\n      isClickable,\n      href,\n      linkProps: reactive({ href }),\n    }\n  }\n\n  // vue-router useLink `to` prop needs to be reactive and useLink will crash if undefined\n  const routerLink = RouterLink.useLink({\n    to: toRef(() => props.to || ''),\n    replace: toRef(() => props.replace),\n  })\n  // Actual link needs to be undefined when to prop is not used\n  const link = computed(() => props.to ? routerLink : undefined)\n  const route = useRoute()\n  const isActive = computed(() => {\n    if (!link.value) return false\n    if (!props.exact) return link.value.isActive?.value ?? false\n    if (!route.value) return link.value.isExactActive?.value ?? false\n\n    return link.value.isExactActive?.value && deepEqual(link.value.route.value.query, route.value.query)\n  })\n  const href = computed(() => props.to ? link.value?.route.value.href : props.href)\n\n  return {\n    isLink,\n    isClickable,\n    isActive,\n    route: link.value?.route,\n    navigate: link.value?.navigate,\n    href,\n    linkProps: reactive({\n      href,\n      'aria-current': toRef(() => isActive.value ? 'page' : undefined),\n    }),\n  }\n}\n\nexport const makeRouterProps = propsFactory({\n  href: String,\n  replace: Boolean,\n  to: [String, Object] as PropType<RouteLocationRaw>,\n  exact: Boolean,\n}, 'router')\n\nlet inTransition = false\nexport function useBackButton (router: Router | undefined, cb: (next: NavigationGuardNext) => void) {\n  let popped = false\n  let removeBefore: (() => void) | undefined\n  let removeAfter: (() => void) | undefined\n\n  if (IN_BROWSER && router?.beforeEach) {\n    nextTick(() => {\n      window.addEventListener('popstate', onPopstate)\n      removeBefore = router.beforeEach((to, from, next) => {\n        if (!inTransition) {\n          setTimeout(() => popped ? cb(next) : next())\n        } else {\n          popped ? cb(next) : next()\n        }\n        inTransition = true\n      })\n      removeAfter = router?.afterEach(() => {\n        inTransition = false\n      })\n    })\n    onScopeDispose(() => {\n      window.removeEventListener('popstate', onPopstate)\n      removeBefore?.()\n      removeAfter?.()\n    })\n  }\n\n  function onPopstate (e: PopStateEvent) {\n    if (e.state?.replaced) return\n\n    popped = true\n    setTimeout(() => (popped = false))\n  }\n}\n", "// Utilities\nimport { getCurrentInstance } from '@/util'\n\nexport function useScopeId () {\n  const vm = getCurrentInstance('useScopeId')\n\n  const scopeId = vm!.vnode.scopeId\n\n  return { scopeId: scopeId ? { [scopeId]: '' } : undefined }\n}\n", "// Composables\nimport { useToggleScope } from '@/composables/toggleScope'\n\n// Utilities\nimport {\n  inject,\n  onScopeDispose,\n  provide,\n  reactive,\n  readonly,\n  shallowRef,\n  toRaw,\n  toRef,\n  toValue,\n  watchEffect,\n} from 'vue'\nimport { getCurrentInstance } from '@/util'\n\n// Types\nimport type { InjectionKey, MaybeRefOrGetter, Ref } from 'vue'\n\nconst StackSymbol: InjectionKey<StackProvide> = Symbol.for('vuetify:stack')\n\ninterface StackProvide {\n  activeChildren: Set<number>\n}\n\nconst globalStack = reactive<[uid: number, zIndex: number][]>([])\n\nexport function useStack (\n  isActive: Readonly<Ref<boolean>>,\n  zIndex: MaybeRefOrGetter<string | number>,\n  disableGlobalStack: boolean\n) {\n  const vm = getCurrentInstance('useStack')\n  const createStackEntry = !disableGlobalStack\n\n  const parent = inject(StackSymbol, undefined)\n  const stack: StackProvide = reactive({\n    activeChildren: new Set<number>(),\n  })\n  provide(StackSymbol, stack)\n\n  const _zIndex = shallowRef(Number(toValue(zIndex)))\n  useToggleScope(isActive, () => {\n    const lastZIndex = globalStack.at(-1)?.[1]\n    _zIndex.value = lastZIndex ? lastZIndex + 10 : Number(toValue(zIndex))\n\n    if (createStackEntry) {\n      globalStack.push([vm.uid, _zIndex.value])\n    }\n\n    parent?.activeChildren.add(vm.uid)\n\n    onScopeDispose(() => {\n      if (createStackEntry) {\n        const idx = toRaw(globalStack).findIndex(v => v[0] === vm.uid)\n        globalStack.splice(idx, 1)\n      }\n\n      parent?.activeChildren.delete(vm.uid)\n    })\n  })\n\n  const globalTop = shallowRef(true)\n  if (createStackEntry) {\n    watchEffect(() => {\n      const _isTop = globalStack.at(-1)?.[0] === vm.uid\n      setTimeout(() => globalTop.value = _isTop)\n    })\n  }\n\n  const localTop = toRef(() => !stack.activeChildren.size)\n\n  return {\n    globalTop: readonly(globalTop),\n    localTop,\n    stackStyles: toRef(() => ({ zIndex: _zIndex.value })),\n  }\n}\n", "// Utilities\nimport { computed, warn } from 'vue'\nimport { IN_BROWSER } from '@/util'\n\nexport function useTeleport (target: () => (boolean | string | ParentNode)) {\n  const teleportTarget = computed(() => {\n    const _target = target()\n\n    if (_target === true || !IN_BROWSER) return undefined\n\n    const targetElement =\n      _target === false ? document.body\n      : typeof _target === 'string' ? document.querySelector(_target)\n      : _target\n\n    if (targetElement == null) {\n      warn(`Unable to locate target ${_target}`)\n      return undefined\n    }\n\n    let container = [...targetElement.children].find(el => el.matches('.v-overlay-container'))\n\n    if (!container) {\n      container = document.createElement('div')\n      container.className = 'v-overlay-container'\n      targetElement.appendChild(container)\n    }\n\n    return container\n  })\n\n  return { teleportTarget }\n}\n", "// Utilities\nimport { h, mergeProps, Transition, TransitionGroup } from 'vue'\nimport { isObject, onlyDefinedProps, propsFactory } from '@/util'\n\n// Types\nimport type { Component, FunctionalComponent, Prop, TransitionProps } from 'vue'\n\nexport const makeTransitionProps = propsFactory({\n  transition: {\n    type: null,\n    default: 'fade-transition',\n    validator: val => val !== true,\n  } as Prop<null | string | boolean | TransitionProps & { component?: Component }>,\n}, 'transition')\n\ninterface MaybeTransitionProps extends TransitionProps {\n  transition?: null | string | boolean | TransitionProps & { component?: any }\n  disabled?: boolean\n  group?: boolean\n}\n\nexport const MaybeTransition: FunctionalComponent<MaybeTransitionProps> = (props, { slots }) => {\n  const { transition, disabled, group, ...rest } = props\n\n  const {\n    component = group ? TransitionGroup : Transition,\n    ...customProps\n  } = isObject(transition) ? transition : {}\n\n  let transitionProps\n  if (isObject(transition)) {\n    transitionProps = mergeProps(\n      customProps,\n      onlyDefinedProps({ disabled, group }),\n      rest,\n    )\n  } else {\n    transitionProps = mergeProps(\n      { name: disabled || !transition ? '' : transition },\n      rest,\n    )\n  }\n\n  return h(\n    component,\n    transitionProps,\n    slots\n  )\n}\n", "// Utilities\nimport { keys } from '@/util'\n\n// Types\nimport type {\n  DirectiveBinding,\n} from 'vue'\n\nexport interface TouchHandlers {\n  start?: (wrapperEvent: { originalEvent: TouchEvent } & TouchData) => void\n  end?: (wrapperEvent: { originalEvent: TouchEvent } & TouchData) => void\n  move?: (wrapperEvent: { originalEvent: TouchEvent } & TouchData) => void\n  left?: (wrapper: TouchData) => void\n  right?: (wrapper: TouchData) => void\n  up?: (wrapper: TouchData) => void\n  down?: (wrapper: TouchData) => void\n}\n\nexport interface TouchData {\n  touchstartX: number\n  touchstartY: number\n  touchmoveX: number\n  touchmoveY: number\n  touchendX: number\n  touchendY: number\n  offsetX: number\n  offsetY: number\n}\n\nexport type TouchWrapper = TouchHandlers & TouchData\n\nexport interface TouchValue extends TouchHandlers {\n  parent?: boolean\n  options?: AddEventListenerOptions\n}\n\nexport interface TouchStoredHandlers {\n  touchstart: (e: TouchEvent) => void\n  touchend: (e: TouchEvent) => void\n  touchmove: (e: TouchEvent) => void\n}\n\nexport interface TouchDirectiveBinding extends Omit<DirectiveBinding, 'value'> {\n  value?: TouchValue\n}\n\nconst handleGesture = (wrapper: TouchWrapper) => {\n  const { touchstartX, touchendX, touchstartY, touchendY } = wrapper\n  const dirRatio = 0.5\n  const minDistance = 16\n  wrapper.offsetX = touchendX - touchstartX\n  wrapper.offsetY = touchendY - touchstartY\n\n  if (Math.abs(wrapper.offsetY) < dirRatio * Math.abs(wrapper.offsetX)) {\n    wrapper.left && (touchendX < touchstartX - minDistance) && wrapper.left(wrapper)\n    wrapper.right && (touchendX > touchstartX + minDistance) && wrapper.right(wrapper)\n  }\n\n  if (Math.abs(wrapper.offsetX) < dirRatio * Math.abs(wrapper.offsetY)) {\n    wrapper.up && (touchendY < touchstartY - minDistance) && wrapper.up(wrapper)\n    wrapper.down && (touchendY > touchstartY + minDistance) && wrapper.down(wrapper)\n  }\n}\n\nfunction touchstart (event: TouchEvent, wrapper: TouchWrapper) {\n  const touch = event.changedTouches[0]\n  wrapper.touchstartX = touch.clientX\n  wrapper.touchstartY = touch.clientY\n\n  wrapper.start?.({ originalEvent: event, ...wrapper })\n}\n\nfunction touchend (event: TouchEvent, wrapper: TouchWrapper) {\n  const touch = event.changedTouches[0]\n  wrapper.touchendX = touch.clientX\n  wrapper.touchendY = touch.clientY\n\n  wrapper.end?.({ originalEvent: event, ...wrapper })\n\n  handleGesture(wrapper)\n}\n\nfunction touchmove (event: TouchEvent, wrapper: TouchWrapper) {\n  const touch = event.changedTouches[0]\n  wrapper.touchmoveX = touch.clientX\n  wrapper.touchmoveY = touch.clientY\n\n  wrapper.move?.({ originalEvent: event, ...wrapper })\n}\n\nfunction createHandlers (value: TouchHandlers = {}): TouchStoredHandlers {\n  const wrapper = {\n    touchstartX: 0,\n    touchstartY: 0,\n    touchendX: 0,\n    touchendY: 0,\n    touchmoveX: 0,\n    touchmoveY: 0,\n    offsetX: 0,\n    offsetY: 0,\n    left: value.left,\n    right: value.right,\n    up: value.up,\n    down: value.down,\n    start: value.start,\n    move: value.move,\n    end: value.end,\n  }\n\n  return {\n    touchstart: (e: TouchEvent) => touchstart(e, wrapper),\n    touchend: (e: TouchEvent) => touchend(e, wrapper),\n    touchmove: (e: TouchEvent) => touchmove(e, wrapper),\n  }\n}\n\nfunction mounted (el: HTMLElement, binding: TouchDirectiveBinding) {\n  const value = binding.value\n  const target = value?.parent ? el.parentElement : el\n  const options = value?.options ?? { passive: true }\n  const uid = binding.instance?.$.uid // TODO: use custom uid generator\n\n  if (!target || !uid) return\n\n  const handlers = createHandlers(binding.value)\n\n  target._touchHandlers = target._touchHandlers ?? Object.create(null)\n  target._touchHandlers![uid] = handlers\n\n  keys(handlers).forEach(eventName => {\n    target.addEventListener(eventName, handlers[eventName], options)\n  })\n}\n\nfunction unmounted (el: HTMLElement, binding: TouchDirectiveBinding) {\n  const target = binding.value?.parent ? el.parentElement : el\n  const uid = binding.instance?.$.uid\n\n  if (!target?._touchHandlers || !uid) return\n\n  const handlers = target._touchHandlers[uid]\n\n  keys(handlers).forEach(eventName => {\n    target.removeEventListener(eventName, handlers[eventName])\n  })\n\n  delete target._touchHandlers[uid]\n}\n\nexport const Touch = {\n  mounted,\n  unmounted,\n}\n\nexport default Touch\n", "// Styles\nimport './VTooltip.sass'\n\n// Components\nimport { VOverlay } from '@/components/VOverlay'\nimport { makeVOverlayProps } from '@/components/VOverlay/VOverlay'\n\n// Composables\nimport { forwardRefs } from '@/composables/forwardRefs'\nimport { useProxiedModel } from '@/composables/proxiedModel'\nimport { useScopeId } from '@/composables/scopeId'\n\n// Utilities\nimport { computed, mergeProps, ref, toRef, useId } from 'vue'\nimport { genericComponent, omit, propsFactory, useRender } from '@/util'\n\n// Types\nimport type { StrategyProps } from '@/components/VOverlay/locationStrategies'\nimport type { OverlaySlots } from '@/components/VOverlay/VOverlay'\n\nexport const makeVTooltipProps = propsFactory({\n  id: String,\n  interactive: Boolean,\n  text: String,\n\n  ...omit(makeVOverlayProps({\n    closeOnBack: false,\n    location: 'end' as const,\n    locationStrategy: 'connected' as const,\n    eager: true,\n    minWidth: 0,\n    offset: 10,\n    openOnClick: false,\n    openOnHover: true,\n    origin: 'auto' as const,\n    scrim: false,\n    scrollStrategy: 'reposition' as const,\n    transition: null,\n  }), [\n    'absolute',\n    'persistent',\n  ]),\n}, 'VTooltip')\n\nexport const VTooltip = genericComponent<OverlaySlots>()({\n  name: 'VTooltip',\n\n  props: makeVTooltipProps(),\n\n  emits: {\n    'update:modelValue': (value: boolean) => true,\n  },\n\n  setup (props, { slots }) {\n    const isActive = useProxiedModel(props, 'modelValue')\n    const { scopeId } = useScopeId()\n\n    const uid = useId()\n    const id = toRef(() => props.id || `v-tooltip-${uid}`)\n\n    const overlay = ref<VOverlay>()\n\n    const location = computed(() => {\n      return props.location.split(' ').length > 1\n        ? props.location\n        : props.location + ' center' as StrategyProps['location']\n    })\n\n    const origin = computed(() => {\n      return (\n        props.origin === 'auto' ||\n        props.origin === 'overlap' ||\n        props.origin.split(' ').length > 1 ||\n        props.location.split(' ').length > 1\n      ) ? props.origin\n        : props.origin + ' center' as StrategyProps['origin']\n    })\n\n    const transition = toRef(() => {\n      if (props.transition != null) return props.transition\n      return isActive.value ? 'scale-transition' : 'fade-transition'\n    })\n\n    const activatorProps = computed(() =>\n      mergeProps({\n        'aria-describedby': id.value,\n      }, props.activatorProps)\n    )\n\n    useRender(() => {\n      const overlayProps = VOverlay.filterProps(props)\n\n      return (\n        <VOverlay\n          ref={ overlay }\n          class={[\n            'v-tooltip',\n            { 'v-tooltip--interactive': props.interactive },\n            props.class,\n          ]}\n          style={ props.style }\n          id={ id.value }\n          { ...overlayProps }\n          v-model={ isActive.value }\n          transition={ transition.value }\n          absolute\n          location={ location.value }\n          origin={ origin.value }\n          persistent\n          role=\"tooltip\"\n          activatorProps={ activatorProps.value }\n          _disableGlobalStack\n          { ...scopeId }\n        >\n          {{\n            activator: slots.activator,\n            default: (...args) => slots.default?.(...args) ?? props.text,\n          }}\n        </VOverlay>\n      )\n    })\n\n    return forwardRefs({}, overlay)\n  },\n})\n\nexport type VTooltip = InstanceType<typeof VTooltip>\n", "// Types\nimport type { ComponentOptionsBase, ComponentPublicInstance, Ref, UnwrapRef } from 'vue'\nimport type { UnionToIntersection } from '@/util'\n\nconst Refs = Symbol('Forwarded refs')\n\n/** Omit properties starting with P */\ntype OmitPrefix<\n  T,\n  P extends string,\n  E = Extract<keyof T, `${P}${any}`>,\n> = [E] extends [never] ? T : Omit<T, `${P}${any}`>\ntype OmitPrivate<T> = OmitPrefix<T, '$'>\n\n/** Omit keyof $props from T */\ntype OmitProps<T> = T extends { $props: any } ? Omit<T, keyof T['$props']> : T\n\nfunction getDescriptor (obj: any, key: PropertyKey) {\n  let currentObj = obj\n  while (currentObj) {\n    const descriptor = Reflect.getOwnPropertyDescriptor(currentObj, key)\n    if (descriptor) return descriptor\n    currentObj = Object.getPrototypeOf(currentObj)\n  }\n  return undefined\n}\n\nexport function forwardRefs<\n  T extends {},\n  U extends Ref<HTMLElement | Omit<ComponentPublicInstance, '$emit' | '$slots'> | undefined>[],\n  UU = { [K in keyof U]: NonNullable<UnwrapRef<U[K]>> }[number],\n  UC = { [K in keyof U]: OmitPrivate<OmitProps<NonNullable<UnwrapRef<U[K]>>>> }[number],\n  R = T & UnionToIntersection<UC> & {\n    _allExposed: T | (\n      UU extends { $options: infer O }\n        ? O extends ComponentOptionsBase<any, infer E, any, any, any, any, any, any>\n          ? E\n          : never\n        : never\n    )\n  }\n> (target: T, ...refs: U): R {\n  (target as any)[Refs] = refs\n\n  return new Proxy(target, {\n    get (target, key) {\n      if (Reflect.has(target, key)) {\n        return Reflect.get(target, key)\n      }\n\n      // Skip internal properties\n      if (typeof key === 'symbol' || key.startsWith('$') || key.startsWith('__')) return\n\n      for (const ref of refs) {\n        if (ref.value && Reflect.has(ref.value, key)) {\n          const val = Reflect.get(ref.value, key)\n          return typeof val === 'function'\n            ? val.bind(ref.value)\n            : val\n        }\n      }\n    },\n    has (target, key) {\n      if (Reflect.has(target, key)) {\n        return true\n      }\n\n      // Skip internal properties\n      if (typeof key === 'symbol' || key.startsWith('$') || key.startsWith('__')) return false\n\n      for (const ref of refs) {\n        if (ref.value && Reflect.has(ref.value, key)) {\n          return true\n        }\n      }\n      return false\n    },\n    set (target, key, value) {\n      if (Reflect.has(target, key)) {\n        return Reflect.set(target, key, value)\n      }\n\n      // Skip internal properties\n      if (typeof key === 'symbol' || key.startsWith('$') || key.startsWith('__')) return false\n\n      for (const ref of refs) {\n        if (ref.value && Reflect.has(ref.value, key)) {\n          return Reflect.set(ref.value, key, value)\n        }\n      }\n\n      return false\n    },\n    getOwnPropertyDescriptor (target, key) {\n      const descriptor = Reflect.getOwnPropertyDescriptor(target, key)\n      if (descriptor) return descriptor\n\n      // Skip internal properties\n      if (typeof key === 'symbol' || key.startsWith('$') || key.startsWith('__')) return\n\n      // Check each ref's own properties\n      for (const ref of refs) {\n        if (!ref.value) continue\n        const descriptor = getDescriptor(ref.value, key) ?? ('_' in ref.value ? getDescriptor(ref.value._?.setupState, key) : undefined)\n        if (descriptor) return descriptor\n      }\n\n      // Recursive search up each ref's prototype\n      for (const ref of refs) {\n        const childRefs = ref.value && (ref.value as any)[Refs]\n        if (!childRefs) continue\n        const queue = childRefs.slice()\n        while (queue.length) {\n          const ref = queue.shift()\n          const descriptor = getDescriptor(ref.value, key)\n          if (descriptor) return descriptor\n          const childRefs = ref.value && (ref.value as any)[Refs]\n          if (childRefs) queue.push(...childRefs)\n        }\n      }\n\n      return undefined\n    },\n  }) as any\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsBA,SAASA,QAASC,IAAiBC,SAAkC;AACnE,MAAI,CAACC,sBAAuB;AAE5B,QAAMC,YAAYF,QAAQE,aAAa,CAAC;AACxC,QAAMC,QAAQH,QAAQG;AACtB,QAAM;IAAEC;IAASC;EAAQ,IAAI,OAAOF,UAAU,WAC1CA,QACA;IAAEC,SAASD;IAAOE,SAAS,CAAC;EAAE;AAElC,QAAMC,WAAW,IAAIC,qBAAqB,WAGrC;AAlCP;AAkCO,QAFHC,UAAoCC,UAAAC,SAAA,KAAAD,UAAA,CAAA,MAAAE,SAAAF,UAAA,CAAA,IAAG,CAAA;AAAE,QACzCH,YAA8BG,UAAAC,SAAA,IAAAD,UAAA,CAAA,IAAAE;AAE9B,UAAMC,YAAWb,QAAGa,aAAHb,mBAAcC,QAAQa,SAAUC,EAAEC;AACnD,QAAI,CAACH,SAAU;AAEf,UAAMI,iBAAiBR,QAAQS,KAAKC,WAASA,MAAMF,cAAc;AAIjE,QACEZ,YACE,CAACF,UAAUiB,SACXP,SAASQ,UAET,CAAClB,UAAUmB,QACXL,kBACAJ,SAASQ,OAEX;AACAhB,cAAQY,gBAAgBR,SAASF,SAAQ;IAC3C;AAEA,QAAIU,kBAAkBd,UAAUmB,KAAMC,WAAUvB,IAAIC,OAAO;QACtDY,UAASQ,OAAO;EACvB,GAAGf,OAAO;AAEVN,KAAGa,WAAWW,OAAOxB,GAAGa,QAAQ;AAChCb,KAAGa,SAAUZ,QAAQa,SAAUC,EAAEC,GAAG,IAAI;IAAEK,MAAM;IAAOd;EAAS;AAEhEA,WAASkB,QAAQzB,EAAE;AACrB;AAEA,SAASuB,UAAWvB,IAAiBC,SAAkC;AAjEvE;AAkEE,QAAMwB,WAAUzB,QAAGa,aAAHb,mBAAcC,QAAQa,SAAUC,EAAEC;AAClD,MAAI,CAACS,QAAS;AAEdA,UAAQlB,SAASmB,UAAU1B,EAAE;AAC7B,SAAOA,GAAGa,SAAUZ,QAAQa,SAAUC,EAAEC,GAAG;AAC7C;AAEO,IAAMW,YAAY;EACvB5B;EACAwB;AACF;AAEA,IAAA,oBAAeI;;;AC7Ef,OAAA;AAQA,IAAMC,aAAaC,OAAO,YAAY;AAItC,IAAMC,eAAe;AAErB,SAASC,UAAWC,IAAiBC,OAAe;AAClDD,KAAGE,MAAMH,YAAYE;AACrBD,KAAGE,MAAMC,kBAAkBF;AAC7B;AAiBA,SAASG,aAAcC,GAAwC;AAC7D,SAAOA,EAAEC,YAAYC,SAAS;AAChC;AAEA,SAASC,gBAAiBH,GAA2C;AACnE,SAAOA,EAAEC,YAAYC,SAAS;AAChC;AAEA,IAAME,YAAY,SAChBJ,GACAL,IAEG;AA/CL;AA+CK,MADHC,QAAoBS,UAAAC,SAAA,KAAAD,UAAA,CAAA,MAAAE,SAAAF,UAAA,CAAA,IAAG,CAAC;AAExB,MAAIG,SAAS;AACb,MAAIC,SAAS;AAEb,MAAI,CAACN,gBAAgBH,CAAC,GAAG;AACvB,UAAMU,SAASf,GAAGgB,sBAAsB;AACxC,UAAMC,SAASb,aAAaC,CAAC,IAAIA,EAAEa,QAAQb,EAAEa,QAAQP,SAAS,CAAC,IAAIN;AAEnEQ,aAASI,OAAOE,UAAUJ,OAAOK;AACjCN,aAASG,OAAOI,UAAUN,OAAOO;EACnC;AAEA,MAAIC,SAAS;AACb,MAAIC,QAAQ;AACZ,OAAIxB,QAAGyB,YAAHzB,mBAAY0B,QAAQ;AACtBF,YAAQ;AACRD,aAASvB,GAAG2B,cAAc;AAC1BJ,aAAStB,MAAM2B,SAASL,SAASA,SAASM,KAAKC,MAAMjB,SAASU,WAAW,KAAKT,SAASS,WAAW,CAAC,IAAI;EACzG,OAAO;AACLA,aAASM,KAAKC,KAAK9B,GAAG2B,eAAe,IAAI3B,GAAG+B,gBAAgB,CAAC,IAAI;EACnE;AAEA,QAAMC,UAAU,IAAIhC,GAAG2B,cAAeJ,SAAS,KAAM,CAAC;AACtD,QAAMU,UAAU,IAAIjC,GAAG+B,eAAgBR,SAAS,KAAM,CAAC;AAEvD,QAAMW,IAAIjC,MAAM2B,SAASI,UAAU,GAAGnB,SAASU,MAAM;AACrD,QAAMY,IAAIlC,MAAM2B,SAASK,UAAU,GAAGnB,SAASS,MAAM;AAErD,SAAO;IAAEA;IAAQC;IAAOU;IAAGC;IAAGH;IAASC;EAAQ;AACjD;AAEA,IAAMG,UAAU;;EAEdC,KACEhC,GACAL,IAEA;AApFJ;AAoFI,QADAC,QAAoBS,UAAAC,SAAA,KAAAD,UAAA,CAAA,MAAAE,SAAAF,UAAA,CAAA,IAAG,CAAC;AAExB,QAAI,GAACV,8BAAIyB,YAAJzB,mBAAasC,UAAS;AACzB;IACF;AAEA,UAAMC,YAAYC,SAASC,cAAc,MAAM;AAC/C,UAAMC,YAAYF,SAASC,cAAc,MAAM;AAE/CF,cAAUI,YAAYD,SAAS;AAC/BH,cAAUK,YAAY;AAEtB,QAAI3C,MAAM4C,OAAO;AACfN,gBAAUK,aAAa,IAAI3C,MAAM4C,KAAK;IACxC;AAEA,UAAM;MAAEtB;MAAQC;MAAOU;MAAGC;MAAGH;MAASC;IAAQ,IAAIxB,UAAUJ,GAAGL,IAAIC,KAAK;AAExE,UAAM6C,OAAO,GAAGvB,SAAS,CAAC;AAC1BmB,cAAUE,YAAY;AACtBF,cAAUxC,MAAM6C,QAAQD;AACxBJ,cAAUxC,MAAM8C,SAASF;AAEzB9C,OAAG2C,YAAYJ,SAAS;AAExB,UAAMU,YAAWC,OAAOC,iBAAiBnD,EAAE;AAC3C,QAAIiD,aAAYA,UAASG,aAAa,UAAU;AAC9CpD,SAAGE,MAAMkD,WAAW;AACpBpD,SAAGqD,QAAQC,mBAAmB;IAChC;AAEAZ,cAAUa,UAAUC,IAAI,4BAA4B;AACpDd,cAAUa,UAAUC,IAAI,8BAA8B;AACtDzD,cAAU2C,WAAW,aAAaR,CAAC,KAAKC,CAAC,aAAaX,KAAK,IAAIA,KAAK,IAAIA,KAAK,GAAG;AAChFkB,cAAUW,QAAQI,YAAYC,OAAOC,YAAYC,IAAI,CAAC;AAEtDC,0BAAsB,MAAM;AAC1BA,4BAAsB,MAAM;AAC1BnB,kBAAUa,UAAUO,OAAO,4BAA4B;AACvDpB,kBAAUa,UAAUC,IAAI,yBAAyB;AACjDzD,kBAAU2C,WAAW,aAAaV,OAAO,KAAKC,OAAO,kBAAkB;MACzE,CAAC;IACH,CAAC;EACH;EAEA8B,KAAM/D,IAAwB;AAhIhC;AAiII,QAAI,GAACA,8BAAIyB,YAAJzB,mBAAasC,SAAS;AAE3B,UAAMF,WAAUpC,GAAGgE,uBAAuB,qBAAqB;AAE/D,QAAI5B,SAAQzB,WAAW,EAAG;AAC1B,UAAM+B,YAAYN,SAAQA,SAAQzB,SAAS,CAAC;AAE5C,QAAI+B,UAAUW,QAAQY,SAAU;QAC3BvB,WAAUW,QAAQY,WAAW;AAElC,UAAMC,OAAOP,YAAYC,IAAI,IAAIO,OAAOzB,UAAUW,QAAQI,SAAS;AACnE,UAAMW,QAAQvC,KAAKwC,IAAI,MAAMH,MAAM,CAAC;AAEpCI,eAAW,MAAM;AACf5B,gBAAUa,UAAUO,OAAO,yBAAyB;AACpDpB,gBAAUa,UAAUC,IAAI,0BAA0B;AAElDc,iBAAW,MAAM;AAlJvB,YAAAC;AAmJQ,cAAMnC,WAAUpC,GAAGgE,uBAAuB,qBAAqB;AAC/D,YAAI5B,SAAQzB,WAAW,KAAKX,GAAGqD,QAAQC,kBAAkB;AACvDtD,aAAGE,MAAMkD,WAAWpD,GAAGqD,QAAQC;AAC/B,iBAAOtD,GAAGqD,QAAQC;QACpB;AAEA,cAAIZ,MAAAA,UAAU8B,eAAV9B,gBAAAA,IAAsB8B,gBAAexE,GAAIA,IAAGyE,YAAY/B,UAAU8B,UAAU;MAClF,GAAG,GAAG;IACR,GAAGJ,KAAK;EACV;AACF;AAEA,SAASM,gBAAiBzE,OAA2B;AACnD,SAAO,OAAOA,UAAU,eAAe,CAAC,CAACA;AAC3C;AAEA,SAAS0E,WAAYtE,GAAuB;AAC1C,QAAMJ,QAAuB,CAAC;AAC9B,QAAM2E,UAAUvE,EAAEwE;AAElB,MAAI,EAACD,mCAASnD,YAAWmD,QAAQnD,QAAQqD,WAAWzE,EAAET,UAAU,EAAG;AAGnES,IAAET,UAAU,IAAI;AAEhB,MAAIQ,aAAaC,CAAC,GAAG;AACnBuE,YAAQnD,QAAQqD,UAAU;AAC1BF,YAAQnD,QAAQsD,UAAU;EAC5B,OAAO;AAKL,QAAIH,QAAQnD,QAAQsD,QAAS;EAC/B;AAEA9E,QAAM2B,SAASgD,QAAQnD,QAAQuD,YAAYxE,gBAAgBH,CAAC;AAC5D,MAAIuE,QAAQnD,QAAQoB,OAAO;AACzB5C,UAAM4C,QAAQ+B,QAAQnD,QAAQoB;EAChC;AAEA,MAAIzC,aAAaC,CAAC,GAAG;AAEnB,QAAIuE,QAAQnD,QAAQwD,gBAAiB;AAErCL,YAAQnD,QAAQwD,kBAAkB,MAAM;AACtC7C,cAAQC,KAAKhC,GAAGuE,SAAS3E,KAAK;IAChC;AACA2E,YAAQnD,QAAQyD,YAAYhC,OAAOoB,WAAW,MAAM;AAnMxD;AAoMM,WAAIM,wCAASnD,YAATmD,mBAAkBK,iBAAiB;AACrCL,gBAAQnD,QAAQwD,gBAAgB;AAChCL,gBAAQnD,QAAQwD,kBAAkB;MACpC;IACF,GAAGnF,YAAY;EACjB,OAAO;AACLsC,YAAQC,KAAKhC,GAAGuE,SAAS3E,KAAK;EAChC;AACF;AAEA,SAASkF,WAAY9E,GAAuB;AAC1CA,IAAET,UAAU,IAAI;AAClB;AAEA,SAASwF,WAAY/E,GAAU;AAC7B,QAAMuE,UAAUvE,EAAEwE;AAClB,MAAI,EAACD,mCAASnD,SAAS;AAEvByB,SAAOmC,aAAaT,QAAQnD,QAAQyD,SAAS;AAI7C,MAAI7E,EAAEiF,SAAS,cAAcV,QAAQnD,QAAQwD,iBAAiB;AAC5DL,YAAQnD,QAAQwD,gBAAgB;AAChCL,YAAQnD,QAAQwD,kBAAkB;AAGlCL,YAAQnD,QAAQyD,YAAYhC,OAAOoB,WAAW,MAAM;AAClDc,iBAAW/E,CAAC;IACd,CAAC;AACD;EACF;AAEA6C,SAAOoB,WAAW,MAAM;AACtB,QAAIM,QAAQnD,SAAS;AACnBmD,cAAQnD,QAAQqD,UAAU;IAC5B;EACF,CAAC;AACD1C,UAAQ2B,KAAKa,OAAO;AACtB;AAEA,SAASW,iBAAkBlF,GAA4B;AACrD,QAAMuE,UAAUvE,EAAEwE;AAElB,MAAI,EAACD,mCAASnD,SAAS;AAEvB,MAAImD,QAAQnD,QAAQwD,iBAAiB;AACnCL,YAAQnD,QAAQwD,kBAAkB;EACpC;AAEA/B,SAAOmC,aAAaT,QAAQnD,QAAQyD,SAAS;AAC/C;AAEA,IAAIM,iBAAiB;AAErB,SAASC,mBAAoBpF,GAAkB;AAC7C,MAAI,CAACmF,mBAAmBnF,EAAEqF,YAAYC,SAASC,SAASvF,EAAEqF,YAAYC,SAASE,QAAQ;AACrFL,qBAAiB;AACjBb,eAAWtE,CAAC;EACd;AACF;AAEA,SAASyF,mBAAoBzF,GAAkB;AAC7CmF,mBAAiB;AACjBJ,aAAW/E,CAAC;AACd;AAEA,SAAS0F,gBAAiB1F,GAAe;AACvC,MAAImF,gBAAgB;AAClBA,qBAAiB;AACjBJ,eAAW/E,CAAC;EACd;AACF;AAEA,SAAS2F,aAAchG,IAAiBiG,SAAiCC,YAAqB;AAC5F,QAAM;IAAEjG;IAAOkG;EAAU,IAAIF;AAC7B,QAAM3D,UAAUoC,gBAAgBzE,KAAK;AACrC,MAAI,CAACqC,SAAS;AACZF,YAAQ2B,KAAK/D,EAAE;EACjB;AAEAA,KAAGyB,UAAUzB,GAAGyB,WAAW,CAAC;AAC5BzB,KAAGyB,QAAQa,UAAUA;AACrBtC,KAAGyB,QAAQuD,WAAWmB,UAAUvE;AAChC5B,KAAGyB,QAAQC,SAASyE,UAAUzE;AAC9B,MAAI0E,SAASnG,KAAK,KAAKA,MAAM4C,OAAO;AAClC7C,OAAGyB,QAAQoB,QAAQ5C,MAAM4C;EAC3B;AAEA,MAAIP,WAAW,CAAC4D,YAAY;AAC1B,QAAIC,UAAUE,MAAM;AAClBrG,SAAGsG,iBAAiB,cAAcnB,YAAY;QAAEoB,SAAS;MAAK,CAAC;AAC/DvG,SAAGsG,iBAAiB,aAAanB,UAAU;AAC3C;IACF;AAEAnF,OAAGsG,iBAAiB,cAAc3B,YAAY;MAAE4B,SAAS;IAAK,CAAC;AAC/DvG,OAAGsG,iBAAiB,YAAYlB,YAAY;MAAEmB,SAAS;IAAK,CAAC;AAC7DvG,OAAGsG,iBAAiB,aAAaf,kBAAkB;MAAEgB,SAAS;IAAK,CAAC;AACpEvG,OAAGsG,iBAAiB,eAAelB,UAAU;AAE7CpF,OAAGsG,iBAAiB,aAAa3B,UAAU;AAC3C3E,OAAGsG,iBAAiB,WAAWlB,UAAU;AACzCpF,OAAGsG,iBAAiB,cAAclB,UAAU;AAE5CpF,OAAGsG,iBAAiB,WAAWb,kBAAkB;AACjDzF,OAAGsG,iBAAiB,SAASR,kBAAkB;AAE/C9F,OAAGsG,iBAAiB,QAAQP,eAAe;AAG3C/F,OAAGsG,iBAAiB,aAAalB,YAAY;MAAEmB,SAAS;IAAK,CAAC;EAChE,WAAW,CAACjE,WAAW4D,YAAY;AACjCM,oBAAgBxG,EAAE;EACpB;AACF;AAEA,SAASwG,gBAAiBxG,IAAiB;AACzCA,KAAGyG,oBAAoB,aAAa9B,UAAU;AAC9C3E,KAAGyG,oBAAoB,cAAc9B,UAAU;AAC/C3E,KAAGyG,oBAAoB,YAAYrB,UAAU;AAC7CpF,KAAGyG,oBAAoB,aAAalB,gBAAgB;AACpDvF,KAAGyG,oBAAoB,eAAerB,UAAU;AAChDpF,KAAGyG,oBAAoB,WAAWrB,UAAU;AAC5CpF,KAAGyG,oBAAoB,cAAcrB,UAAU;AAC/CpF,KAAGyG,oBAAoB,WAAWhB,kBAAkB;AACpDzF,KAAGyG,oBAAoB,SAASX,kBAAkB;AAClD9F,KAAGyG,oBAAoB,aAAarB,UAAU;AAC9CpF,KAAGyG,oBAAoB,QAAQV,eAAe;AAChD;AAEA,SAASW,SAAS1G,IAAiBiG,SAAiC;AAClED,eAAahG,IAAIiG,SAAS,KAAK;AACjC;AAEA,SAASU,WAAW3G,IAAiB;AACnC,SAAOA,GAAGyB;AACV+E,kBAAgBxG,EAAE;AACpB;AAEA,SAAS4G,QAAS5G,IAAiBiG,SAAiC;AAClE,MAAIA,QAAQhG,UAAUgG,QAAQY,UAAU;AACtC;EACF;AAEA,QAAMX,aAAaxB,gBAAgBuB,QAAQY,QAAQ;AACnDb,eAAahG,IAAIiG,SAASC,UAAU;AACtC;AAEO,IAAMY,SAAS;EACpBJ,SAAAA;EACAC,WAAAA;EACAC;AACF;AAEA,IAAA,iBAAeE;;;AC/Uf,SAASC,qBAAsB;AAC7B,SAAO;AACT;AAEA,SAASC,WAAYC,GAAeC,IAAiBC,SAAgD;AAKnG,MAAI,CAACF,KAAKG,cAAcH,GAAGE,OAAO,MAAM,MAAO,QAAO;AAKtD,QAAME,OAAOC,aAAaJ,EAAE;AAC5B,MACE,OAAOK,eAAe,eACtBF,gBAAgBE,cAChBF,KAAKG,SAASP,EAAEQ,OAChB,QAAO;AAIT,QAAMC,YAAa,OAAOP,QAAQQ,UAAU,YAAYR,QAAQQ,MAAMC,YAAa,MAAM,CAAA,IAAK;AAE9FF,WAASG,KAAKX,EAAE;AAOhB,SAAO,CAACQ,SAASI,KAAKZ,CAAAA,QAAMA,OAAAA,gBAAAA,IAAIa,SAASd,EAAEQ,OAAe;AAC5D;AAEA,SAASL,cAAeH,GAAeE,SAAuD;AAC5F,QAAMa,WAAY,OAAOb,QAAQQ,UAAU,YAAYR,QAAQQ,MAAMM,oBAAqBlB;AAE1F,SAAOiB,SAASf,CAAC;AACnB;AAEA,SAASiB,UAAWjB,GAAeC,IAAiBC,SAAuC;AACzF,QAAMgB,UAAU,OAAOhB,QAAQQ,UAAU,aAAaR,QAAQQ,QAAQR,QAAQQ,MAAMQ;AAGpFlB,IAAEmB,eAAenB,EAAEQ;AAEnBP,KAAGmB,cAAeC,2BAA2BtB,WAAWC,GAAGC,IAAIC,OAAO,KAAKoB,WAAW,MAAM;AAC1FnB,kBAAcH,GAAGE,OAAO,KAAKgB,WAAWA,QAAQlB,CAAC;EACnD,GAAG,CAAC;AACN;AAEA,SAASuB,aAActB,IAAiBuB,UAA0B;AAChE,QAAMpB,OAAOC,aAAaJ,EAAE;AAE5BuB,WAASC,QAAQ;AAEjB,MAAI,OAAOnB,eAAe,eAAeF,gBAAgBE,YAAY;AACnEkB,aAASpB,IAAI;EACf;AACF;AAEO,IAAMsB,eAAe;;;;;;EAM1BC,QAAS1B,IAAiBC,SAAuC;AAC/D,UAAM0B,UAAW5B,OAAaiB,UAAUjB,GAAiBC,IAAIC,OAAO;AACpE,UAAM2B,cAAe7B,OAAa;AAChCC,SAAGmB,cAAeC,0BAA0BtB,WAAWC,GAAiBC,IAAIC,OAAO;IACrF;AAEAqB,iBAAatB,IAAK6B,SAAqB;AACrCA,UAAIC,iBAAiB,SAASH,SAAS,IAAI;AAC3CE,UAAIC,iBAAiB,aAAaF,aAAa,IAAI;IACrD,CAAC;AACD,QAAI,CAAC5B,GAAGmB,eAAe;AACrBnB,SAAGmB,gBAAgB;QACjBC,yBAAyB;MAC3B;IACF;AAEApB,OAAGmB,cAAclB,QAAQ8B,SAAUC,EAAEC,GAAG,IAAI;MAC1CN;MACAC;IACF;EACF;EAEAM,cAAelC,IAAiBC,SAAuC;AACrE,QAAI,CAACD,GAAGmB,cAAe;AAEvBG,iBAAatB,IAAK6B,SAAqB;AA7G3C;AA8GM,UAAI,CAACA,OAAO,GAAC7B,QAAGmB,kBAAHnB,mBAAmBC,QAAQ8B,SAAUC,EAAEC,MAAM;AAE1D,YAAM;QAAEN;QAASC;MAAY,IAAI5B,GAAGmB,cAAclB,QAAQ8B,SAAUC,EAAEC,GAAG;AAEzEJ,UAAIM,oBAAoB,SAASR,SAAS,IAAI;AAC9CE,UAAIM,oBAAoB,aAAaP,aAAa,IAAI;IACxD,CAAC;AAED,WAAO5B,GAAGmB,cAAclB,QAAQ8B,SAAUC,EAAEC,GAAG;EACjD;AACF;AAEA,IAAA,wBAAeR;;;ACzHf,OAAA;;;ACYO,SAASW,kBAAmBC,OAAqBC,QAAsB;AAC5E,SAAO;IACLC,GAAGF,MAAME,IAAID,OAAOC;IACpBC,GAAGH,MAAMG,IAAIF,OAAOE;EACtB;AACF;AAWO,SAASC,UAA4BC,GAAMC,GAAM;AACtD,SAAO;IACLC,GAAGF,EAAEE,IAAID,EAAEC;IACXC,GAAGH,EAAEG,IAAIF,EAAEE;EACb;AACF;AAGO,SAASC,cAAeC,QAAsBC,KAAyB;AAC5E,MAAID,OAAOE,SAAS,SAASF,OAAOE,SAAS,UAAU;AACrD,UAAM;MAAEA;MAAMC;IAAM,IAAIH;AAExB,UAAMH,IACJM,UAAU,SAAS,IACjBA,UAAU,WAAWF,IAAIG,QAAQ,IACjCD,UAAU,UAAUF,IAAIG,QACxBD;AACJ,UAAML,IACJI,SAAS,QAAQ,IACfA,SAAS,WAAWD,IAAII,SACxBH;AAEJ,WAAOI,kBAAkB;MAAET;MAAGC;IAAE,GAAmBG,GAAG;EACxD,WAAWD,OAAOE,SAAS,UAAUF,OAAOE,SAAS,SAAS;AAC5D,UAAM;MAAEA;MAAMC;IAAM,IAAIH;AAExB,UAAMH,IACJK,SAAS,SAAS,IAChBA,SAAS,UAAUD,IAAIG,QACvBF;AACJ,UAAMJ,IACJK,UAAU,QAAQ,IAChBA,UAAU,WAAWF,IAAII,SAAS,IAClCF,UAAU,WAAWF,IAAII,SACzBF;AAEJ,WAAOG,kBAAkB;MAAET;MAAGC;IAAE,GAAmBG,GAAG;EACxD;AAEA,SAAOK,kBAAkB;IACvBT,GAAGI,IAAIG,QAAQ;IACfN,GAAGG,IAAII,SAAS;EAClB,GAAmBJ,GAAG;AACxB;;;AC7BA,IAAMM,qBAAqB;EACzBC,QAAQC;;EACRC,WAAWC;;AACb;AAaO,IAAMC,4BAA4BC,aAAa;EACpDC,kBAAkB;IAChBC,MAAM,CAACC,QAAQC,QAAQ;IACvBC,SAAS;IACTC,WAAYC,SAAa,OAAOA,QAAQ,cAAcA,OAAOb;EAC/D;EACAc,UAAU;IACRN,MAAMC;IACNE,SAAS;EACX;EACAI,QAAQ;IACNP,MAAMC;IACNE,SAAS;EACX;EACAK,QAAQ,CAACC,QAAQR,QAAQS,KAAK;AAChC,GAAG,8BAA8B;AAE1B,SAASC,sBACdC,OACAC,MACA;AACA,QAAMC,gBAAgBC,IAAI,CAAC,CAAC;AAC5B,QAAMC,iBAAiBD,IAAwB;AAE/C,MAAIE,YAAY;AACdC,mBAAe,MAAM,CAAC,EAAEL,KAAKM,SAASC,SAASR,MAAMb,mBAAmBsB,WAAS;AApFrF;AAqFMC,YAAM,MAAMV,MAAMb,kBAAkBsB,KAAK;AACzCE,qBAAe,MAAM;AACnBC,eAAOC,oBAAoB,UAAUC,QAAQ;AAC7CC,yDAAgBF,oBAAoB,UAAUG;AAC9CD,yDAAgBF,oBAAoB,UAAUI;AAC9Cb,uBAAeI,QAAQU;MACzB,CAAC;AAEDN,aAAOO,iBAAiB,UAAUL,UAAU;QAAEM,SAAS;MAAK,CAAC;AAC7DL,uDAAgBI,iBAAiB,UAAUH,gBAAgB;QAAEI,SAAS;MAAK;AAC3EL,uDAAgBI,iBAAiB,UAAUF,gBAAgB;QAAEG,SAAS;MAAK;AAE3E,UAAI,OAAOpB,MAAMb,qBAAqB,YAAY;AAChDiB,uBAAeI,SAAQR,WAAMb,iBAAiBc,MAAMD,OAAOE,aAAa,MAAjDF,mBAAoDI;MAC7E,OAAO;AACLA,uBAAeI,SAAQ5B,wBAAmBoB,MAAMb,gBAAgB,EAAEc,MAAMD,OAAOE,aAAa,MAArEtB,mBAAwEwB;MACjG;IACF,CAAC;EACH;AAEA,WAASU,SAAUO,GAAU;AAzG/B;AA0GIjB,yBAAeI,UAAfJ,wCAAuBiB;EACzB;AAEA,WAASL,eAAgBK,GAAU;AA7GrC;AA8GIjB,yBAAeI,UAAfJ,wCAAuBiB;EACzB;AAEA,WAASJ,eAAgBI,GAAU;AAjHrC;AAkHIjB,yBAAeI,UAAfJ,wCAAuBiB;EACzB;AAEA,SAAO;IACLnB;IACAE;EACF;AACF;AAEA,SAAStB,yBAA0B;AACjC;AAIF,SAASwC,iBAAkBC,IAAiBC,OAAgB;AAgB1D,QAAMC,aAAaC,kBAAkBH,EAAE;AAEvC,MAAIC,OAAO;AACTC,eAAWE,KAAKC,WAAWL,GAAGM,MAAMC,SAAS,CAAC;EAChD,OAAO;AACLL,eAAWE,KAAKC,WAAWL,GAAGM,MAAME,QAAQ,CAAC;EAC/C;AACAN,aAAWO,KAAKJ,WAAWL,GAAGM,MAAMI,OAAO,CAAC;AAQ5C,SAAOR;AACT;AAEA,SAASzC,0BAA2BiB,MAA4BD,OAAsBE,eAA4C;AAChI,QAAMgC,iBAAiBpC,MAAMqC,QAAQlC,KAAKmC,OAAO5B,KAAK,KAAK6B,gBAAgBpC,KAAKmC,OAAO5B,KAAK;AAC5F,MAAI0B,gBAAgB;AAClBI,WAAOC,OAAOrC,cAAcM,OAAO;MACjCgC,UAAU;MACVP,KAAK;MACL,CAAChC,KAAKuB,MAAMhB,QAAQ,UAAU,MAAM,GAAG;IACzC,CAAC;EACH;AAEA,QAAM;IAAEiC;IAAiBC;EAAgB,IAAIC,iBAAiB,MAAM;AAClE,UAAMC,eAAeC,YAAY7C,MAAMN,UAAUO,KAAKuB,MAAMhB,KAAK;AACjE,UAAMsC,eACJ9C,MAAML,WAAW,YAAYiD,eAC3B5C,MAAML,WAAW,SAASoD,SAASH,YAAY,IAC/CC,YAAY7C,MAAML,QAAQM,KAAKuB,MAAMhB,KAAK;AAG9C,QAAIoC,aAAaI,SAASF,aAAaE,QAAQJ,aAAaK,UAAUC,UAAUJ,YAAY,EAAEG,OAAO;AACnG,aAAO;QACLR,iBAAiBU,WAAWP,YAAY;QACxCF,iBAAiBS,WAAWL,YAAY;MAC1C;IACF,OAAO;AACL,aAAO;QACLL,iBAAiBG;QACjBF,iBAAiBI;MACnB;IACF;EACF,CAAC;AAED,QAAM,CAACM,UAAUC,WAAWC,UAAUC,SAAS,IAC5C,CAAC,YAAY,aAAa,YAAY,WAAW,EAAYC,IAAIC,SAAO;AACvE,WAAOC,SAAS,MAAM;AACpB,YAAMjE,MAAMmC,WAAW5B,MAAMyD,GAAG,CAAE;AAClC,aAAOE,MAAMlE,GAAG,IAAImE,WAAWnE;IACjC,CAAC;EACH,CAAC;AAEH,QAAMG,SAAS8D,SAAS,MAAM;AAC5B,QAAI5D,MAAMqC,QAAQnC,MAAMJ,MAAM,GAAG;AAC/B,aAAOI,MAAMJ;IACf;AACA,QAAI,OAAOI,MAAMJ,WAAW,UAAU;AACpC,YAAMA,UAASI,MAAMJ,OAAOiE,MAAM,GAAG,EAAEL,IAAI5B,UAAU;AACrD,UAAIhC,QAAOkE,SAAS,EAAGlE,CAAAA,QAAOmE,KAAK,CAAC;AACpC,aAAOnE;IACT;AACA,WAAO,OAAOI,MAAMJ,WAAW,WAAW,CAACI,MAAMJ,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC;EACrE,CAAC;AAED,MAAIoE,UAAU;AACd,MAAIC,YAAY;AAChB,QAAMC,UAAU,IAAIC,eAA2C,CAAC;AAChE,QAAMC,WAAW,IAAIC,eAAe,MAAM;AACxC,QAAI,CAACL,QAAS;AAGdM,0BAAsBC,aAAW;AAC/B,UAAIA,YAAYN,UAAWC,SAAQM,MAAM;AACzCF,4BAAsBG,gBAAc;AAClCR,oBAAYQ;MACd,CAAC;IACH,CAAC;AAED,QAAIP,QAAQQ,QAAQ;AAClB,YAAMC,SAAST,QAAQS,OAAO;AAC9B,UAAIC,UAAUD,OAAOE,GAAG,EAAE,GAAGF,OAAOE,GAAG,EAAE,CAAC,GAAG;AAE3C;MACF;IACF;AAEA,UAAMC,SAAS1E,eAAe;AAC9B,QAAI0E,OAAQZ,SAAQH,KAAKe,OAAOZ,OAAO;EACzC,CAAC;AAEDxD,QAAM,CAACT,KAAKmC,QAAQnC,KAAK8E,SAAS,GAAG,CAAAC,MAAAC,UAA0D;AAAA,QAAzD,CAACC,WAAWC,YAAY,IAACH;AAAA,QAAE,CAACI,WAAWC,YAAY,IAACJ;AACxF,QAAIG,aAAa,CAACtF,MAAMqC,QAAQiD,SAAS,EAAGhB,UAASkB,UAAUF,SAAS;AACxE,QAAIF,aAAa,CAACpF,MAAMqC,QAAQ+C,SAAS,EAAGd,UAASJ,QAAQkB,SAAS;AAEtE,QAAIG,aAAcjB,UAASkB,UAAUD,YAAY;AACjD,QAAIF,aAAcf,UAASJ,QAAQmB,YAAY;EACjD,GAAG;IACDI,WAAW;EACb,CAAC;AAED5E,iBAAe,MAAM;AACnByD,aAASoB,WAAW;EACtB,CAAC;AAED,MAAIC,YAAY,IAAIC,IAAI;IAAE/D,GAAG;IAAGK,GAAG;IAAG2D,OAAO;IAAGC,QAAQ;EAAE,CAAC;AAG3D,WAASxF,iBAAkB;AACzB4D,cAAU;AACVM,0BAAsB,MAAMN,UAAU,IAAI;AAE1C,QAAI,CAAC/D,KAAKmC,OAAO5B,SAAS,CAACP,KAAK8E,UAAUvE,MAAO;AAEjD,QACEV,MAAMqC,QAAQlC,KAAKmC,OAAO5B,KAAK,KAC/BP,KAAKmC,OAAO5B,MAAMqF,gBAClB5F,KAAKmC,OAAO5B,MAAMsF,eAAe,EAAEhC,QACnC;AACA2B,kBAAYM,aAAa9F,KAAKmC,OAAO5B,KAAK;IAC5C;AAEA,UAAMiB,aAAaH,iBAAiBrB,KAAK8E,UAAUvE,OAAOP,KAAKuB,MAAMhB,KAAK;AAC1E,UAAMwF,gBAAgBC,iBAAiBhG,KAAK8E,UAAUvE,KAAK;AAC3D,UAAM0F,iBAAiB;AAEvB,QAAI,CAACF,cAAclC,QAAQ;AACzBkC,oBAAcjC,KAAKoC,SAASC,eAAe;AAC3C,UAAI,EAAEnG,KAAK8E,UAAUvE,MAAMqB,MAAMI,OAAOhC,KAAK8E,UAAUvE,MAAMqB,MAAME,OAAO;AACxEN,mBAAWE,KAAKC,WAAWuE,SAASC,gBAAgBvE,MAAMwE,iBAAiB,mBAAmB,KAAK,CAAC;AACpG5E,mBAAWO,KAAKJ,WAAWuE,SAASC,gBAAgBvE,MAAMwE,iBAAiB,mBAAmB,KAAK,CAAC;MACtG;IACF;AAEA,UAAMC,WAAWN,cAAcO,OAAY,CAACC,KAAsBjF,OAAO;AACvE,YAAMkF,YAAYC,cAAcnF,EAAE;AAElC,UAAIiF,KAAK;AACP,eAAO,IAAId,IAAI;UACb/D,GAAGgF,KAAKC,IAAIJ,IAAIzE,MAAM0E,UAAU1E,IAAI;UACpCC,GAAG2E,KAAKC,IAAIJ,IAAIvE,KAAKwE,UAAUxE,GAAG;UAClC0D,OAAOgB,KAAKE,IAAIL,IAAI1E,OAAO2E,UAAU3E,KAAK,IAAI6E,KAAKC,IAAIJ,IAAIzE,MAAM0E,UAAU1E,IAAI;UAC/E6D,QAAQe,KAAKE,IAAIL,IAAIM,QAAQL,UAAUK,MAAM,IAAIH,KAAKC,IAAIJ,IAAIvE,KAAKwE,UAAUxE,GAAG;QAClF,CAAC;MACH;AACA,aAAOwE;IACT,GAAGvF,MAAU;AACboF,aAAS3E,KAAKuE;AACdI,aAAStE,KAAKkE;AACdI,aAASX,SAASO,iBAAiB;AACnCI,aAASV,UAAUM,iBAAiB;AAEpC,QAAIa,YAAY;MACdC,QAAQvE,gBAAgBjC;MACxBb,QAAQ+C,gBAAgBlC;IAC1B;AAEA,aAASyG,cAAeC,YAA8B;AACpD,YAAMV,MAAM,IAAId,IAAIjE,UAAU;AAC9B,YAAM0F,cAAcC,cAAcF,WAAWF,QAAQvB,SAAS;AAC9D,YAAM4B,eAAeD,cAAcF,WAAWvH,QAAQ6G,GAAG;AAEzD,UAAI;QAAE7E,GAAAA;QAAGK,GAAAA;MAAE,IAAIsF,UAAUH,aAAaE,YAAY;AAElD,cAAQH,WAAWF,OAAOhE,MAAI;QAC5B,KAAK;AAAOhB,UAAAA,MAAKpC,OAAOY,MAAM,CAAC;AAAG;QAClC,KAAK;AAAUwB,UAAAA,MAAKpC,OAAOY,MAAM,CAAC;AAAG;QACrC,KAAK;AAAQmB,UAAAA,MAAK/B,OAAOY,MAAM,CAAC;AAAG;QACnC,KAAK;AAASmB,UAAAA,MAAK/B,OAAOY,MAAM,CAAC;AAAG;MACtC;AAEA,cAAQ0G,WAAWF,OAAO/D,OAAK;QAC7B,KAAK;AAAOjB,UAAAA,MAAKpC,OAAOY,MAAM,CAAC;AAAG;QAClC,KAAK;AAAUwB,UAAAA,MAAKpC,OAAOY,MAAM,CAAC;AAAG;QACrC,KAAK;AAAQmB,UAAAA,MAAK/B,OAAOY,MAAM,CAAC;AAAG;QACnC,KAAK;AAASmB,UAAAA,MAAK/B,OAAOY,MAAM,CAAC;AAAG;MACtC;AAEAgG,UAAI7E,KAAKA;AACT6E,UAAIxE,KAAKA;AAETwE,UAAIb,QAAQgB,KAAKE,IAAIL,IAAIb,OAAOrC,SAAS9C,KAAK;AAC9CgG,UAAIZ,SAASe,KAAKE,IAAIL,IAAIZ,QAAQrC,UAAU/C,KAAK;AAEjD,YAAM+G,YAAYC,YAAYhB,KAAKF,QAAQ;AAE3C,aAAO;QAAEiB;QAAW5F,GAAAA;QAAGK,GAAAA;MAAE;IAC3B;AAEA,QAAIL,IAAI;AAAG,QAAIK,IAAI;AACnB,UAAMyF,YAAY;MAAE9F,GAAG;MAAGK,GAAG;IAAE;AAC/B,UAAMkC,WAAU;MAAEvC,GAAG;MAAOK,GAAG;IAAM;AACrC,QAAI0F,SAAS;AACb,WAAO,MAAM;AACX,UAAIA,WAAW,IAAI;AACjBC,qBAAa,qDAAqD;AAClE;MACF;AAEA,YAAM;QAAEhG,GAAGiG;QAAI5F,GAAG6F;QAAIN;MAAU,IAAIN,cAAcF,SAAS;AAE3DpF,WAAKiG;AACL5F,WAAK6F;AAELpG,iBAAWE,KAAKiG;AAChBnG,iBAAWO,KAAK6F;AAGhB;AACE,cAAMC,QAAOC,QAAQhB,UAAUC,MAAM;AACrC,cAAMgB,eAAeT,UAAU5F,EAAEsG,UAAUV,UAAU5F,EAAEuG;AACvD,cAAMC,eAAeZ,UAAUvF,EAAEiG,UAAUV,UAAUvF,EAAEkG;AAEvD,YAAIzH,QAAQ;AACX,SAAC,KAAK,GAAG,EAAE2H,QAAQ3E,SAAO;AACzB,cACGA,QAAQ,OAAOuE,gBAAgB,CAAC9D,SAAQvC,KACxC8B,QAAQ,OAAO0E,gBAAgB,CAACjE,SAAQlC,GACzC;AACA,kBAAMqG,eAAe;cAAErB,QAAQ;gBAAE,GAAGD,UAAUC;cAAO;cAAGrH,QAAQ;gBAAE,GAAGoH,UAAUpH;cAAO;YAAE;AACxF,kBAAM2I,OAAO7E,QAAQ,MACjBqE,UAAS,MAAM5E,YAAYH,WAC3B+E,UAAS,MAAM/E,WAAWG;AAC9BmF,yBAAarB,SAASsB,KAAKD,aAAarB,MAAM;AAC9CqB,yBAAa1I,SAAS2I,KAAKD,aAAa1I,MAAM;AAC9C,kBAAM;cAAE4H,WAAWgB;YAAa,IAAItB,cAAcoB,YAAY;AAC9D,gBACGE,aAAa9E,GAAG,EAAEwE,UAAUV,UAAU9D,GAAG,EAAEwE,UAC1CM,aAAa9E,GAAG,EAAEyE,SAASX,UAAU9D,GAAG,EAAEyE,SAC3CK,aAAa9E,GAAG,EAAEwE,SAASM,aAAa9E,GAAG,EAAEyE,SAC3CX,UAAU9D,GAAG,EAAEwE,SAASV,UAAU9D,GAAG,EAAEyE,SAAS,GACnD;AACAnB,0BAAYsB;AACZ5H,sBAAQyD,SAAQT,GAAG,IAAI;YACzB;UACF;QACF,CAAC;AACD,YAAIhD,MAAO;MACb;AAGA,UAAI8G,UAAU5F,EAAEsG,QAAQ;AACtBtG,aAAK4F,UAAU5F,EAAEsG;AACjBxG,mBAAWE,KAAK4F,UAAU5F,EAAEsG;MAC9B;AACA,UAAIV,UAAU5F,EAAEuG,OAAO;AACrBvG,aAAK4F,UAAU5F,EAAEuG;AACjBzG,mBAAWE,KAAK4F,UAAU5F,EAAEuG;MAC9B;AACA,UAAIX,UAAUvF,EAAEiG,QAAQ;AACtBjG,aAAKuF,UAAUvF,EAAEiG;AACjBxG,mBAAWO,KAAKuF,UAAUvF,EAAEiG;MAC9B;AACA,UAAIV,UAAUvF,EAAEkG,OAAO;AACrBlG,aAAKuF,UAAUvF,EAAEkG;AACjBzG,mBAAWO,KAAKuF,UAAUvF,EAAEkG;MAC9B;AAGA;AACE,cAAMX,aAAYC,YAAY/F,YAAY6E,QAAQ;AAClDmB,kBAAU9F,IAAI2E,SAASX,QAAQ4B,WAAU5F,EAAEsG,SAASV,WAAU5F,EAAEuG;AAChET,kBAAUzF,IAAIsE,SAASV,SAAS2B,WAAUvF,EAAEiG,SAASV,WAAUvF,EAAEkG;AAEjEvG,aAAK4F,WAAU5F,EAAEsG;AACjBxG,mBAAWE,KAAK4F,WAAU5F,EAAEsG;AAC5BjG,aAAKuF,WAAUvF,EAAEiG;AACjBxG,mBAAWO,KAAKuF,WAAUvF,EAAEiG;MAC9B;AAEA;IACF;AAEA,UAAMH,OAAOC,QAAQhB,UAAUC,MAAM;AAErC1E,WAAOC,OAAOrC,cAAcM,OAAO;MACjC,6BAA6B,GAAGuG,UAAUC,OAAOhE,IAAI,IAAI+D,UAAUC,OAAO/D,KAAK;MAC/EuF,iBAAiB,GAAGzB,UAAUpH,OAAOqD,IAAI,IAAI+D,UAAUpH,OAAOsD,KAAK;;MAEnEhB,KAAKwG,cAAcC,WAAW1G,CAAC,CAAC;MAChCD,MAAM9B,KAAKuB,MAAMhB,QAAQU,SAAYuH,cAAcC,WAAW/G,CAAC,CAAC;MAChEG,OAAO7B,KAAKuB,MAAMhB,QAAQiI,cAAcC,WAAW,CAAC/G,CAAC,CAAC,IAAIT;MAC1DkC,UAAUqF,cAAcX,SAAS,MAAMnB,KAAKE,IAAIzD,SAAS5C,OAAOiF,UAAUE,KAAK,IAAIvC,SAAS5C,KAAK;MACjG8C,UAAUmF,cAAcE,UAAUC,MAAMnB,UAAU9F,GAAGyB,SAAS5C,UAAUoD,WAAW,IAAIR,SAAS5C,OAAO8C,SAAS9C,KAAK,CAAC,CAAC;MACvH+C,WAAWkF,cAAcE,UAAUC,MAAMnB,UAAUzF,GAAGqB,UAAU7C,UAAUoD,WAAW,IAAIP,UAAU7C,OAAO+C,UAAU/C,KAAK,CAAC,CAAC;IAC7H,CAAC;AAED,WAAO;MACLiH;MACAhG;MACAyC,SAAAA;IACF;EACF;AAEAxD,QACE,MAAM,CACJ+B,gBAAgBjC,OAChBkC,gBAAgBlC,OAChBR,MAAMJ,QACNI,MAAMoD,UACNpD,MAAMqD,WACNrD,MAAMsD,UACNtD,MAAMuD,SAAS,GAEjB,MAAMnD,eAAe,CACvB;AAEAyI,WAAS,MAAM;AACb,UAAM/D,SAAS1E,eAAe;AAI9B,QAAI,CAAC0E,OAAQ;AACb,UAAM;MAAE2C;MAAWhG;IAAW,IAAIqD;AAClC,QAAIrD,WAAWmE,SAAS6B,UAAUzF,GAAG;AACnCsC,4BAAsB,MAAM;AAC1BlE,uBAAe;AACfkE,8BAAsB,MAAM;AAC1BlE,yBAAe;QACjB,CAAC;MACH,CAAC;IACH;EACF,CAAC;AAED,SAAO;IAAEA;EAAe;AAC1B;AAEA,SAASsI,WAAYjJ,KAAa;AAChC,SAAOkH,KAAKmC,MAAMrJ,MAAMsJ,gBAAgB,IAAIA;AAC9C;AAEA,SAASJ,UAAWlJ,KAAa;AAC/B,SAAOkH,KAAKqC,KAAKvJ,MAAMsJ,gBAAgB,IAAIA;AAC7C;;;ACjeA,IAAIE,QAAQ;AACZ,IAAMC,SAAS,CAAA;AAMR,SAASC,gBAAiBC,IAAgB;AAC/C,MAAI,CAACH,SAASC,OAAOG,QAAQ;AAC3BH,WAAOI,KAAKF,EAAE;AACdG,QAAI;EACN,OAAO;AACLN,YAAQ;AACRG,OAAG;AACHG,QAAI;EACN;AACF;AAEA,IAAIC,MAAM;AACV,SAASD,MAAO;AACdE,uBAAqBD,GAAG;AACxBA,QAAME,sBAAsB,MAAM;AAChC,UAAMC,QAAQT,OAAOU,MAAM;AAC3B,QAAID,MAAOA,OAAM;AAEjB,QAAIT,OAAOG,OAAQE,KAAI;QAClBN,SAAQ;EACf,CAAC;AACH;;;ACVA,IAAMY,mBAAmB;EACvBC,MAAM;EACNC,OAAOC;EACPC,OAAOC;EACPC,YAAYC;AACd;AAOO,IAAMC,0BAA0BC,aAAa;EAClDC,gBAAgB;IACdC,MAAM,CAACC,QAAQC,QAAQ;IACvBC,SAAS;IACTC,WAAYC,SAAa,OAAOA,QAAQ,cAAcA,OAAOhB;EAC/D;AACF,GAAG,4BAA4B;AAExB,SAASiB,oBACdC,OACAC,MACA;AACA,MAAI,CAACC,WAAY;AAEjB,MAAIC;AACJC,cAAY,YAAY;AACtBD,mCAAOE;AAEP,QAAI,EAAEJ,KAAKK,SAASC,SAASP,MAAMR,gBAAiB;AAEpDW,YAAQK,YAAY;AACpB,UAAM,IAAIC,QAAQC,aAAWC,WAAWD,OAAO,CAAC;AAChDP,UAAMS,UAAUT,MAAMU,IAAI,MAAM;AApDpC;AAqDM,UAAI,OAAOb,MAAMR,mBAAmB,YAAY;AAC9CQ,cAAMR,eAAeS,MAAMD,OAAOG,KAAM;MAC1C,OAAO;AACLrB,+BAAiBkB,MAAMR,oBAAvBV,0CAAyCmB,MAAMD,OAAOG;MACxD;IACF,CAAC;EACH,CAAC;AAEDW,iBAAe,MAAM;AACnBX,mCAAOE;EACT,CAAC;AACH;AAEA,SAASpB,oBAAqBgB,MAA0B;AACtD,WAASc,SAAUC,GAAU;AAC3Bf,SAAKK,SAASC,QAAQ;EACxB;AAEAU,aAAWhB,KAAKiB,SAASX,SAASN,KAAKkB,UAAUZ,OAAOQ,QAAQ;AAClE;AAEA,SAAS5B,oBAAqBc,MAA0BD,OAAsB;AA1E9E;AA2EE,QAAMoB,gBAAenB,UAAKoB,KAAKd,UAAVN,mBAAiBmB;AACtC,QAAME,iBAAiB,CAAC,GAAG,oBAAIC,IAAI,CACjC,GAAGC,iBAAiBvB,KAAKiB,SAASX,OAAOP,MAAMyB,YAAYL,eAAeM,MAAS,GACnF,GAAGF,iBAAiBvB,KAAKkB,UAAUZ,OAAOP,MAAMyB,YAAYL,eAAeM,MAAS,CAAC,CACtF,CAAC,EAAEC,OAAOC,QAAM,CAACA,GAAGC,UAAUC,SAAS,0BAA0B,CAAC;AACnE,QAAMC,iBAAiBC,OAAOC,aAAaC,SAASC,gBAAgBC;AAEpE,QAAMC,oBAAoBT,QAAMU,aAAaV,EAAE,KAAKA,IAAIR,gBAAgBc,SAASC,eAAe;AAChG,MAAIE,kBAAkB;AACpBpC,SAAKoB,KAAKd,MAAOsB,UAAUU,IAAI,2BAA2B;EAC5D;AAEAjB,iBAAekB,QAAQ,CAACZ,IAAIa,MAAM;AAChCb,OAAGc,MAAMC,YAAY,qBAAqBC,cAAc,CAAChB,GAAGiB,UAAU,CAAC;AACvEjB,OAAGc,MAAMC,YAAY,qBAAqBC,cAAc,CAAChB,GAAGkB,SAAS,CAAC;AAEtE,QAAIlB,OAAOM,SAASC,iBAAiB;AACnCP,SAAGc,MAAMC,YAAY,wBAAwBC,cAAcb,cAAc,CAAC;IAC5E;AAEAH,OAAGC,UAAUU,IAAI,0BAA0B;EAC7C,CAAC;AAEDzB,iBAAe,MAAM;AACnBQ,mBAAekB,QAAQ,CAACZ,IAAIa,MAAM;AAChC,YAAMM,IAAIC,WAAWpB,GAAGc,MAAMO,iBAAiB,mBAAmB,CAAC;AACnE,YAAMC,IAAIF,WAAWpB,GAAGc,MAAMO,iBAAiB,mBAAmB,CAAC;AAEnE,YAAME,iBAAiBvB,GAAGc,MAAMS;AAEhCvB,SAAGc,MAAMS,iBAAiB;AAC1BvB,SAAGc,MAAMU,eAAe,mBAAmB;AAC3CxB,SAAGc,MAAMU,eAAe,mBAAmB;AAC3CxB,SAAGc,MAAMU,eAAe,sBAAsB;AAC9CxB,SAAGC,UAAUwB,OAAO,0BAA0B;AAE9CzB,SAAGiB,aAAa,CAACE;AACjBnB,SAAGkB,YAAY,CAACI;AAEhBtB,SAAGc,MAAMS,iBAAiBA;IAC5B,CAAC;AACD,QAAId,kBAAkB;AACpBpC,WAAKoB,KAAKd,MAAOsB,UAAUwB,OAAO,2BAA2B;IAC/D;EACF,CAAC;AACH;AAEA,SAAShE,yBAA0BY,MAA0BD,OAAsBG,OAAoB;AACrG,MAAImD,OAAO;AACX,MAAIC,OAAM;AACV,MAAIC,MAAM;AAEV,WAASC,OAAQzC,GAAU;AACzB0C,oBAAgB,MAAM;AAhI1B;AAiIM,YAAMC,QAAQC,YAAYC,IAAI;AAC9B5D,uBAAK6D,gBAAevD,UAApBN,4BAA4Be;AAC5B,YAAM+C,OAAOH,YAAYC,IAAI,IAAIF;AACjCL,aAAOS,QAAQ,MAAO,MAAM;IAC9B,CAAC;EACH;AAEAP,SAAO,OAAOQ,wBAAwB,cAAeC,QAAiBA,GAAG,IAAID,qBAAqB,MAAM;AACtG7D,UAAMU,IAAI,MAAM;AACdI,iBAAWhB,KAAKiB,SAASX,SAASN,KAAKkB,UAAUZ,OAAOS,OAAK;AAC3D,YAAIsC,MAAM;AAKRY,+BAAqBX,IAAG;AACxBA,UAAAA,OAAMY,sBAAsB,MAAM;AAChCZ,YAAAA,OAAMY,sBAAsB,MAAM;AAChCV,qBAAOzC,CAAC;YACV,CAAC;UACH,CAAC;QACH,OAAO;AACLyC,iBAAOzC,CAAC;QACV;MACF,CAAC;IACH,CAAC;EACH,CAAC;AAEDF,iBAAe,MAAM;AACnB,WAAOsD,uBAAuB,eAAeA,mBAAmBZ,GAAG;AACnEU,yBAAqBX,IAAG;EAC1B,CAAC;AACH;AAGA,SAAStC,WAAYW,IAA6Bb,UAA8B;AAC9E,QAAMO,iBAAiB,CAACY,UAAU,GAAGV,iBAAiBI,EAAE,CAAC;AACzDN,iBAAekB,QAAQZ,CAAAA,QAAM;AAC3BA,IAAAA,IAAGyC,iBAAiB,UAAUtD,UAAU;MAAEuD,SAAS;IAAK,CAAC;EAC3D,CAAC;AAEDxD,iBAAe,MAAM;AACnBQ,mBAAekB,QAAQZ,CAAAA,QAAM;AAC3BA,MAAAA,IAAG2C,oBAAoB,UAAUxD,QAAQ;IAC3C,CAAC;EACH,CAAC;AACH;;;ACtKO,IAAMyD,cAAyCC,OAAOC,IAAI,gBAAgB;;;ACC1E,IAAMC,iBAAiBC,aAAa;EACzCC,YAAY,CAACC,QAAQC,MAAM;EAC3BC,WAAW,CAACF,QAAQC,MAAM;AAC5B,GAAG,OAAO;AAEH,SAASE,SAAUC,OAAmBC,IAA+B;AAC1E,MAAIC,aAA2BA,MAAM;EAAC;AAEtC,WAASC,SAAUC,WAAoB;AACrCF;AAEA,UAAMG,QAAQT,OAAOQ,YAAYJ,MAAMF,YAAYE,MAAML,UAAU;AAEnE,WAAO,IAAIW,QAAQC,aAAW;AAC5BL,mBAAaM,MAAMH,OAAO,MAAM;AAC9BJ,iCAAKG;AACLG,gBAAQH,SAAS;MACnB,CAAC;IACH,CAAC;EACH;AAEA,WAASK,eAAgB;AACvB,WAAON,SAAS,IAAI;EACtB;AAEA,WAASO,gBAAiB;AACxB,WAAOP,SAAS,KAAK;EACvB;AAEA,SAAO;IACLD;IACAO;IACAC;EACF;AACF;;;ACMO,IAAMC,qBAAqBC,aAAa;EAC7CC,QAAQ,CAACC,QAAQC,MAAM;EACvBC,WAAW,CAACF,QAAQC,MAAM;EAC1BE,gBAAgB;IACdC,MAAMH;IACNI,SAASA,OAAO,CAAC;EACnB;EAEAC,aAAa;IACXF,MAAMG;IACNF,SAASG;EACX;EACAC,aAAaF;EACbG,aAAa;IACXN,MAAMG;IACNF,SAASG;EACX;EAEAG,qBAAqBJ;EAErB,GAAGK,eAAe;AACpB,GAAG,oBAAoB;AAEhB,SAASC,aACdC,OAAqBC,MAMrB;AAAA,MALA;IAAEC;IAAUC;IAAOC;EAInB,IAACH;AAED,QAAMI,KAAKC,mBAAmB,cAAc;AAC5C,QAAMC,cAAcC,IAAiB;AAErC,MAAIC,YAAY;AAChB,MAAIC,YAAY;AAChB,MAAIC,aAAa;AAEjB,QAAMf,cAAcgB,SAAS,MAAMZ,MAAMJ,eAAgBI,MAAMJ,eAAe,QAAQI,MAAML,WAAY;AACxG,QAAMH,cAAcoB,SAAS,MAAMZ,MAAMR,eAAgBQ,MAAMR,eAAe,QAAQ,CAACQ,MAAML,eAAe,CAACC,YAAYiB,KAAM;AAE/H,QAAM;IAAEC;IAAcC;EAAc,IAAIC,SAAShB,OAAOa,WAAS;AAC/D,QACEA,WACGb,MAAML,eAAec,aACrBb,YAAYiB,SAASH,cACnB,EAAEV,MAAML,eAAeO,SAASW,SAAS,CAACV,MAAMU,QACrD;AACA,UAAIX,SAASW,UAAUA,OAAO;AAC5BF,qBAAa;MACf;AACAT,eAASW,QAAQA;IACnB;EACF,CAAC;AAED,QAAMI,eAAeT,IAA4B;AACjD,QAAMU,kBAAkB;IACtBC,SAAUC,OAAkB;AAC1BA,QAAEC,gBAAgB;AAClBd,kBAAYM,QAASO,EAAEE,iBAAiBF,EAAEnC;AAC1C,UAAI,CAACiB,SAASW,OAAO;AACnBI,qBAAaJ,QAAQ,CAACO,EAAEG,SAASH,EAAEI,OAAO;MAC5C;AACAtB,eAASW,QAAQ,CAACX,SAASW;IAC7B;IACAY,cAAeL,OAAkB;AAnHrC;AAoHM,WAAIA,OAAEM,uBAAFN,mBAAsBO,iBAAkB;AAE5ClB,kBAAY;AACZF,kBAAYM,QAASO,EAAEE,iBAAiBF,EAAEnC;AAC1C6B,mBAAa;IACf;IACAc,cAAeR,OAAkB;AAC/BX,kBAAY;AACZM,oBAAc;IAChB;IACAc,SAAUT,OAAkB;AAC1B,UAAIU,gBAAgBV,EAAEnC,QAAuB,gBAAgB,MAAM,MAAO;AAE1EyB,kBAAY;AACZU,QAAEC,gBAAgB;AAClBd,kBAAYM,QAASO,EAAEE,iBAAiBF,EAAEnC;AAE1C6B,mBAAa;IACf;IACAiB,QAASX,OAAkB;AACzBV,kBAAY;AACZU,QAAEC,gBAAgB;AAElBN,oBAAc;IAChB;EACF;AAEA,QAAMiB,kBAAkBpB,SAAS,MAAM;AACrC,UAAMqB,SAA0C,CAAC;AAEjD,QAAIzC,YAAYqB,OAAO;AACrBoB,aAAOd,UAAUD,gBAAgBC;IACnC;AACA,QAAInB,MAAML,aAAa;AACrBsC,aAAOR,eAAeP,gBAAgBO;AACtCQ,aAAOL,eAAeV,gBAAgBU;IACxC;AACA,QAAIhC,YAAYiB,OAAO;AACrBoB,aAAOJ,UAAUX,gBAAgBW;AACjCI,aAAOF,SAASb,gBAAgBa;IAClC;AAEA,WAAOE;EACT,CAAC;AAED,QAAMC,gBAAgBtB,SAAS,MAAM;AACnC,UAAMqB,SAAwC,CAAC;AAE/C,QAAIjC,MAAML,aAAa;AACrBsC,aAAOR,eAAe,MAAM;AAC1BhB,oBAAY;AACZK,qBAAa;MACf;AACAmB,aAAOL,eAAe,MAAM;AAC1BnB,oBAAY;AACZM,sBAAc;MAChB;IACF;AAEA,QAAInB,YAAYiB,OAAO;AACrBoB,aAAOE,YAAY,MAAM;AACvBzB,oBAAY;AACZI,qBAAa;MACf;AACAmB,aAAOG,aAAa,MAAM;AACxB1B,oBAAY;AACZK,sBAAc;MAChB;IACF;AAEA,QAAIf,MAAMH,qBAAqB;AAC7B,YAAMwC,OAAOC,OAAOC,aAAa,IAAI;AACrCN,aAAOd,UAAU,MAAM;AACrBjB,iBAASW,QAAQ;AACjBwB,qCAAMG;MACR;IACF;AAEA,WAAOP;EACT,CAAC;AAED,QAAMQ,cAAc7B,SAAS,MAAM;AACjC,UAAMqB,SAAwC,CAAC;AAE/C,QAAIjC,MAAML,aAAa;AACrBsC,aAAOR,eAAe,MAAM;AAC1B,YAAId,YAAY;AACdF,sBAAY;AACZE,uBAAa;AACbG,uBAAa;QACf;MACF;AACAmB,aAAOL,eAAe,MAAM;AAC1BnB,oBAAY;AACZM,sBAAc;MAChB;IACF;AAEA,WAAOkB;EACT,CAAC;AAEDS,QAAMvC,OAAOwC,SAAO;AAzNtB;AA0NI,QAAIA,QACD3C,MAAML,eAAe,CAACc,cAAc,CAACb,YAAYiB,SAAS,CAACH,cAC3Dd,YAAYiB,SAAS,CAACH,cAAc,CAACV,MAAML,eAAe,CAACc,eACzD,GAACL,eAAUS,UAAVT,mBAAiBwC,SAASC,SAASC,iBAAgB;AACvD5C,eAASW,QAAQ;IACnB;EACF,CAAC;AAED6B,QAAMxC,UAAUyC,SAAO;AACrB,QAAI,CAACA,KAAK;AACRI,iBAAW,MAAM;AACf9B,qBAAaJ,QAAQnB;MACvB,CAAC;IACH;EACF,GAAG;IAAEsD,OAAO;EAAO,CAAC;AAEpB,QAAMC,eAAeC,YAAY;AACjCC,cAAY,MAAM;AAChB,QAAI,CAACF,aAAapC,MAAO;AAEzBuC,aAAS,MAAM;AACb7C,kBAAYM,QAAQoC,aAAaI;IACnC,CAAC;EACH,CAAC;AAED,QAAMC,YAAYJ,YAAY;AAC9B,QAAMjE,SAAS2B,SAAS,MAAM;AAC5B,QAAIZ,MAAMf,WAAW,YAAYgC,aAAaJ,MAAO,QAAOI,aAAaJ;AACzE,QAAIyC,UAAUzC,MAAO,QAAOyC,UAAUD;AACtC,WAAOE,UAAUvD,MAAMf,QAAQoB,EAAE,KAAKE,YAAYM;EACpD,CAAC;AACD,QAAM2C,WAAW5C,SAAS,MAAM;AAC9B,WAAO6C,MAAMC,QAAQzE,OAAO4B,KAAK,IAC7BnB,SACAT,OAAO4B;EACb,CAAC;AAED,MAAI8C;AACJjB,QAAM,MAAM,CAAC,CAAC1C,MAAMZ,WAAWuD,SAAO;AACpC,QAAIA,OAAOiB,YAAY;AACrBD,cAAQE,YAAY;AACpBF,YAAMG,IAAI,MAAM;AACdC,sBAAc/D,OAAOK,IAAI;UAAEE;UAAayB;QAAgB,CAAC;MAC3D,CAAC;IACH,WAAW2B,OAAO;AAChBA,YAAMK,KAAK;IACb;EACF,GAAG;IAAEhB,OAAO;IAAQiB,WAAW;EAAK,CAAC;AAErCC,iBAAe,MAAM;AACnBP,mCAAOK;EACT,CAAC;AAED,SAAO;IAAEzD;IAAa0C;IAAchE;IAAQuE;IAAUF;IAAWtB;IAAiBE;IAAeO;EAAY;AAC/G;AAEA,SAASsB,cACP/D,OACAK,IAA6B8D,OAE7B;AAAA,MADA;IAAE5D;IAAayB;EAA0F,IAACmC;AAE1GzB,QAAM,MAAM1C,MAAMZ,WAAW,CAACuD,KAAKyB,WAAW;AAC5C,QAAIA,UAAUzB,QAAQyB,QAAQ;AAC5B,YAAMhF,YAAYiF,aAAaD,MAAM;AACrChF,mBAAakF,qBAAqBlF,SAAS;IAC7C;AACA,QAAIuD,KAAK;AACPS,eAAS,MAAMmB,mBAAmB,CAAC;IACrC;EACF,GAAG;IAAEN,WAAW;EAAK,CAAC;AAEtBvB,QAAM,MAAM1C,MAAMX,gBAAgB,MAAM;AACtCkF,uBAAmB;EACrB,CAAC;AAEDL,iBAAe,MAAM;AACnBI,yBAAqB;EACvB,CAAC;AAED,WAASC,qBAAwE;AAAA,QAApDlB,KAAEmB,UAAAC,SAAA,KAAAD,UAAA,CAAA,MAAA9E,SAAA8E,UAAA,CAAA,IAAGH,aAAa;AAAC,QAAEK,SAAMF,UAAAC,SAAA,KAAAD,UAAA,CAAA,MAAA9E,SAAA8E,UAAA,CAAA,IAAGxE,MAAMX;AAC/D,QAAI,CAACgE,GAAI;AAETsB,cAAUtB,IAAIuB,WAAW5C,gBAAgBnB,OAAO6D,MAAM,CAAC;EACzD;AAEA,WAASJ,uBAA0E;AAAA,QAApDjB,KAAEmB,UAAAC,SAAA,KAAAD,UAAA,CAAA,MAAA9E,SAAA8E,UAAA,CAAA,IAAGH,aAAa;AAAC,QAAEK,SAAMF,UAAAC,SAAA,KAAAD,UAAA,CAAA,MAAA9E,SAAA8E,UAAA,CAAA,IAAGxE,MAAMX;AACjE,QAAI,CAACgE,GAAI;AAETwB,gBAAYxB,IAAIuB,WAAW5C,gBAAgBnB,OAAO6D,MAAM,CAAC;EAC3D;AAEA,WAASL,eAAmE;AAAA,QAArDS,WAAQN,UAAAC,SAAA,KAAAD,UAAA,CAAA,MAAA9E,SAAA8E,UAAA,CAAA,IAAGxE,MAAMZ;AACtC,UAAMA,YAAYmE,UAAUuB,UAAUzE,EAAE;AAGxCE,gBAAYM,SAAQzB,uCAAW2F,cAAaC,KAAKC,eAAe7F,YAAYM;AAE5E,WAAOa,YAAYM;EACrB;AACF;AAEA,SAAS0C,UACPuB,UACAzE,IAC8E;AAlUhF;AAmUE,MAAI,CAACyE,SAAU;AAEf,MAAI7F;AACJ,MAAI6F,aAAa,UAAU;AACzB,QAAIzB,MAAKhD,oCAAI6E,UAAJ7E,mBAAW8E,QAAX9E,mBAAgB+E;AACzB,WAAO/B,yBAAIgC,aAAa,sBAAsB;AAC5ChC,WAAKA,GAAG+B;IACV;AACAnG,aAASoE;EACX,WAAW,OAAOyB,aAAa,UAAU;AAEvC7F,aAAS4D,SAASyC,cAAcR,QAAQ;EAC1C,WAAW,SAASA,UAAU;AAE5B7F,aAAS6F,SAASK;EACpB,OAAO;AAELlG,aAAS6F;EACX;AAEA,SAAO7F;AACT;;;ACpUO,SAASsG,SAAUC,QAA0E;AAClG,SAAOC,iBAAiB,MAAM;AAC5B,UAAMC,UAAUC,QAAQH,MAAM;AAC9B,UAAMI,UAAoB,CAAA;AAC1B,UAAMC,SAAwB,CAAC;AAE/B,QAAIH,QAAQI,YAAY;AACtB,UAAIC,WAAWL,QAAQI,UAAU,GAAG;AAClCD,eAAOG,kBAAkBN,QAAQI;AAEjC,YAAI,CAACJ,QAAQO,QAAQC,gBAAgBR,QAAQI,UAAU,GAAG;AACxD,gBAAME,kBAAkBG,WAAWT,QAAQI,UAAU;AACrD,cAAIE,gBAAgBI,KAAK,QAAQJ,gBAAgBI,MAAM,GAAG;AACxD,kBAAMC,YAAYC,cAAcN,eAAe;AAE/CH,mBAAOU,QAAQF;AACfR,mBAAOW,aAAaH;UACtB;QACF;MACF,OAAO;AACLT,gBAAQa,KAAK,MAAMf,QAAQI,UAAU,EAAE;MACzC;IACF;AAEA,QAAIJ,QAAQO,MAAM;AAChB,UAAIF,WAAWL,QAAQO,IAAI,GAAG;AAC5BJ,eAAOU,QAAQb,QAAQO;AACvBJ,eAAOW,aAAad,QAAQO;MAC9B,OAAO;AACLL,gBAAQa,KAAK,QAAQf,QAAQO,IAAI,EAAE;MACrC;IACF;AAEA,WAAO;MAAES,cAAcd;MAASe,aAAad;IAAO;EACtD,CAAC;AACH;AAEO,SAASe,aAAcL,OAAoD;AAChF,QAAM;IACJG,cAAcG;IACdF,aAAaG;EACf,IAAIvB,SAAS,OAAO;IAClBU,MAAMN,QAAQY,KAAK;EACrB,EAAE;AAEF,SAAO;IAAEM;IAAkBC;EAAgB;AAC7C;AAEO,SAASC,mBAAoBR,OAA0D;AAC5F,QAAM;IACJG,cAAcM;IACdL,aAAaM;EACf,IAAI1B,SAAS,OAAO;IAClBO,YAAYH,QAAQY,KAAK;EAC3B,EAAE;AAEF,SAAO;IAAES;IAAwBC;EAAsB;AACzD;;;AC9DO,IAAMC,qBAAqBC,aAAa;EAC7CC,QAAQ,CAACC,QAAQC,MAAM;EACvBC,WAAW,CAACF,QAAQC,MAAM;EAC1BE,UAAU,CAACH,QAAQC,MAAM;EACzBG,WAAW,CAACJ,QAAQC,MAAM;EAC1BI,UAAU,CAACL,QAAQC,MAAM;EACzBK,OAAO,CAACN,QAAQC,MAAM;AACxB,GAAG,WAAW;AAEP,SAASM,aAAcC,OAAuB;AACnD,QAAMC,kBAAkBC,SAAS,MAAM;AACrC,UAAMC,SAA8B,CAAC;AAErC,UAAMZ,SAASa,cAAcJ,MAAMT,MAAM;AACzC,UAAMG,YAAYU,cAAcJ,MAAMN,SAAS;AAC/C,UAAMC,WAAWS,cAAcJ,MAAML,QAAQ;AAC7C,UAAMC,YAAYQ,cAAcJ,MAAMJ,SAAS;AAC/C,UAAMC,WAAWO,cAAcJ,MAAMH,QAAQ;AAC7C,UAAMC,QAAQM,cAAcJ,MAAMF,KAAK;AAEvC,QAAIP,UAAU,KAAMY,QAAOZ,SAASA;AACpC,QAAIG,aAAa,KAAMS,QAAOT,YAAYA;AAC1C,QAAIC,YAAY,KAAMQ,QAAOR,WAAWA;AACxC,QAAIC,aAAa,KAAMO,QAAOP,YAAYA;AAC1C,QAAIC,YAAY,KAAMM,QAAON,WAAWA;AACxC,QAAIC,SAAS,KAAMK,QAAOL,QAAQA;AAElC,WAAOK;EACT,CAAC;AAED,SAAO;IAAEF;EAAgB;AAC3B;;;ACvCO,SAASI,eAAgB;AAC9B,MAAI,CAACC,WAAY,QAAOC,WAAW,KAAK;AAExC,QAAM;IAAEC;EAAI,IAAIC,WAAW;AAE3B,MAAID,KAAK;AACP,UAAME,YAAYH,WAAW,KAAK;AAClCI,cAAU,MAAM;AACdD,gBAAUE,QAAQ;IACpB,CAAC;AACD,WAAOF;EACT,OAAO;AACL,WAAOH,WAAW,IAAI;EACxB;AACF;;;ACdO,IAAMM,gBAAgBC,aAAa;EACxCC,OAAOC;AACT,GAAG,MAAM;AAEF,SAASC,QAASC,OAA2BC,QAAsB;AACxE,QAAMC,WAAWC,WAAW,KAAK;AACjC,QAAMC,aAAaC,MAAM,MAAMH,SAASI,SAASN,MAAMH,SAASI,OAAOK,KAAK;AAE5EC,QAAMN,QAAQ,MAAMC,SAASI,QAAQ,IAAI;AAEzC,WAASE,eAAgB;AACvB,QAAI,CAACR,MAAMH,MAAOK,UAASI,QAAQ;EACrC;AAEA,SAAO;IAAEJ;IAAUE;IAAYI;EAAa;AAC9C;;;ACAO,SAASC,WAA4D;AAC1E,QAAMC,KAAKC,mBAAmB,UAAU;AAExC,SAAOC,SAAS,MAAA;AAzBlB;AAyBwBF,0CAAIG,UAAJH,mBAAWI;GAAM;AACzC;AAEO,SAASC,YAAiC;AA5BjD;AA6BE,UAAOJ,8BAAmB,WAAW,MAA9BA,mBAAiCE,UAAjCF,mBAAwCK;AACjD;AAqBO,SAASC,QAASC,OAAkCC,OAAuC;AAnDlG;AAoDE,QAAMC,aAAaC,wBAAwB,YAAY;AAEvD,QAAMC,SAASC,MAAM,MAAM,CAAC,EAAEL,MAAMM,QAAQN,MAAMO,GAAG;AACrD,QAAMC,cAAcd,SAAS,MAAM;AACjC,YAAOU,iCAAQK,UAASC,SAAST,OAAO,OAAO,KAAKS,SAASV,OAAO,OAAO;EAC7E,CAAC;AAED,MAAI,OAAOE,eAAe,YAAY,EAAE,aAAaA,aAAa;AAChE,UAAMI,QAAOD,MAAM,MAAML,MAAMM,IAAI;AACnC,WAAO;MACLF;MACAI;MACAF,MAAAA;MACAK,WAAWC,SAAS;QAAEN,MAAAA;MAAK,CAAC;IAC9B;EACF;AAGA,QAAMO,aAAaX,WAAWH,QAAQ;IACpCQ,IAAIF,MAAM,MAAML,MAAMO,MAAM,EAAE;IAC9BO,SAAST,MAAM,MAAML,MAAMc,OAAO;EACpC,CAAC;AAED,QAAMC,OAAOrB,SAAS,MAAMM,MAAMO,KAAKM,aAAaG,MAAS;AAC7D,QAAMC,QAAQ1B,SAAS;AACvB,QAAM2B,WAAWxB,SAAS,MAAM;AA7ElC,QAAAyB,KAAAC,KAAA;AA8EI,QAAI,CAACL,KAAKN,MAAO,QAAO;AACxB,QAAI,CAACT,MAAMqB,MAAO,UAAON,MAAAA,KAAKN,MAAMS,aAAXH,gBAAAA,IAAqBN,UAAS;AACvD,QAAI,CAACQ,MAAMR,MAAO,UAAOM,MAAAA,KAAKN,MAAMa,kBAAXP,gBAAAA,IAA0BN,UAAS;AAE5D,aAAOM,UAAKN,MAAMa,kBAAXP,mBAA0BN,UAASc,UAAUR,KAAKN,MAAMQ,MAAMR,MAAMe,OAAOP,MAAMR,MAAMe,KAAK;EACrG,CAAC;AACD,QAAMlB,OAAOZ,SAAS,MAAA;AApFxB,QAAAyB;AAoF8BnB,iBAAMO,MAAKQ,MAAAA,KAAKN,UAALM,gBAAAA,IAAYE,MAAMR,MAAMH,OAAON,MAAMM;GAAI;AAEhF,SAAO;IACLF;IACAI;IACAU;IACAD,QAAOF,UAAKN,UAALM,mBAAYE;IACnBQ,WAAUV,UAAKN,UAALM,mBAAYU;IACtBnB;IACAK,WAAWC,SAAS;MAClBN;MACA,gBAAgBD,MAAM,MAAMa,SAAST,QAAQ,SAASO,MAAS;IACjE,CAAC;EACH;AACF;AAEO,IAAMU,kBAAkBC,aAAa;EAC1CrB,MAAMsB;EACNd,SAASe;EACTtB,IAAI,CAACqB,QAAQE,MAAM;EACnBT,OAAOQ;AACT,GAAG,QAAQ;AAEX,IAAIE,eAAe;AACZ,SAASC,cAAeC,QAA4BC,IAAyC;AAClG,MAAIC,SAAS;AACb,MAAIC;AACJ,MAAIC;AAEJ,MAAIC,eAAcL,iCAAQM,aAAY;AACpCC,aAAS,MAAM;AACbC,aAAOC,iBAAiB,YAAYC,UAAU;AAC9CP,qBAAeH,OAAOM,WAAW,CAAChC,IAAIqC,MAAMC,SAAS;AACnD,YAAI,CAACd,cAAc;AACjBe,qBAAW,MAAMX,SAASD,GAAGW,IAAI,IAAIA,KAAK,CAAC;QAC7C,OAAO;AACLV,mBAASD,GAAGW,IAAI,IAAIA,KAAK;QAC3B;AACAd,uBAAe;MACjB,CAAC;AACDM,oBAAcJ,iCAAQc,UAAU,MAAM;AACpChB,uBAAe;MACjB;IACF,CAAC;AACDiB,mBAAe,MAAM;AACnBP,aAAOQ,oBAAoB,YAAYN,UAAU;AACjDP;AACAC;IACF,CAAC;EACH;AAEA,WAASM,WAAYO,GAAkB;AAvIzC;AAwII,SAAIA,OAAEC,UAAFD,mBAASE,SAAU;AAEvBjB,aAAS;AACTW,eAAW,MAAOX,SAAS,KAAM;EACnC;AACF;;;AC1IO,SAASkB,aAAc;AAC5B,QAAMC,KAAKC,mBAAmB,YAAY;AAE1C,QAAMC,UAAUF,GAAIG,MAAMD;AAE1B,SAAO;IAAEA,SAASA,UAAU;MAAE,CAACA,OAAO,GAAG;IAAG,IAAIE;EAAU;AAC5D;;;ACYA,IAAMC,cAA0CC,OAAOC,IAAI,eAAe;AAM1E,IAAMC,cAAcC,SAA0C,CAAA,CAAE;AAEzD,SAASC,SACdC,UACAC,QACAC,oBACA;AACA,QAAMC,KAAKC,mBAAmB,UAAU;AACxC,QAAMC,mBAAmB,CAACH;AAE1B,QAAMI,SAASC,OAAOb,aAAac,MAAS;AAC5C,QAAMC,QAAsBX,SAAS;IACnCY,gBAAgB,oBAAIC,IAAY;EAClC,CAAC;AACDC,UAAQlB,aAAae,KAAK;AAE1B,QAAMI,UAAUC,WAAWC,OAAOC,QAAQf,MAAM,CAAC,CAAC;AAClDgB,iBAAejB,UAAU,MAAM;AA5CjC;AA6CI,UAAMkB,cAAarB,iBAAYsB,GAAG,EAAE,MAAjBtB,mBAAqB;AACxCgB,YAAQO,QAAQF,aAAaA,aAAa,KAAKH,OAAOC,QAAQf,MAAM,CAAC;AAErE,QAAII,kBAAkB;AACpBR,kBAAYwB,KAAK,CAAClB,GAAGmB,KAAKT,QAAQO,KAAK,CAAC;IAC1C;AAEAd,qCAAQI,eAAea,IAAIpB,GAAGmB;AAE9BE,mBAAe,MAAM;AACnB,UAAInB,kBAAkB;AACpB,cAAMoB,MAAMC,MAAM7B,WAAW,EAAE8B,UAAUC,OAAKA,EAAE,CAAC,MAAMzB,GAAGmB,GAAG;AAC7DzB,oBAAYgC,OAAOJ,KAAK,CAAC;MAC3B;AAEAnB,uCAAQI,eAAeoB,OAAO3B,GAAGmB;IACnC,CAAC;EACH,CAAC;AAED,QAAMS,YAAYjB,WAAW,IAAI;AACjC,MAAIT,kBAAkB;AACpB2B,gBAAY,MAAM;AAlEtB;AAmEM,YAAMC,WAASpC,iBAAYsB,GAAG,EAAE,MAAjBtB,mBAAqB,QAAOM,GAAGmB;AAC9CY,iBAAW,MAAMH,UAAUX,QAAQa,MAAM;IAC3C,CAAC;EACH;AAEA,QAAME,WAAWC,MAAM,MAAM,CAAC3B,MAAMC,eAAe2B,IAAI;AAEvD,SAAO;IACLN,WAAWO,SAASP,SAAS;IAC7BI;IACAI,aAAaH,MAAM,OAAO;MAAEnC,QAAQY,QAAQO;IAAM,EAAE;EACtD;AACF;;;AC3EO,SAASoB,YAAaC,QAA+C;AAC1E,QAAMC,iBAAiBC,SAAS,MAAM;AACpC,UAAMC,UAAUH,OAAO;AAEvB,QAAIG,YAAY,QAAQ,CAACC,WAAY,QAAOC;AAE5C,UAAMC,gBACJH,YAAY,QAAQI,SAASC,OAC3B,OAAOL,YAAY,WAAWI,SAASE,cAAcN,OAAO,IAC5DA;AAEJ,QAAIG,iBAAiB,MAAM;AACzBI,WAAK,2BAA2BP,OAAO,EAAE;AACzC,aAAOE;IACT;AAEA,QAAIM,YAAY,CAAC,GAAGL,cAAcM,QAAQ,EAAEC,KAAKC,QAAMA,GAAGC,QAAQ,sBAAsB,CAAC;AAEzF,QAAI,CAACJ,WAAW;AACdA,kBAAYJ,SAASS,cAAc,KAAK;AACxCL,gBAAUM,YAAY;AACtBX,oBAAcY,YAAYP,SAAS;IACrC;AAEA,WAAOA;EACT,CAAC;AAED,SAAO;IAAEV;EAAe;AAC1B;;;ACzBO,IAAMkB,sBAAsBC,aAAa;EAC9CC,YAAY;IACVC,MAAM;IACNC,SAAS;IACTC,WAAWC,SAAOA,QAAQ;EAC5B;AACF,GAAG,YAAY;AAQR,IAAMC,kBAA6DA,CAACC,OAAKC,SAAgB;AAAA,MAAd;IAAEC;EAAM,IAACD;AACzF,QAAM;IAAEP;IAAYS;IAAUC;IAAO,GAAGC;EAAK,IAAIL;AAEjD,QAAM;IACJM,YAAYF,QAAQG,kBAAkBC;IACtC,GAAGC;EACL,IAAIC,SAAShB,UAAU,IAAIA,aAAa,CAAC;AAEzC,MAAIiB;AACJ,MAAID,SAAShB,UAAU,GAAG;AACxBiB,sBAAkBC,WAChBH,aACAI,iBAAiB;MAAEV;MAAUC;IAAM,CAAC,GACpCC,IACF;EACF,OAAO;AACLM,sBAAkBC,WAChB;MAAEE,MAAMX,YAAY,CAACT,aAAa,KAAKA;IAAW,GAClDW,IACF;EACF;AAEA,SAAOU,EACLT,WACAK,iBACAT,KACF;AACF;;;AhBSA,SAASc,MAAOC,OAAmB;AACjC,QAAM;IAAEC;IAAYC;IAAO,GAAGC;EAAK,IAAIH;AACvC,SAAAI,YAAAC,YAAA;IAAA,QAAA;IAAA,UAAA;EAAA,GAAA;IAAAC,SAAAA,MAAA,CAEMN,MAAMC,cAAUM,gBAAA,OAAAC,WAAA;MAAA,SAEP,CACL,oBACAR,MAAME,MAAMO,uBAAuBC,KAAK;MACzC,SACOV,MAAME,MAAMS,sBAAsBD;IAAK,GAC1CP,IAAI,GAAA,IAAA,CAEZ;EAAA,CAAA;AAGP;AAOO,IAAMS,oBAAoBC,aAAa;EAC5CC,UAAUC;EACVC,QAAQ,CAACD,SAASE,QAAQC,MAAM;EAChCC,aAAa;IACXC,MAAML;IACNT,SAAS;EACX;EACAe,WAAWN;EACXO,cAAc;EACdC,cAAc;EACdC,UAAUT;EACVU,SAAS,CAACC,QAAQT,MAAM;EACxBU,kBAAkBZ;EAClBd,YAAYc;EACZa,YAAYb;EACZc,OAAO;IACLT,MAAM,CAACL,SAASE,MAAM;IACtBX,SAAS;EACX;EACAwB,QAAQ;IACNV,MAAM,CAACM,QAAQT,MAAM;IACrBX,SAAS;EACX;EAEA,GAAGyB,mBAAmB;EACtB,GAAGC,mBAAmB;EACtB,GAAGC,mBAAmB;EACtB,GAAGC,cAAc;EACjB,GAAGC,0BAA0B;EAC7B,GAAGC,wBAAwB;EAC3B,GAAGC,eAAe;EAClB,GAAGC,oBAAoB;AACzB,GAAG,UAAU;AAEN,IAAMC,WAAWC,iBAA+B,EAAE;EACvDC,MAAM;EAENC,YAAY;IAAEC;EAAc;EAE5BC,cAAc;EAEd5C,OAAO;IACL6C,qBAAqB9B;IAErB,GAAGH,kBAAkB;EACvB;EAEAkC,OAAO;IACL,iBAAkBC,OAAkB;IACpC,qBAAsBrC,WAAmB;IACzCsC,SAAUD,OAAqB;IAC/BE,YAAYA,MAAM;IAClBC,YAAYA,MAAM;EACpB;EAEAC,MAAOnD,OAAKoD,MAA0B;AAAA,QAAxB;MAAEC;MAAOC;MAAOC;IAAK,IAACH;AAClC,UAAMI,KAAKC,mBAAmB,UAAU;AACxC,UAAMC,OAAOC,IAAiB;AAC9B,UAAMC,UAAUD,IAAiB;AACjC,UAAME,YAAYF,IAAiB;AACnC,UAAMG,QAAQC,gBAAgB/D,OAAO,YAAY;AACjD,UAAMgE,WAAWC,SAAS;MACxBC,KAAKA,MAAMJ,MAAMpD;MACjByD,KAAKC,OAAK;AACR,YAAI,EAAEA,KAAKpE,MAAMwB,UAAWsC,OAAMpD,QAAQ0D;MAC5C;IACF,CAAC;AACD,UAAM;MAAEC;IAAa,IAAIC,aAAatE,KAAK;AAC3C,UAAM;MAAEuE;MAAYC;IAAM,IAAIC,OAAO;AACrC,UAAM;MAAEC;MAAYC,cAAcC;IAAc,IAAIC,QAAQ7E,OAAOgE,QAAQ;AAC3E,UAAMc,aAAaC,mBAAmB,MAAM;AAC1C,aAAO,OAAO/E,MAAM6B,UAAU,WAAW7B,MAAM6B,QAAQ;IACzD,CAAC;AACD,UAAM;MAAEmD;MAAWC;MAAUC;IAAY,IAAIC,SAASnB,UAAU,MAAMhE,MAAM8B,QAAQ9B,MAAM6C,mBAAmB;AAC7G,UAAM;MACJuC;MAAaC;MACbC;MAAQC;MAAUC;MAClBC;MACAC;MACAC;IACF,IAAIC,aAAa5F,OAAO;MAAEgE;MAAU6B,OAAOZ;MAAUpB;IAAU,CAAC;AAChE,UAAM;MAAEiC;IAAe,IAAIC,YAAY,MAAM;;AAC3C,YAAMT,UAAStF,MAAMgB,UAAUhB,MAAMqB;AACrC,UAAIiE,QAAQ,QAAOA;AACnB,YAAMU,aAAWZ,gDAAa1E,UAAb0E,mBAAoBa,oBAAiBzC,cAAG0C,UAAH1C,mBAAU2C,QAAV3C,mBAAeyC;AACrE,UAAID,oBAAoBI,WAAY,QAAOJ;AAC3C,aAAO;IACT,CAAC;AACD,UAAM;MAAEK;IAAgB,IAAIC,aAAatG,KAAK;AAC9C,UAAMuG,YAAYC,aAAa;AAC/B,UAAM;MAAEC;IAAQ,IAAIC,WAAW;AAE/BC,UAAM,MAAM3G,MAAMwB,UAAU4C,OAAK;AAC/B,UAAIA,EAAGJ,UAAStD,QAAQ;IAC1B,CAAC;AAED,UAAM;MAAEkG;MAAeC;IAAe,IAAIC,sBAAsB9G,OAAO;MACrEwE;MACAX;MACAyB;MACAtB;IACF,CAAC;AACD+C,wBAAoB/G,OAAO;MACzB0D;MACAG;MACA0B;MACAvB;MACA6C;IACF,CAAC;AAED,aAASG,eAAgBjE,GAAe;AACtCQ,WAAK,iBAAiBR,CAAC;AAEvB,UAAI,CAAC/C,MAAM4B,WAAYoC,UAAStD,QAAQ;UACnCuG,cAAa;IACpB;AAEA,aAASC,iBAAkBnE,GAAU;AACnC,aAAOiB,SAAStD,SAASsE,UAAUtE;OAEjC,CAACV,MAAM6B,SAASkB,EAAEuC,WAAW1B,QAAQlD,SAAUqC,aAAaoE,cAAcpE,EAAEqE,iBAAiBxD,QAAQlD;IAEzG;AAEA2G,kBAAcV,MAAM3C,UAAUsD,SAAO;AACnC,UAAIA,KAAK;AACPC,eAAOC,iBAAiB,WAAWC,SAAS;MAC9C,OAAO;AACLF,eAAOG,oBAAoB,WAAWD,SAAS;MACjD;IACF,GAAG;MAAEE,WAAW;IAAK,CAAC;AAEtBC,oBAAgB,MAAM;AACpB,UAAI,CAACP,WAAY;AAEjBE,aAAOG,oBAAoB,WAAWD,SAAS;IACjD,CAAC;AAED,aAASA,UAAW1E,GAAkB;;AACpC,UAAIA,EAAE8E,QAAQ,YAAY7C,UAAUtE,OAAO;AACzC,YAAI,GAACmD,eAAUnD,UAAVmD,mBAAiBiE,SAASC,SAASC,iBAAgB;AACtDzE,eAAK,WAAWR,CAAC;QACnB;AACA,YAAI,CAAC/C,MAAM4B,YAAY;AACrBoC,mBAAStD,QAAQ;AACjB,eAAImD,eAAUnD,UAAVmD,mBAAiBiE,SAASC,SAASC,gBAAgB;AACrD5C,8BAAY1E,UAAZ0E,mBAAmB6C;UACrB;QACF,MAAOhB,cAAa;MACtB;IACF;AACA,aAASiB,cAAenF,GAAkB;AACxC,UAAIA,EAAE8E,QAAQ,YAAY,CAAC7C,UAAUtE,MAAO;AAE5C6C,WAAK,WAAWR,CAAC;IACnB;AAEA,UAAMoF,SAASC,UAAU;AACzBC,mBAAe,MAAMrI,MAAMmB,aAAa,MAAM;AAC5CmH,oBAAcH,QAAQI,UAAQ;AAC5B,YAAIvD,UAAUtE,SAASsD,SAAStD,OAAO;AACrC6H,eAAK,KAAK;AACV,cAAI,CAACvI,MAAM4B,WAAYoC,UAAStD,QAAQ;cACnCuG,cAAa;QACpB,OAAO;AACLsB,eAAK;QACP;MACF,CAAC;IACH,CAAC;AAED,UAAMC,MAAM7E,IAAY;AACxBgD,UAAM,MAAM3C,SAAStD,UAAUV,MAAMc,YAAYd,MAAMqB,cAAcyE,eAAepF,SAAS,MAAM4G,SAAO;AACxG,UAAIA,KAAK;AACP,cAAMmB,eAAeC,gBAAgBhF,KAAKhD,KAAK;AAC/C,YAAI+H,gBAAgBA,iBAAiBV,SAASY,kBAAkB;AAC9DH,cAAI9H,QAAQ+H,aAAaG;QAC3B;MACF;IACF,CAAC;AAGD,aAAS3B,eAAgB;AACvB,UAAIjH,MAAM2B,iBAAkB;AAE5BkC,gBAAUnD,SAASmI,QAAQhF,UAAUnD,OAAO,CAC1C;QAAEoI,iBAAiB;MAAS,GAC5B;QAAEC,WAAW;MAAc,GAC3B;QAAED,iBAAiB;MAAS,CAAC,GAC5B;QACDE,UAAU;QACVC,QAAQC;MACV,CAAC;IACH;AAEA,aAASC,eAAgB;AACvB5F,WAAK,YAAY;IACnB;AAEA,aAASoB,eAAgB;AACvBC,oBAAc;AACdrB,WAAK,YAAY;IACnB;AAEA6F,cAAU,MAAA;;AAAA7I,6BAAA8I,UAAA,MAAA,EAEJhG,WAAMiG,cAANjG,+BAAkB;QAClBW,UAAUA,SAAStD;QACnB8E;QACAxF,OAAOuJ,WAAW;UAChB5F,KAAK0B;QACP,GAAGI,gBAAgB/E,OAAOV,MAAMwJ,cAAc;MAChD,IAEEjD,UAAU7F,SAASgE,WAAWhE,SAAKN,YAAAqJ,UAAA;QAAA,YAEtB,CAAC3D,eAAepF;QAAK,MAC3BoF,eAAepF;MAAK,GAAA;QAAAJ,SAAAA,MAAA,CAAAC,gBAAA,OAAAC,WAAA;UAAA,SAGhB,CACL,aACA;YACE,uBAAuBR,MAAMc,YAAYd,MAAMqB;YAC/C,qBAAqB2C,SAAStD;YAC9B,wBAAwBV,MAAMqB;UAChC,GACAgD,aAAa3D,OACb6D,WAAW7D,OACXV,MAAM0J,KAAK;UACZ,SACM,CACLxE,YAAYxE,OACZ;YACE,uBAAuBV,MAAMyB;YAC7B+G,KAAKmB,cAAcnB,IAAI9H,KAAK;UAC9B,GACAV,MAAM4J,KAAK;UACZ,OACKlG;UAAI,aACEwE;QAAa,GACpBzB,SACAnD,KAAK,GAAA,CAAAlD,YAAAL,OAAAS,WAAA;UAAA,SAGAsE;UAAU,cACLd,SAAStD,SAAS,CAAC,CAACV,MAAM6B;UAAK,OACtC+B;QAAO,GACR+B,YAAYjF,KAAK,GAAA,IAAA,GAAAN,YAAAyJ,iBAAA;UAAA,UAAA;UAAA,aAAA;UAAA,cAKT7J,MAAM8J;UAAU,UACpBxE,OAAO5E;UAAK,gBACNyI;UAAY,gBACZxE;QAAY,GAAA;UAAArE,SAAAA,MAAA;;AAAA,oBAAAyJ,eAAAxJ,gBAAA,OAAAC,WAAA;cAAA,OAGnBqD;cAAS,SAGR,CACL,sBACA7D,MAAMsB,YAAY;cACnB,SACM,CACL+E,gBAAgB3F,OAChBkG,cAAclG,KAAK;YACpB,GACIgF,cAAchF,OACdV,MAAMuB,YAAY,GAAA,EAErB8B,MAAAA,MAAM/C,YAAN+C,gBAAAA,IAAAA,YAAgB;cAAEW;YAAS,EAAE,CAAA,GAAA,CAAA,CAAAgG,OAbtBhG,SAAStD,KAAK,GAAA,CAAAiC,uBACN;cAAEsH,SAASjD;cAAgBE;cAAkBgD,SAASA,MAAM,CAAC9E,YAAY1E,KAAK;YAAE,CAAC,CAAA,CAAA,CAAA;;QAAA,CAAA,CAAA,CAAA,CAAA;MAAA,CAAA,CAiB3G,CAAA;KAEJ;AAED,WAAO;MACL0E;MACAxB;MACA0B;MACA2B;MACApD;MACAmB;MACAC;MACA4B;IACF;EACF;AACF,CAAC;;;AiBtUD,IAAMsD,gBAAiBC,aAA0B;AAC/C,QAAM;IAAEC;IAAaC;IAAWC;IAAaC;EAAU,IAAIJ;AAC3D,QAAMK,WAAW;AACjB,QAAMC,cAAc;AACpBN,UAAQO,UAAUL,YAAYD;AAC9BD,UAAQQ,UAAUJ,YAAYD;AAE9B,MAAIM,KAAKC,IAAIV,QAAQQ,OAAO,IAAIH,WAAWI,KAAKC,IAAIV,QAAQO,OAAO,GAAG;AACpEP,YAAQW,QAAST,YAAYD,cAAcK,eAAgBN,QAAQW,KAAKX,OAAO;AAC/EA,YAAQY,SAAUV,YAAYD,cAAcK,eAAgBN,QAAQY,MAAMZ,OAAO;EACnF;AAEA,MAAIS,KAAKC,IAAIV,QAAQO,OAAO,IAAIF,WAAWI,KAAKC,IAAIV,QAAQQ,OAAO,GAAG;AACpER,YAAQa,MAAOT,YAAYD,cAAcG,eAAgBN,QAAQa,GAAGb,OAAO;AAC3EA,YAAQc,QAASV,YAAYD,cAAcG,eAAgBN,QAAQc,KAAKd,OAAO;EACjF;AACF;AAEA,SAASe,WAAYC,OAAmBhB,SAAuB;AAhE/D;AAiEE,QAAMiB,QAAQD,MAAME,eAAe,CAAC;AACpClB,UAAQC,cAAcgB,MAAME;AAC5BnB,UAAQG,cAAcc,MAAMG;AAE5BpB,gBAAQqB,UAARrB,iCAAgB;IAAEsB,eAAeN;IAAO,GAAGhB;EAAQ;AACrD;AAEA,SAASuB,SAAUP,OAAmBhB,SAAuB;AAxE7D;AAyEE,QAAMiB,QAAQD,MAAME,eAAe,CAAC;AACpClB,UAAQE,YAAYe,MAAME;AAC1BnB,UAAQI,YAAYa,MAAMG;AAE1BpB,gBAAQwB,QAARxB,iCAAc;IAAEsB,eAAeN;IAAO,GAAGhB;EAAQ;AAEjDD,gBAAcC,OAAO;AACvB;AAEA,SAASyB,UAAWT,OAAmBhB,SAAuB;AAlF9D;AAmFE,QAAMiB,QAAQD,MAAME,eAAe,CAAC;AACpClB,UAAQ0B,aAAaT,MAAME;AAC3BnB,UAAQ2B,aAAaV,MAAMG;AAE3BpB,gBAAQ4B,SAAR5B,iCAAe;IAAEsB,eAAeN;IAAO,GAAGhB;EAAQ;AACpD;AAEA,SAAS6B,iBAAgE;AAAA,MAAhDC,QAAoBC,UAAAC,SAAA,KAAAD,UAAA,CAAA,MAAAE,SAAAF,UAAA,CAAA,IAAG,CAAC;AAC/C,QAAM/B,UAAU;IACdC,aAAa;IACbE,aAAa;IACbD,WAAW;IACXE,WAAW;IACXsB,YAAY;IACZC,YAAY;IACZpB,SAAS;IACTC,SAAS;IACTG,MAAMmB,MAAMnB;IACZC,OAAOkB,MAAMlB;IACbC,IAAIiB,MAAMjB;IACVC,MAAMgB,MAAMhB;IACZO,OAAOS,MAAMT;IACbO,MAAME,MAAMF;IACZJ,KAAKM,MAAMN;EACb;AAEA,SAAO;IACLT,YAAamB,OAAkBnB,WAAWmB,GAAGlC,OAAO;IACpDuB,UAAWW,OAAkBX,SAASW,GAAGlC,OAAO;IAChDyB,WAAYS,OAAkBT,UAAUS,GAAGlC,OAAO;EACpD;AACF;AAEA,SAASmC,SAASC,IAAiBC,SAAgC;AApHnE;AAqHE,QAAMP,QAAQO,QAAQP;AACtB,QAAMQ,UAASR,+BAAOS,UAASH,GAAGI,gBAAgBJ;AAClD,QAAMK,WAAUX,+BAAOW,YAAW;IAAEC,SAAS;EAAK;AAClD,QAAMC,OAAMN,aAAQO,aAARP,mBAAkBQ,EAAEF;AAEhC,MAAI,CAACL,UAAU,CAACK,IAAK;AAErB,QAAMG,WAAWjB,eAAeQ,QAAQP,KAAK;AAE7CQ,SAAOS,iBAAiBT,OAAOS,kBAAkBC,uBAAOC,OAAO,IAAI;AACnEX,SAAOS,eAAgBJ,GAAG,IAAIG;AAE9BI,OAAKJ,QAAQ,EAAEK,QAAQC,eAAa;AAClCd,WAAOe,iBAAiBD,WAAWN,SAASM,SAAS,GAAGX,OAAO;EACjE,CAAC;AACH;AAEA,SAASa,WAAWlB,IAAiBC,SAAgC;AAtIrE;AAuIE,QAAMC,WAASD,aAAQP,UAARO,mBAAeE,UAASH,GAAGI,gBAAgBJ;AAC1D,QAAMO,OAAMN,aAAQO,aAARP,mBAAkBQ,EAAEF;AAEhC,MAAI,EAACL,iCAAQS,mBAAkB,CAACJ,IAAK;AAErC,QAAMG,WAAWR,OAAOS,eAAeJ,GAAG;AAE1CO,OAAKJ,QAAQ,EAAEK,QAAQC,eAAa;AAClCd,WAAOiB,oBAAoBH,WAAWN,SAASM,SAAS,CAAC;EAC3D,CAAC;AAED,SAAOd,OAAOS,eAAeJ,GAAG;AAClC;AAEO,IAAMa,QAAQ;EACnBrB,SAAAA;EACAmB,WAAAA;AACF;AAEA,IAAA,gBAAeE;;;ACzJf,OAAA;;;ACGA,IAAMC,OAAOC,OAAO,gBAAgB;AAapC,SAASC,cAAeC,KAAUC,KAAkB;AAClD,MAAIC,aAAaF;AACjB,SAAOE,YAAY;AACjB,UAAMC,aAAaC,QAAQC,yBAAyBH,YAAYD,GAAG;AACnE,QAAIE,WAAY,QAAOA;AACvBD,iBAAaI,OAAOC,eAAeL,UAAU;EAC/C;AACA,SAAOM;AACT;AAEO,SAASC,YAcbC,QAA0B;AAAA,WAAAC,OAAAC,UAAAC,QAAZC,OAAI,IAAAC,MAAAJ,OAAA,IAAAA,OAAA,IAAA,CAAA,GAAAK,OAAA,GAAAA,OAAAL,MAAAK,QAAA;AAAJF,SAAIE,OAAA,CAAA,IAAAJ,UAAAI,IAAA;EAAA;AAClBN,SAAeb,IAAI,IAAIiB;AAExB,SAAO,IAAIG,MAAMP,QAAQ;IACvBQ,IAAKR,SAAQT,KAAK;AAChB,UAAIG,QAAQe,IAAIT,SAAQT,GAAG,GAAG;AAC5B,eAAOG,QAAQc,IAAIR,SAAQT,GAAG;MAChC;AAGA,UAAI,OAAOA,QAAQ,YAAYA,IAAImB,WAAW,GAAG,KAAKnB,IAAImB,WAAW,IAAI,EAAG;AAE5E,iBAAWC,QAAOP,MAAM;AACtB,YAAIO,KAAIC,SAASlB,QAAQe,IAAIE,KAAIC,OAAOrB,GAAG,GAAG;AAC5C,gBAAMsB,MAAMnB,QAAQc,IAAIG,KAAIC,OAAOrB,GAAG;AACtC,iBAAO,OAAOsB,QAAQ,aAClBA,IAAIC,KAAKH,KAAIC,KAAK,IAClBC;QACN;MACF;IACF;IACAJ,IAAKT,SAAQT,KAAK;AAChB,UAAIG,QAAQe,IAAIT,SAAQT,GAAG,GAAG;AAC5B,eAAO;MACT;AAGA,UAAI,OAAOA,QAAQ,YAAYA,IAAImB,WAAW,GAAG,KAAKnB,IAAImB,WAAW,IAAI,EAAG,QAAO;AAEnF,iBAAWC,QAAOP,MAAM;AACtB,YAAIO,KAAIC,SAASlB,QAAQe,IAAIE,KAAIC,OAAOrB,GAAG,GAAG;AAC5C,iBAAO;QACT;MACF;AACA,aAAO;IACT;IACAwB,IAAKf,SAAQT,KAAKqB,OAAO;AACvB,UAAIlB,QAAQe,IAAIT,SAAQT,GAAG,GAAG;AAC5B,eAAOG,QAAQqB,IAAIf,SAAQT,KAAKqB,KAAK;MACvC;AAGA,UAAI,OAAOrB,QAAQ,YAAYA,IAAImB,WAAW,GAAG,KAAKnB,IAAImB,WAAW,IAAI,EAAG,QAAO;AAEnF,iBAAWC,QAAOP,MAAM;AACtB,YAAIO,KAAIC,SAASlB,QAAQe,IAAIE,KAAIC,OAAOrB,GAAG,GAAG;AAC5C,iBAAOG,QAAQqB,IAAIJ,KAAIC,OAAOrB,KAAKqB,KAAK;QAC1C;MACF;AAEA,aAAO;IACT;IACAjB,yBAA0BK,SAAQT,KAAK;AA7F3C;AA8FM,YAAME,aAAaC,QAAQC,yBAAyBK,SAAQT,GAAG;AAC/D,UAAIE,WAAY,QAAOA;AAGvB,UAAI,OAAOF,QAAQ,YAAYA,IAAImB,WAAW,GAAG,KAAKnB,IAAImB,WAAW,IAAI,EAAG;AAG5E,iBAAWC,QAAOP,MAAM;AACtB,YAAI,CAACO,KAAIC,MAAO;AAChB,cAAMnB,cAAaJ,cAAcsB,KAAIC,OAAOrB,GAAG,MAAM,OAAOoB,KAAIC,QAAQvB,eAAcsB,KAAAA,KAAIC,MAAMI,MAAVL,mBAAaM,YAAY1B,GAAG,IAAIO;AACtH,YAAIL,YAAY,QAAOA;MACzB;AAGA,iBAAWkB,QAAOP,MAAM;AACtB,cAAMc,YAAYP,KAAIC,SAAUD,KAAIC,MAAczB,IAAI;AACtD,YAAI,CAAC+B,UAAW;AAChB,cAAMC,QAAQD,UAAUE,MAAM;AAC9B,eAAOD,MAAMhB,QAAQ;AACnB,gBAAMQ,OAAMQ,MAAME,MAAM;AACxB,gBAAM5B,cAAaJ,cAAcsB,KAAIC,OAAOrB,GAAG;AAC/C,cAAIE,YAAY,QAAOA;AACvB,gBAAMyB,aAAYP,KAAIC,SAAUD,KAAIC,MAAczB,IAAI;AACtD,cAAI+B,WAAWC,OAAMG,KAAK,GAAGJ,UAAS;QACxC;MACF;AAEA,aAAOpB;IACT;EACF,CAAC;AACH;;;ADxGO,IAAMyB,oBAAoBC,aAAa;EAC5CC,IAAIC;EACJC,aAAaC;EACbC,MAAMH;EAEN,GAAGI,KAAKC,kBAAkB;IACxBC,aAAa;IACbC,UAAU;IACVC,kBAAkB;IAClBC,OAAO;IACPC,UAAU;IACVC,QAAQ;IACRC,aAAa;IACbC,aAAa;IACbC,QAAQ;IACRC,OAAO;IACPC,gBAAgB;IAChBC,YAAY;EACd,CAAC,GAAG,CACF,YACA,YAAY,CACb;AACH,GAAG,UAAU;AAEN,IAAMC,WAAWC,iBAA+B,EAAE;EACvDC,MAAM;EAENC,OAAOxB,kBAAkB;EAEzByB,OAAO;IACL,qBAAsBC,WAAmB;EAC3C;EAEAC,MAAOH,OAAKI,MAAa;AAAA,QAAX;MAAEC;IAAM,IAACD;AACrB,UAAME,WAAWC,gBAAgBP,OAAO,YAAY;AACpD,UAAM;MAAEQ;IAAQ,IAAIC,WAAW;AAE/B,UAAMC,MAAMC,MAAM;AAClB,UAAMjC,KAAKkC,MAAM,MAAMZ,MAAMtB,MAAM,aAAagC,GAAG,EAAE;AAErD,UAAMG,UAAUC,IAAc;AAE9B,UAAM5B,WAAW6B,SAAS,MAAM;AAC9B,aAAOf,MAAMd,SAAS8B,MAAM,GAAG,EAAEC,SAAS,IACtCjB,MAAMd,WACNc,MAAMd,WAAW;IACvB,CAAC;AAED,UAAMO,SAASsB,SAAS,MAAM;AAC5B,aACEf,MAAMP,WAAW,UACjBO,MAAMP,WAAW,aACjBO,MAAMP,OAAOuB,MAAM,GAAG,EAAEC,SAAS,KACjCjB,MAAMd,SAAS8B,MAAM,GAAG,EAAEC,SAAS,IACjCjB,MAAMP,SACNO,MAAMP,SAAS;IACrB,CAAC;AAED,UAAMG,aAAagB,MAAM,MAAM;AAC7B,UAAIZ,MAAMJ,cAAc,KAAM,QAAOI,MAAMJ;AAC3C,aAAOU,SAASJ,QAAQ,qBAAqB;IAC/C,CAAC;AAED,UAAMgB,iBAAiBH,SAAS,MAC9BI,WAAW;MACT,oBAAoBzC,GAAGwB;IACzB,GAAGF,MAAMkB,cAAc,CACzB;AAEAE,cAAU,MAAM;AACd,YAAMC,eAAeC,SAASC,YAAYvB,KAAK;AAE/C,aAAAwB,YAAAF,UAAAG,WAAA;QAAA,OAEUZ;QAAO,SACN,CACL,aACA;UAAE,0BAA0Bb,MAAMpB;QAAY,GAC9CoB,MAAM0B,KAAK;QACZ,SACO1B,MAAM2B;QAAK,MACdjD,GAAGwB;MAAK,GACRmB,cAAY;QAAA,cACPf,SAASJ;QAAK,uBAAA0B,YAAdtB,SAASJ,QAAK0B;QAAA,cACXhC,WAAWM;QAAK,YAAA;QAAA,YAElBhB,SAASgB;QAAK,UAChBT,OAAOS;QAAK,cAAA;QAAA,QAAA;QAAA,kBAGJgB,eAAehB;QAAK,uBAAA;MAAA,GAEhCM,OAAO,GAAA;QAGVqB,WAAWxB,MAAMwB;QACjBC,SAAS,WAAA;;AAAA,mBAAAC,OAAAC,UAAAf,QAAIgB,OAAI,IAAAC,MAAAH,IAAA,GAAAI,OAAA,GAAAA,OAAAJ,MAAAI,QAAA;AAAJF,iBAAIE,IAAA,IAAAH,UAAAG,IAAA;UAAA;AAAA,mBAAK9B,WAAMyB,YAANzB,+BAAgB,GAAG4B,UAASjC,MAAMlB;QAAI;MAAA,CAAA;IAIpE,CAAC;AAED,WAAOsD,YAAY,CAAC,GAAGvB,OAAO;EAChC;AACF,CAAC;", "names": ["mounted", "el", "binding", "SUPPORTS_INTERSECTION", "modifiers", "value", "handler", "options", "observer", "IntersectionObserver", "entries", "arguments", "length", "undefined", "_observe", "instance", "$", "uid", "isIntersecting", "some", "entry", "quiet", "init", "once", "unmounted", "Object", "observe", "unobserve", "Intersect", "stopSymbol", "Symbol", "DELAY_RIPPLE", "transform", "el", "value", "style", "webkitTransform", "isTouchEvent", "e", "constructor", "name", "isKeyboardEvent", "calculate", "arguments", "length", "undefined", "localX", "localY", "offset", "getBoundingClientRect", "target", "touches", "clientX", "left", "clientY", "top", "radius", "scale", "_ripple", "circle", "clientWidth", "center", "Math", "sqrt", "clientHeight", "centerX", "centerY", "x", "y", "ripples", "show", "enabled", "container", "document", "createElement", "animation", "append<PERSON><PERSON><PERSON>", "className", "class", "size", "width", "height", "computed", "window", "getComputedStyle", "position", "dataset", "previousPosition", "classList", "add", "activated", "String", "performance", "now", "requestAnimationFrame", "remove", "hide", "getElementsByClassName", "isHiding", "diff", "Number", "delay", "max", "setTimeout", "_a", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "isRippleEnabled", "rippleShow", "element", "currentTarget", "touched", "is<PERSON><PERSON>ch", "centered", "showTimerCommit", "showTimer", "rippleStop", "rippleHide", "clearTimeout", "type", "rippleCancelShow", "keyboardRipple", "keyboardRippleShow", "keyCode", "keyCodes", "enter", "space", "keyboardRippleHide", "focusRippleHide", "updateRipple", "binding", "wasEnabled", "modifiers", "isObject", "stop", "addEventListener", "passive", "removeListeners", "removeEventListener", "mounted", "unmounted", "updated", "oldValue", "<PERSON><PERSON><PERSON>", "defaultConditional", "checkEvent", "e", "el", "binding", "checkIsActive", "root", "attachedRoot", "ShadowRoot", "host", "target", "elements", "value", "include", "push", "some", "contains", "isActive", "closeConditional", "directive", "handler", "<PERSON><PERSON><PERSON><PERSON>", "_clickOutside", "lastMousedownWasOutside", "setTimeout", "handleShadow", "callback", "document", "ClickOutside", "mounted", "onClick", "onMousedown", "app", "addEventListener", "instance", "$", "uid", "beforeUnmount", "removeEventListener", "elementToViewport", "point", "offset", "x", "y", "getOffset", "a", "b", "x", "y", "anchorToPoint", "anchor", "box", "side", "align", "width", "height", "elementToViewport", "locationStrategies", "static", "staticLocationStrategy", "connected", "connectedLocationStrategy", "makeLocationStrategyProps", "propsFactory", "locationStrategy", "type", "String", "Function", "default", "validator", "val", "location", "origin", "offset", "Number", "Array", "useLocationStrategies", "props", "data", "contentStyles", "ref", "updateLocation", "IN_BROWSER", "useToggleScope", "isActive", "value", "reset", "watch", "onScopeDispose", "window", "removeEventListener", "onResize", "visualViewport", "onVisualResize", "onVisualScroll", "undefined", "addEventListener", "passive", "e", "getIntrinsicSize", "el", "isRtl", "contentBox", "nullifyTransforms", "x", "parseFloat", "style", "right", "left", "y", "top", "activatorFixed", "isArray", "target", "isFixedPosition", "Object", "assign", "position", "preferredAnchor", "preferred<PERSON><PERSON>in", "destructComputed", "parsedAnchor", "parseAnchor", "parsed<PERSON><PERSON>in", "flipSide", "side", "align", "flipAlign", "<PERSON><PERSON><PERSON><PERSON>", "min<PERSON><PERSON><PERSON>", "minHeight", "max<PERSON><PERSON><PERSON>", "maxHeight", "map", "key", "computed", "isNaN", "Infinity", "split", "length", "push", "observe", "<PERSON><PERSON><PERSON><PERSON>", "flipped", "Circular<PERSON><PERSON>er", "observer", "ResizeObserver", "requestAnimationFrame", "newTime", "clear", "newNewTime", "isFull", "values", "deepEqual", "at", "result", "contentEl", "_ref", "_ref2", "newTarget", "newContentEl", "old<PERSON><PERSON>get", "oldContentEl", "unobserve", "immediate", "disconnect", "targetBox", "Box", "width", "height", "offsetParent", "getClientRects", "getTargetBox", "scrollParents", "getScrollParents", "viewportMargin", "document", "documentElement", "getPropertyValue", "viewport", "reduce", "box", "scrollBox", "getElementBox", "Math", "max", "min", "bottom", "placement", "anchor", "checkOverflow", "_placement", "targetPoint", "anchorToPoint", "contentPoint", "getOffset", "overflows", "getOverflow", "available", "resets", "consoleError", "_x", "_y", "axis", "getAxis", "hasOverflowX", "before", "after", "hasOverflowY", "for<PERSON>ach", "newPlacement", "flip", "newOverflows", "transform<PERSON><PERSON>in", "convertToUnit", "pixelRound", "pixelCeil", "clamp", "nextTick", "round", "devicePixelRatio", "ceil", "clean", "frames", "requestNewFrame", "cb", "length", "push", "run", "raf", "cancelAnimationFrame", "requestAnimationFrame", "frame", "shift", "scrollStrategies", "none", "close", "closeScrollStrategy", "block", "blockScrollStrategy", "reposition", "repositionScrollStrategy", "makeScrollStrategyProps", "propsFactory", "scrollStrategy", "type", "String", "Function", "default", "validator", "val", "useScrollStrategies", "props", "data", "IN_BROWSER", "scope", "watchEffect", "stop", "isActive", "value", "effectScope", "Promise", "resolve", "setTimeout", "active", "run", "onScopeDispose", "onScroll", "e", "bindScroll", "targetEl", "contentEl", "offsetParent", "root", "scrollElements", "Set", "getScrollParents", "contained", "undefined", "filter", "el", "classList", "contains", "scrollbarWidth", "window", "innerWidth", "document", "documentElement", "offsetWidth", "scrollableParent", "hasScrollbar", "add", "for<PERSON>ach", "i", "style", "setProperty", "convertToUnit", "scrollLeft", "scrollTop", "x", "parseFloat", "getPropertyValue", "y", "scroll<PERSON>eh<PERSON>or", "removeProperty", "remove", "slow", "raf", "ric", "update", "requestNewFrame", "start", "performance", "now", "updateLocation", "time", "requestIdleCallback", "cb", "cancelAnimationFrame", "requestAnimationFrame", "cancelIdleCallback", "addEventListener", "passive", "removeEventListener", "VMenuSymbol", "Symbol", "for", "makeDelayProps", "propsFactory", "close<PERSON><PERSON><PERSON>", "Number", "String", "openDelay", "useDelay", "props", "cb", "clear<PERSON>elay", "runDelay", "isOpening", "delay", "Promise", "resolve", "defer", "run<PERSON>pen<PERSON>elay", "runCloseDelay", "makeActivatorProps", "propsFactory", "target", "String", "Object", "activator", "activatorProps", "type", "default", "openOnClick", "Boolean", "undefined", "openOnHover", "openOnFocus", "closeOnContentClick", "makeDelayProps", "useActivator", "props", "_ref", "isActive", "isTop", "contentEl", "vm", "getCurrentInstance", "activatorEl", "ref", "isHovered", "isFocused", "firstEnter", "computed", "value", "run<PERSON>pen<PERSON>elay", "runCloseDelay", "useDelay", "cursor<PERSON>arget", "availableEvents", "onClick", "e", "stopPropagation", "currentTarget", "clientX", "clientY", "onMouseenter", "sourceCapabilities", "firesTouchEvents", "onMouseleave", "onFocus", "matchesSelector", "onBlur", "activatorEvents", "events", "contentEvents", "onFocusin", "onFocusout", "menu", "inject", "VMenuSymbol", "closeParents", "scrimEvents", "watch", "val", "contains", "document", "activeElement", "setTimeout", "flush", "activatorRef", "templateRef", "watchEffect", "nextTick", "el", "targetRef", "get<PERSON><PERSON><PERSON>", "targetEl", "Array", "isArray", "scope", "IN_BROWSER", "effectScope", "run", "_useActivator", "stop", "immediate", "onScopeDispose", "_ref2", "oldVal", "getActivator", "unbindActivatorProps", "bindActivatorProps", "arguments", "length", "_props", "bindProps", "mergeProps", "unbindProps", "selector", "nodeType", "Node", "ELEMENT_NODE", "proxy", "$el", "parentNode", "hasAttribute", "querySelector", "useColor", "colors", "destructComputed", "_colors", "toValue", "classes", "styles", "background", "isCssColor", "backgroundColor", "text", "isParsableColor", "parseColor", "a", "textColor", "getForeground", "color", "caretColor", "push", "colorClasses", "colorStyles", "useTextColor", "textColorClasses", "textColorStyles", "useBackgroundColor", "backgroundColorClasses", "backgroundColorStyles", "makeDimensionProps", "propsFactory", "height", "Number", "String", "maxHeight", "max<PERSON><PERSON><PERSON>", "minHeight", "min<PERSON><PERSON><PERSON>", "width", "useDimension", "props", "dimensionStyles", "computed", "styles", "convertToUnit", "useHydration", "IN_BROWSER", "shallowRef", "ssr", "useDisplay", "isMounted", "onMounted", "value", "makeLazyProps", "propsFactory", "eager", "Boolean", "useLazy", "props", "active", "isBooted", "shallowRef", "<PERSON><PERSON><PERSON><PERSON>", "toRef", "value", "watch", "onAfterLeave", "useRoute", "vm", "getCurrentInstance", "computed", "proxy", "$route", "useRouter", "$router", "useLink", "props", "attrs", "RouterLink", "resolveDynamicComponent", "isLink", "toRef", "href", "to", "isClickable", "value", "hasEvent", "linkProps", "reactive", "routerLink", "replace", "link", "undefined", "route", "isActive", "_a", "_b", "exact", "isExactActive", "deepEqual", "query", "navigate", "makeRouterProps", "propsFactory", "String", "Boolean", "Object", "inTransition", "useBackButton", "router", "cb", "popped", "removeBefore", "removeAfter", "IN_BROWSER", "beforeEach", "nextTick", "window", "addEventListener", "onPopstate", "from", "next", "setTimeout", "after<PERSON>ach", "onScopeDispose", "removeEventListener", "e", "state", "replaced", "useScopeId", "vm", "getCurrentInstance", "scopeId", "vnode", "undefined", "StackSymbol", "Symbol", "for", "globalStack", "reactive", "useStack", "isActive", "zIndex", "disableGlobalStack", "vm", "getCurrentInstance", "createStackEntry", "parent", "inject", "undefined", "stack", "activeC<PERSON><PERSON>n", "Set", "provide", "_zIndex", "shallowRef", "Number", "toValue", "useToggleScope", "lastZIndex", "at", "value", "push", "uid", "add", "onScopeDispose", "idx", "toRaw", "findIndex", "v", "splice", "delete", "globalTop", "watchEffect", "_isTop", "setTimeout", "localTop", "toRef", "size", "readonly", "stackStyles", "useTeleport", "target", "teleportTarget", "computed", "_target", "IN_BROWSER", "undefined", "targetElement", "document", "body", "querySelector", "warn", "container", "children", "find", "el", "matches", "createElement", "className", "append<PERSON><PERSON><PERSON>", "makeTransitionProps", "propsFactory", "transition", "type", "default", "validator", "val", "MaybeTransition", "props", "_ref", "slots", "disabled", "group", "rest", "component", "TransitionGroup", "Transition", "customProps", "isObject", "transitionProps", "mergeProps", "onlyDefinedProps", "name", "h", "Scrim", "props", "modelValue", "color", "rest", "_createVNode", "Transition", "default", "_createElementVNode", "_mergeProps", "backgroundColorClasses", "value", "backgroundColorStyles", "makeVOverlayProps", "propsFactory", "absolute", "Boolean", "attach", "String", "Object", "closeOnBack", "type", "contained", "contentClass", "contentProps", "disabled", "opacity", "Number", "noClickAnimation", "persistent", "scrim", "zIndex", "makeActivatorProps", "makeComponentProps", "makeDimensionProps", "makeLazyProps", "makeLocationStrategyProps", "makeScrollStrategyProps", "makeThemeProps", "makeTransitionProps", "VOverlay", "genericComponent", "name", "directives", "vClickOutside", "inheritAttrs", "_disableGlobalStack", "emits", "e", "keydown", "afterEnter", "afterLeave", "setup", "_ref", "slots", "attrs", "emit", "vm", "getCurrentInstance", "root", "ref", "scrimEl", "contentEl", "model", "useProxiedModel", "isActive", "computed", "get", "set", "v", "themeClasses", "provideTheme", "rtlClasses", "isRtl", "useRtl", "<PERSON><PERSON><PERSON><PERSON>", "onAfterLeave", "_onAfterLeave", "useLazy", "scrimColor", "useBackgroundColor", "globalTop", "localTop", "stackStyles", "useStack", "activatorEl", "activatorRef", "target", "targetEl", "targetRef", "activatorEvents", "contentEvents", "scrimEvents", "useActivator", "isTop", "teleportTarget", "useTeleport", "rootNode", "getRootNode", "proxy", "$el", "ShadowRoot", "dimensionStyles", "useDimension", "isMounted", "useHydration", "scopeId", "useScopeId", "watch", "contentStyles", "updateLocation", "useLocationStrategies", "useScrollStrategies", "onClickOutside", "animateClick", "closeConditional", "MouseEvent", "<PERSON><PERSON><PERSON><PERSON>", "IN_BROWSER", "val", "window", "addEventListener", "onKeydown", "removeEventListener", "immediate", "onBeforeUnmount", "key", "contains", "document", "activeElement", "focus", "onKeydownSelf", "router", "useRouter", "useToggleScope", "useBackButton", "next", "top", "scrollParent", "getScrollParent", "scrollingElement", "scrollTop", "animate", "transform<PERSON><PERSON>in", "transform", "duration", "easing", "standardEasing", "onAfterEnter", "useRender", "_Fragment", "activator", "mergeProps", "activatorProps", "Teleport", "class", "convertToUnit", "style", "MaybeTransition", "transition", "_withDirectives", "_vShow", "handler", "include", "handleGesture", "wrapper", "touchstartX", "touchendX", "touchstartY", "touchendY", "dirRatio", "minDistance", "offsetX", "offsetY", "Math", "abs", "left", "right", "up", "down", "touchstart", "event", "touch", "changedTouches", "clientX", "clientY", "start", "originalEvent", "touchend", "end", "touchmove", "touchmoveX", "touchmoveY", "move", "createHandlers", "value", "arguments", "length", "undefined", "e", "mounted", "el", "binding", "target", "parent", "parentElement", "options", "passive", "uid", "instance", "$", "handlers", "_touchHandlers", "Object", "create", "keys", "for<PERSON>ach", "eventName", "addEventListener", "unmounted", "removeEventListener", "Touch", "Refs", "Symbol", "getDescriptor", "obj", "key", "currentObj", "descriptor", "Reflect", "getOwnPropertyDescriptor", "Object", "getPrototypeOf", "undefined", "forwardRefs", "target", "_len", "arguments", "length", "refs", "Array", "_key", "Proxy", "get", "has", "startsWith", "ref", "value", "val", "bind", "set", "_", "setupState", "childRefs", "queue", "slice", "shift", "push", "makeVTooltipProps", "propsFactory", "id", "String", "interactive", "Boolean", "text", "omit", "makeVOverlayProps", "closeOnBack", "location", "locationStrategy", "eager", "min<PERSON><PERSON><PERSON>", "offset", "openOnClick", "openOnHover", "origin", "scrim", "scrollStrategy", "transition", "VTooltip", "genericComponent", "name", "props", "emits", "value", "setup", "_ref", "slots", "isActive", "useProxiedModel", "scopeId", "useScopeId", "uid", "useId", "toRef", "overlay", "ref", "computed", "split", "length", "activatorProps", "mergeProps", "useRender", "overlayProps", "VOverlay", "filterProps", "_createVNode", "_mergeProps", "class", "style", "$event", "activator", "default", "_len", "arguments", "args", "Array", "_key", "forwardRefs"]}