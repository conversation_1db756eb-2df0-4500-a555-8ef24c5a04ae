<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from './stores/auth'

const router = useRouter()
const authStore = useAuthStore()

const drawer = ref(false)

const navigationItems = [
  { title: '爱生首页', icon: 'mdi-home', to: '/' },
  { title: '爱生文苑', icon: 'mdi-book-open-page-variant', to: '/articles' },
  { title: '爱生图书馆', icon: 'mdi-library', to: '/library' },
  { title: '爱生教室', icon: 'mdi-school', to: '/classroom' },
  { title: '爱生积分', icon: 'mdi-star', to: '/points' },
]

const userMenuItems = [
  { title: '个人中心', icon: 'mdi-account', to: '/profile' },
  { title: '投稿文章', icon: 'mdi-pencil', to: '/submit-article' },
]

const handleLogout = () => {
  authStore.logout()
  router.push('/')
}

onMounted(() => {
  // 应用启动时获取用户信息
  if (authStore.token) {
    authStore.fetchCurrentUser()
  }
})
</script>

<template>
  <v-app>
    <!-- 导航栏 -->
    <v-app-bar app color="primary" dark height="64">
      <!-- 移动端菜单按钮 -->
      <v-app-bar-nav-icon @click="drawer = !drawer" class="d-lg-none"></v-app-bar-nav-icon>

      <!-- 网站标题 -->
      <v-toolbar-title class="text-h5 font-weight-bold">
        <router-link to="/" class="text-decoration-none text-white">
          爱生网
        </router-link>
      </v-toolbar-title>

      <!-- 桌面端导航菜单 -->
      <div class="d-none d-lg-flex ml-8">
        <v-btn
          v-for="item in navigationItems"
          :key="item.title"
          :to="item.to"
          variant="text"
          class="mx-1"
          size="large"
        >
          <v-icon start>{{ item.icon }}</v-icon>
          {{ item.title }}
        </v-btn>
      </div>

      <v-spacer></v-spacer>

      <!-- 用户菜单 -->
      <div v-if="authStore.isLoggedIn" class="d-flex align-center">
        <!-- 桌面端显示用户信息 -->
        <div class="d-none d-md-flex align-center mr-4">
          <v-chip color="white" variant="outlined" class="mr-2">
            <v-icon start>mdi-star</v-icon>
            {{ authStore.user?.points || 0 }} 积分
          </v-chip>
          <span class="text-white mr-2">{{ authStore.user?.nickname }}</span>
        </div>

        <v-menu>
          <template v-slot:activator="{ props }">
            <v-btn icon v-bind="props" size="large">
              <v-avatar size="36">
                <v-img
                  v-if="authStore.user?.avatar"
                  :src="authStore.user.avatar"
                  :alt="authStore.user.nickname"
                ></v-img>
                <v-icon v-else size="24">mdi-account-circle</v-icon>
              </v-avatar>
            </v-btn>
          </template>

          <v-list min-width="200">
            <v-list-item class="d-md-none">
              <v-list-item-title>{{ authStore.user?.nickname }}</v-list-item-title>
              <v-list-item-subtitle>积分: {{ authStore.user?.points }}</v-list-item-subtitle>
            </v-list-item>

            <v-divider class="d-md-none"></v-divider>

            <v-list-item
              v-for="item in userMenuItems"
              :key="item.title"
              :to="item.to"
            >
              <template v-slot:prepend>
                <v-icon>{{ item.icon }}</v-icon>
              </template>
              <v-list-item-title>{{ item.title }}</v-list-item-title>
            </v-list-item>

            <v-divider></v-divider>

            <v-list-item @click="handleLogout">
              <template v-slot:prepend>
                <v-icon>mdi-logout</v-icon>
              </template>
              <v-list-item-title>退出登录</v-list-item-title>
            </v-list-item>
          </v-list>
        </v-menu>
      </div>

      <!-- 未登录时显示登录/注册按钮 -->
      <div v-else class="d-flex">
        <v-btn to="/login" variant="outlined" color="white" class="mr-2">登录</v-btn>
        <v-btn to="/register" variant="flat" color="white">注册</v-btn>
      </div>
    </v-app-bar>

    <!-- 侧边导航栏 (仅移动端) -->
    <v-navigation-drawer v-model="drawer" app temporary class="d-lg-none">
      <v-list>
        <!-- 用户信息 (移动端) -->
        <v-list-item v-if="authStore.isLoggedIn" class="bg-primary">
          <template v-slot:prepend>
            <v-avatar>
              <v-img
                v-if="authStore.user?.avatar"
                :src="authStore.user.avatar"
                :alt="authStore.user.nickname"
              ></v-img>
              <v-icon v-else>mdi-account-circle</v-icon>
            </v-avatar>
          </template>
          <v-list-item-title class="text-white">{{ authStore.user?.nickname }}</v-list-item-title>
          <v-list-item-subtitle class="text-white">积分: {{ authStore.user?.points }}</v-list-item-subtitle>
        </v-list-item>

        <v-divider v-if="authStore.isLoggedIn"></v-divider>

        <!-- 导航菜单 -->
        <v-list-item
          v-for="item in navigationItems"
          :key="item.title"
          :to="item.to"
          @click="drawer = false"
        >
          <template v-slot:prepend>
            <v-icon>{{ item.icon }}</v-icon>
          </template>
          <v-list-item-title>{{ item.title }}</v-list-item-title>
        </v-list-item>

        <!-- 用户菜单 (移动端) -->
        <template v-if="authStore.isLoggedIn">
          <v-divider></v-divider>
          <v-list-item
            v-for="item in userMenuItems"
            :key="item.title"
            :to="item.to"
            @click="drawer = false"
          >
            <template v-slot:prepend>
              <v-icon>{{ item.icon }}</v-icon>
            </template>
            <v-list-item-title>{{ item.title }}</v-list-item-title>
          </v-list-item>
        </template>

        <!-- 登录/注册 (移动端未登录时) -->
        <template v-else>
          <v-divider></v-divider>
          <v-list-item to="/login" @click="drawer = false">
            <template v-slot:prepend>
              <v-icon>mdi-login</v-icon>
            </template>
            <v-list-item-title>登录</v-list-item-title>
          </v-list-item>
          <v-list-item to="/register" @click="drawer = false">
            <template v-slot:prepend>
              <v-icon>mdi-account-plus</v-icon>
            </template>
            <v-list-item-title>注册</v-list-item-title>
          </v-list-item>
        </template>
      </v-list>
    </v-navigation-drawer>

    <!-- 主内容区域 -->
    <v-main>
      <router-view />
    </v-main>

    <!-- 页脚 -->
    <v-footer color="grey-lighten-1" class="text-center pa-4">
      <v-container>
        <v-row>
          <v-col cols="12" md="6" class="text-md-left">
            <div class="text-h6 font-weight-bold mb-2">爱生网</div>
            <div class="text-body-2">综合性学习平台</div>
          </v-col>
          <v-col cols="12" md="6" class="text-md-right">
            <div class="text-body-2">
              © 2024 爱生网. All rights reserved.
            </div>
          </v-col>
        </v-row>
      </v-container>
    </v-footer>
  </v-app>
</template>

<style scoped>
.text-decoration-none {
  text-decoration: none;
}
</style>
