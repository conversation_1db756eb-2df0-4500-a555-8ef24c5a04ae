<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from './stores/auth'

const router = useRouter()
const authStore = useAuthStore()

const drawer = ref(false)

const navigationItems = [
  { title: '爱生首页', icon: 'mdi-home', to: '/' },
  { title: '爱生文苑', icon: 'mdi-book-open-page-variant', to: '/articles' },
  { title: '爱生图书馆', icon: 'mdi-library', to: '/library' },
  { title: '爱生教室', icon: 'mdi-school', to: '/classroom' },
  { title: '爱生积分', icon: 'mdi-star', to: '/points' },
]

const userMenuItems = [
  { title: '个人中心', icon: 'mdi-account', to: '/profile' },
  { title: '投稿文章', icon: 'mdi-pencil', to: '/submit-article' },
]

const handleLogout = () => {
  authStore.logout()
  router.push('/')
}

onMounted(() => {
  // 应用启动时获取用户信息
  if (authStore.token) {
    authStore.fetchCurrentUser()
  }
})
</script>

<template>
  <v-app>
    <!-- 导航栏 -->
    <v-app-bar app color="primary" dark>
      <v-app-bar-nav-icon @click="drawer = !drawer"></v-app-bar-nav-icon>

      <v-toolbar-title class="text-h6">
        <router-link to="/" class="text-decoration-none text-white">
          爱生网
        </router-link>
      </v-toolbar-title>

      <v-spacer></v-spacer>

      <!-- 桌面端导航菜单 -->
      <v-btn
        v-for="item in navigationItems"
        :key="item.title"
        :to="item.to"
        text
        class="d-none d-md-flex"
      >
        <v-icon left>{{ item.icon }}</v-icon>
        {{ item.title }}
      </v-btn>

      <v-spacer class="d-none d-md-flex"></v-spacer>

      <!-- 用户菜单 -->
      <div v-if="authStore.isLoggedIn">
        <v-menu>
          <template v-slot:activator="{ props }">
            <v-btn icon v-bind="props">
              <v-avatar size="32">
                <v-img
                  v-if="authStore.user?.avatar"
                  :src="authStore.user.avatar"
                  :alt="authStore.user.nickname"
                ></v-img>
                <v-icon v-else>mdi-account-circle</v-icon>
              </v-avatar>
            </v-btn>
          </template>

          <v-list>
            <v-list-item>
              <v-list-item-title>{{ authStore.user?.nickname }}</v-list-item-title>
              <v-list-item-subtitle>积分: {{ authStore.user?.points }}</v-list-item-subtitle>
            </v-list-item>

            <v-divider></v-divider>

            <v-list-item
              v-for="item in userMenuItems"
              :key="item.title"
              :to="item.to"
            >
              <template v-slot:prepend>
                <v-icon>{{ item.icon }}</v-icon>
              </template>
              <v-list-item-title>{{ item.title }}</v-list-item-title>
            </v-list-item>

            <v-divider></v-divider>

            <v-list-item @click="handleLogout">
              <template v-slot:prepend>
                <v-icon>mdi-logout</v-icon>
              </template>
              <v-list-item-title>退出登录</v-list-item-title>
            </v-list-item>
          </v-list>
        </v-menu>
      </div>

      <!-- 未登录时显示登录/注册按钮 -->
      <div v-else>
        <v-btn to="/login" text>登录</v-btn>
        <v-btn to="/register" text>注册</v-btn>
      </div>
    </v-app-bar>

    <!-- 侧边导航栏 -->
    <v-navigation-drawer v-model="drawer" app>
      <v-list>
        <v-list-item
          v-for="item in navigationItems"
          :key="item.title"
          :to="item.to"
          @click="drawer = false"
        >
          <template v-slot:prepend>
            <v-icon>{{ item.icon }}</v-icon>
          </template>
          <v-list-item-title>{{ item.title }}</v-list-item-title>
        </v-list-item>
      </v-list>
    </v-navigation-drawer>

    <!-- 主内容区域 -->
    <v-main>
      <router-view />
    </v-main>

    <!-- 页脚 -->
    <v-footer app color="grey-lighten-3" class="text-center">
      <div>
        © 2024 爱生网. All rights reserved.
      </div>
    </v-footer>
  </v-app>
</template>

<style scoped>
.text-decoration-none {
  text-decoration: none;
}
</style>
