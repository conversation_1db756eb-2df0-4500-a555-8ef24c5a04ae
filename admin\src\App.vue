<script setup>
import { ref, onMounted, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useAuthStore } from './stores/auth'

const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()

const drawer = ref(true)

const isAuthLayout = computed(() => route.meta?.layout === 'auth')

const navigationItems = [
  { title: '仪表盘', icon: 'mdi-view-dashboard', to: '/dashboard' },
  { title: '用户管理', icon: 'mdi-account-group', to: '/users', requiresAdmin: true },
  { title: '新闻管理', icon: 'mdi-newspaper', to: '/news' },
  { title: '文章管理', icon: 'mdi-book-open-page-variant', to: '/articles' },
  { title: '文章审核', icon: 'mdi-file-check', to: '/articles/pending' },
  { title: '视频管理', icon: 'mdi-video', to: '/videos' },
  { title: '图书管理', icon: 'mdi-library', to: '/books' },
  { title: '商品管理', icon: 'mdi-package-variant', to: '/products', requiresAdmin: true },
  { title: '系统设置', icon: 'mdi-cog', to: '/settings', requiresAdmin: true },
]

const filteredNavigationItems = computed(() => {
  return navigationItems.filter(item => {
    if (item.requiresAdmin && !authStore.isAdmin) return false
    return true
  })
})

const handleLogout = () => {
  authStore.logout()
  router.push('/login')
}

onMounted(() => {
  // 应用启动时获取用户信息
  if (authStore.token) {
    authStore.fetchCurrentUser()
  }
})
</script>

<template>
  <v-app>
    <!-- 认证布局 -->
    <template v-if="isAuthLayout">
      <v-main>
        <router-view />
      </v-main>
    </template>

    <!-- 主布局 -->
    <template v-else>
      <!-- 导航栏 -->
      <v-app-bar app color="primary" dark>
        <v-app-bar-nav-icon @click="drawer = !drawer"></v-app-bar-nav-icon>

        <v-toolbar-title class="text-h6">
          爱生网 - 管理后台
        </v-toolbar-title>

        <v-spacer></v-spacer>

        <!-- 用户菜单 -->
        <div v-if="authStore.isLoggedIn">
          <v-menu>
            <template v-slot:activator="{ props }">
              <v-btn icon v-bind="props">
                <v-avatar size="32">
                  <v-img
                    v-if="authStore.user?.avatar"
                    :src="authStore.user.avatar"
                    :alt="authStore.user.nickname"
                  ></v-img>
                  <v-icon v-else>mdi-account-circle</v-icon>
                </v-avatar>
              </v-btn>
            </template>

            <v-list>
              <v-list-item>
                <v-list-item-title>{{ authStore.user?.nickname }}</v-list-item-title>
                <v-list-item-subtitle>{{ authStore.user?.role === 'admin' ? '管理员' : '审核员' }}</v-list-item-subtitle>
              </v-list-item>

              <v-divider></v-divider>

              <v-list-item @click="handleLogout">
                <template v-slot:prepend>
                  <v-icon>mdi-logout</v-icon>
                </template>
                <v-list-item-title>退出登录</v-list-item-title>
              </v-list-item>
            </v-list>
          </v-menu>
        </div>
      </v-app-bar>

      <!-- 侧边导航栏 -->
      <v-navigation-drawer v-model="drawer" app>
        <v-list>
          <v-list-item
            v-for="item in filteredNavigationItems"
            :key="item.title"
            :to="item.to"
          >
            <template v-slot:prepend>
              <v-icon>{{ item.icon }}</v-icon>
            </template>
            <v-list-item-title>{{ item.title }}</v-list-item-title>
          </v-list-item>
        </v-list>
      </v-navigation-drawer>

      <!-- 主内容区域 -->
      <v-main>
        <v-container fluid>
          <router-view />
        </v-container>
      </v-main>
    </template>
  </v-app>
</template>

<style scoped>
.text-decoration-none {
  text-decoration: none;
}
</style>
