export declare const VFabTransition: {
    new (...args: any[]): import("vue").CreateComponentPublicInstanceWithMixins<{
        origin: string | undefined;
        disabled: boolean;
        group: boolean;
        mode: string | undefined;
        hideOnLeave: boolean;
        leaveAbsolute: boolean;
    } & {} & {
        $children?: import("vue").VNodeChild | {
            default?: (() => import("vue").VNodeChild) | undefined;
        } | (() => import("vue").VNodeChild);
        'v-slots'?: {
            default?: false | (() => import("vue").VNodeChild) | undefined;
        } | undefined;
    } & {
        "v-slot:default"?: false | (() => import("vue").VNodeChild) | undefined;
    }, () => import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
        [key: string]: any;
    }>, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, {
        origin: string | undefined;
        disabled: boolean;
        group: boolean;
        mode: string | undefined;
        hideOnLeave: boolean;
        leaveAbsolute: boolean;
    }, true, {}, import("vue").SlotsType<Partial<{
        default: () => import("vue").VNode[];
    }>>, import("vue").GlobalComponents, import("vue").GlobalDirectives, string, {}, any, import("vue").ComponentProvideOptions, {
        P: {};
        B: {};
        D: {};
        C: {};
        M: {};
        Defaults: {};
    }, {
        origin: string | undefined;
        disabled: boolean;
        group: boolean;
        mode: string | undefined;
        hideOnLeave: boolean;
        leaveAbsolute: boolean;
    } & {} & {
        $children?: import("vue").VNodeChild | {
            default?: (() => import("vue").VNodeChild) | undefined;
        } | (() => import("vue").VNodeChild);
        'v-slots'?: {
            default?: false | (() => import("vue").VNodeChild) | undefined;
        } | undefined;
    } & {
        "v-slot:default"?: false | (() => import("vue").VNodeChild) | undefined;
    }, () => import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
        [key: string]: any;
    }>, {}, {}, {}, {
        origin: string | undefined;
        disabled: boolean;
        group: boolean;
        mode: string | undefined;
        hideOnLeave: boolean;
        leaveAbsolute: boolean;
    }>;
    __isFragment?: never;
    __isTeleport?: never;
    __isSuspense?: never;
} & import("vue").ComponentOptionsBase<{
    origin: string | undefined;
    disabled: boolean;
    group: boolean;
    mode: string | undefined;
    hideOnLeave: boolean;
    leaveAbsolute: boolean;
} & {} & {
    $children?: import("vue").VNodeChild | {
        default?: (() => import("vue").VNodeChild) | undefined;
    } | (() => import("vue").VNodeChild);
    'v-slots'?: {
        default?: false | (() => import("vue").VNodeChild) | undefined;
    } | undefined;
} & {
    "v-slot:default"?: false | (() => import("vue").VNodeChild) | undefined;
}, () => import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
    [key: string]: any;
}>, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, {
    origin: string | undefined;
    disabled: boolean;
    group: boolean;
    mode: string | undefined;
    hideOnLeave: boolean;
    leaveAbsolute: boolean;
}, {}, string, import("vue").SlotsType<Partial<{
    default: () => import("vue").VNode[];
}>>, import("vue").GlobalComponents, import("vue").GlobalDirectives, string, import("vue").ComponentProvideOptions> & import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps & import("../../util/index.js").FilterPropsOptions<{
    disabled: BooleanConstructor;
    group: BooleanConstructor;
    hideOnLeave: BooleanConstructor;
    leaveAbsolute: BooleanConstructor;
    mode: {
        type: import("vue").PropType<string | undefined>;
        default: string | undefined;
    };
    origin: {
        type: import("vue").PropType<string | undefined>;
        default: string | undefined;
    };
}, import("vue").ExtractPropTypes<{
    disabled: BooleanConstructor;
    group: BooleanConstructor;
    hideOnLeave: BooleanConstructor;
    leaveAbsolute: BooleanConstructor;
    mode: {
        type: import("vue").PropType<string | undefined>;
        default: string | undefined;
    };
    origin: {
        type: import("vue").PropType<string | undefined>;
        default: string | undefined;
    };
}>>;
export declare const VDialogBottomTransition: {
    new (...args: any[]): import("vue").CreateComponentPublicInstanceWithMixins<{
        origin: string | undefined;
        disabled: boolean;
        group: boolean;
        mode: string | undefined;
        hideOnLeave: boolean;
        leaveAbsolute: boolean;
    } & {} & {
        $children?: import("vue").VNodeChild | {
            default?: (() => import("vue").VNodeChild) | undefined;
        } | (() => import("vue").VNodeChild);
        'v-slots'?: {
            default?: false | (() => import("vue").VNodeChild) | undefined;
        } | undefined;
    } & {
        "v-slot:default"?: false | (() => import("vue").VNodeChild) | undefined;
    }, () => import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
        [key: string]: any;
    }>, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, {
        origin: string | undefined;
        disabled: boolean;
        group: boolean;
        mode: string | undefined;
        hideOnLeave: boolean;
        leaveAbsolute: boolean;
    }, true, {}, import("vue").SlotsType<Partial<{
        default: () => import("vue").VNode[];
    }>>, import("vue").GlobalComponents, import("vue").GlobalDirectives, string, {}, any, import("vue").ComponentProvideOptions, {
        P: {};
        B: {};
        D: {};
        C: {};
        M: {};
        Defaults: {};
    }, {
        origin: string | undefined;
        disabled: boolean;
        group: boolean;
        mode: string | undefined;
        hideOnLeave: boolean;
        leaveAbsolute: boolean;
    } & {} & {
        $children?: import("vue").VNodeChild | {
            default?: (() => import("vue").VNodeChild) | undefined;
        } | (() => import("vue").VNodeChild);
        'v-slots'?: {
            default?: false | (() => import("vue").VNodeChild) | undefined;
        } | undefined;
    } & {
        "v-slot:default"?: false | (() => import("vue").VNodeChild) | undefined;
    }, () => import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
        [key: string]: any;
    }>, {}, {}, {}, {
        origin: string | undefined;
        disabled: boolean;
        group: boolean;
        mode: string | undefined;
        hideOnLeave: boolean;
        leaveAbsolute: boolean;
    }>;
    __isFragment?: never;
    __isTeleport?: never;
    __isSuspense?: never;
} & import("vue").ComponentOptionsBase<{
    origin: string | undefined;
    disabled: boolean;
    group: boolean;
    mode: string | undefined;
    hideOnLeave: boolean;
    leaveAbsolute: boolean;
} & {} & {
    $children?: import("vue").VNodeChild | {
        default?: (() => import("vue").VNodeChild) | undefined;
    } | (() => import("vue").VNodeChild);
    'v-slots'?: {
        default?: false | (() => import("vue").VNodeChild) | undefined;
    } | undefined;
} & {
    "v-slot:default"?: false | (() => import("vue").VNodeChild) | undefined;
}, () => import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
    [key: string]: any;
}>, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, {
    origin: string | undefined;
    disabled: boolean;
    group: boolean;
    mode: string | undefined;
    hideOnLeave: boolean;
    leaveAbsolute: boolean;
}, {}, string, import("vue").SlotsType<Partial<{
    default: () => import("vue").VNode[];
}>>, import("vue").GlobalComponents, import("vue").GlobalDirectives, string, import("vue").ComponentProvideOptions> & import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps & import("../../util/index.js").FilterPropsOptions<{
    disabled: BooleanConstructor;
    group: BooleanConstructor;
    hideOnLeave: BooleanConstructor;
    leaveAbsolute: BooleanConstructor;
    mode: {
        type: import("vue").PropType<string | undefined>;
        default: string | undefined;
    };
    origin: {
        type: import("vue").PropType<string | undefined>;
        default: string | undefined;
    };
}, import("vue").ExtractPropTypes<{
    disabled: BooleanConstructor;
    group: BooleanConstructor;
    hideOnLeave: BooleanConstructor;
    leaveAbsolute: BooleanConstructor;
    mode: {
        type: import("vue").PropType<string | undefined>;
        default: string | undefined;
    };
    origin: {
        type: import("vue").PropType<string | undefined>;
        default: string | undefined;
    };
}>>;
export declare const VDialogTopTransition: {
    new (...args: any[]): import("vue").CreateComponentPublicInstanceWithMixins<{
        origin: string | undefined;
        disabled: boolean;
        group: boolean;
        mode: string | undefined;
        hideOnLeave: boolean;
        leaveAbsolute: boolean;
    } & {} & {
        $children?: import("vue").VNodeChild | {
            default?: (() => import("vue").VNodeChild) | undefined;
        } | (() => import("vue").VNodeChild);
        'v-slots'?: {
            default?: false | (() => import("vue").VNodeChild) | undefined;
        } | undefined;
    } & {
        "v-slot:default"?: false | (() => import("vue").VNodeChild) | undefined;
    }, () => import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
        [key: string]: any;
    }>, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, {
        origin: string | undefined;
        disabled: boolean;
        group: boolean;
        mode: string | undefined;
        hideOnLeave: boolean;
        leaveAbsolute: boolean;
    }, true, {}, import("vue").SlotsType<Partial<{
        default: () => import("vue").VNode[];
    }>>, import("vue").GlobalComponents, import("vue").GlobalDirectives, string, {}, any, import("vue").ComponentProvideOptions, {
        P: {};
        B: {};
        D: {};
        C: {};
        M: {};
        Defaults: {};
    }, {
        origin: string | undefined;
        disabled: boolean;
        group: boolean;
        mode: string | undefined;
        hideOnLeave: boolean;
        leaveAbsolute: boolean;
    } & {} & {
        $children?: import("vue").VNodeChild | {
            default?: (() => import("vue").VNodeChild) | undefined;
        } | (() => import("vue").VNodeChild);
        'v-slots'?: {
            default?: false | (() => import("vue").VNodeChild) | undefined;
        } | undefined;
    } & {
        "v-slot:default"?: false | (() => import("vue").VNodeChild) | undefined;
    }, () => import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
        [key: string]: any;
    }>, {}, {}, {}, {
        origin: string | undefined;
        disabled: boolean;
        group: boolean;
        mode: string | undefined;
        hideOnLeave: boolean;
        leaveAbsolute: boolean;
    }>;
    __isFragment?: never;
    __isTeleport?: never;
    __isSuspense?: never;
} & import("vue").ComponentOptionsBase<{
    origin: string | undefined;
    disabled: boolean;
    group: boolean;
    mode: string | undefined;
    hideOnLeave: boolean;
    leaveAbsolute: boolean;
} & {} & {
    $children?: import("vue").VNodeChild | {
        default?: (() => import("vue").VNodeChild) | undefined;
    } | (() => import("vue").VNodeChild);
    'v-slots'?: {
        default?: false | (() => import("vue").VNodeChild) | undefined;
    } | undefined;
} & {
    "v-slot:default"?: false | (() => import("vue").VNodeChild) | undefined;
}, () => import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
    [key: string]: any;
}>, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, {
    origin: string | undefined;
    disabled: boolean;
    group: boolean;
    mode: string | undefined;
    hideOnLeave: boolean;
    leaveAbsolute: boolean;
}, {}, string, import("vue").SlotsType<Partial<{
    default: () => import("vue").VNode[];
}>>, import("vue").GlobalComponents, import("vue").GlobalDirectives, string, import("vue").ComponentProvideOptions> & import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps & import("../../util/index.js").FilterPropsOptions<{
    disabled: BooleanConstructor;
    group: BooleanConstructor;
    hideOnLeave: BooleanConstructor;
    leaveAbsolute: BooleanConstructor;
    mode: {
        type: import("vue").PropType<string | undefined>;
        default: string | undefined;
    };
    origin: {
        type: import("vue").PropType<string | undefined>;
        default: string | undefined;
    };
}, import("vue").ExtractPropTypes<{
    disabled: BooleanConstructor;
    group: BooleanConstructor;
    hideOnLeave: BooleanConstructor;
    leaveAbsolute: BooleanConstructor;
    mode: {
        type: import("vue").PropType<string | undefined>;
        default: string | undefined;
    };
    origin: {
        type: import("vue").PropType<string | undefined>;
        default: string | undefined;
    };
}>>;
export declare const VFadeTransition: {
    new (...args: any[]): import("vue").CreateComponentPublicInstanceWithMixins<{
        origin: string | undefined;
        disabled: boolean;
        group: boolean;
        mode: string | undefined;
        hideOnLeave: boolean;
        leaveAbsolute: boolean;
    } & {} & {
        $children?: import("vue").VNodeChild | {
            default?: (() => import("vue").VNodeChild) | undefined;
        } | (() => import("vue").VNodeChild);
        'v-slots'?: {
            default?: false | (() => import("vue").VNodeChild) | undefined;
        } | undefined;
    } & {
        "v-slot:default"?: false | (() => import("vue").VNodeChild) | undefined;
    }, () => import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
        [key: string]: any;
    }>, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, {
        origin: string | undefined;
        disabled: boolean;
        group: boolean;
        mode: string | undefined;
        hideOnLeave: boolean;
        leaveAbsolute: boolean;
    }, true, {}, import("vue").SlotsType<Partial<{
        default: () => import("vue").VNode[];
    }>>, import("vue").GlobalComponents, import("vue").GlobalDirectives, string, {}, any, import("vue").ComponentProvideOptions, {
        P: {};
        B: {};
        D: {};
        C: {};
        M: {};
        Defaults: {};
    }, {
        origin: string | undefined;
        disabled: boolean;
        group: boolean;
        mode: string | undefined;
        hideOnLeave: boolean;
        leaveAbsolute: boolean;
    } & {} & {
        $children?: import("vue").VNodeChild | {
            default?: (() => import("vue").VNodeChild) | undefined;
        } | (() => import("vue").VNodeChild);
        'v-slots'?: {
            default?: false | (() => import("vue").VNodeChild) | undefined;
        } | undefined;
    } & {
        "v-slot:default"?: false | (() => import("vue").VNodeChild) | undefined;
    }, () => import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
        [key: string]: any;
    }>, {}, {}, {}, {
        origin: string | undefined;
        disabled: boolean;
        group: boolean;
        mode: string | undefined;
        hideOnLeave: boolean;
        leaveAbsolute: boolean;
    }>;
    __isFragment?: never;
    __isTeleport?: never;
    __isSuspense?: never;
} & import("vue").ComponentOptionsBase<{
    origin: string | undefined;
    disabled: boolean;
    group: boolean;
    mode: string | undefined;
    hideOnLeave: boolean;
    leaveAbsolute: boolean;
} & {} & {
    $children?: import("vue").VNodeChild | {
        default?: (() => import("vue").VNodeChild) | undefined;
    } | (() => import("vue").VNodeChild);
    'v-slots'?: {
        default?: false | (() => import("vue").VNodeChild) | undefined;
    } | undefined;
} & {
    "v-slot:default"?: false | (() => import("vue").VNodeChild) | undefined;
}, () => import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
    [key: string]: any;
}>, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, {
    origin: string | undefined;
    disabled: boolean;
    group: boolean;
    mode: string | undefined;
    hideOnLeave: boolean;
    leaveAbsolute: boolean;
}, {}, string, import("vue").SlotsType<Partial<{
    default: () => import("vue").VNode[];
}>>, import("vue").GlobalComponents, import("vue").GlobalDirectives, string, import("vue").ComponentProvideOptions> & import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps & import("../../util/index.js").FilterPropsOptions<{
    disabled: BooleanConstructor;
    group: BooleanConstructor;
    hideOnLeave: BooleanConstructor;
    leaveAbsolute: BooleanConstructor;
    mode: {
        type: import("vue").PropType<string | undefined>;
        default: string | undefined;
    };
    origin: {
        type: import("vue").PropType<string | undefined>;
        default: string | undefined;
    };
}, import("vue").ExtractPropTypes<{
    disabled: BooleanConstructor;
    group: BooleanConstructor;
    hideOnLeave: BooleanConstructor;
    leaveAbsolute: BooleanConstructor;
    mode: {
        type: import("vue").PropType<string | undefined>;
        default: string | undefined;
    };
    origin: {
        type: import("vue").PropType<string | undefined>;
        default: string | undefined;
    };
}>>;
export declare const VScaleTransition: {
    new (...args: any[]): import("vue").CreateComponentPublicInstanceWithMixins<{
        origin: string | undefined;
        disabled: boolean;
        group: boolean;
        mode: string | undefined;
        hideOnLeave: boolean;
        leaveAbsolute: boolean;
    } & {} & {
        $children?: import("vue").VNodeChild | {
            default?: (() => import("vue").VNodeChild) | undefined;
        } | (() => import("vue").VNodeChild);
        'v-slots'?: {
            default?: false | (() => import("vue").VNodeChild) | undefined;
        } | undefined;
    } & {
        "v-slot:default"?: false | (() => import("vue").VNodeChild) | undefined;
    }, () => import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
        [key: string]: any;
    }>, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, {
        origin: string | undefined;
        disabled: boolean;
        group: boolean;
        mode: string | undefined;
        hideOnLeave: boolean;
        leaveAbsolute: boolean;
    }, true, {}, import("vue").SlotsType<Partial<{
        default: () => import("vue").VNode[];
    }>>, import("vue").GlobalComponents, import("vue").GlobalDirectives, string, {}, any, import("vue").ComponentProvideOptions, {
        P: {};
        B: {};
        D: {};
        C: {};
        M: {};
        Defaults: {};
    }, {
        origin: string | undefined;
        disabled: boolean;
        group: boolean;
        mode: string | undefined;
        hideOnLeave: boolean;
        leaveAbsolute: boolean;
    } & {} & {
        $children?: import("vue").VNodeChild | {
            default?: (() => import("vue").VNodeChild) | undefined;
        } | (() => import("vue").VNodeChild);
        'v-slots'?: {
            default?: false | (() => import("vue").VNodeChild) | undefined;
        } | undefined;
    } & {
        "v-slot:default"?: false | (() => import("vue").VNodeChild) | undefined;
    }, () => import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
        [key: string]: any;
    }>, {}, {}, {}, {
        origin: string | undefined;
        disabled: boolean;
        group: boolean;
        mode: string | undefined;
        hideOnLeave: boolean;
        leaveAbsolute: boolean;
    }>;
    __isFragment?: never;
    __isTeleport?: never;
    __isSuspense?: never;
} & import("vue").ComponentOptionsBase<{
    origin: string | undefined;
    disabled: boolean;
    group: boolean;
    mode: string | undefined;
    hideOnLeave: boolean;
    leaveAbsolute: boolean;
} & {} & {
    $children?: import("vue").VNodeChild | {
        default?: (() => import("vue").VNodeChild) | undefined;
    } | (() => import("vue").VNodeChild);
    'v-slots'?: {
        default?: false | (() => import("vue").VNodeChild) | undefined;
    } | undefined;
} & {
    "v-slot:default"?: false | (() => import("vue").VNodeChild) | undefined;
}, () => import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
    [key: string]: any;
}>, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, {
    origin: string | undefined;
    disabled: boolean;
    group: boolean;
    mode: string | undefined;
    hideOnLeave: boolean;
    leaveAbsolute: boolean;
}, {}, string, import("vue").SlotsType<Partial<{
    default: () => import("vue").VNode[];
}>>, import("vue").GlobalComponents, import("vue").GlobalDirectives, string, import("vue").ComponentProvideOptions> & import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps & import("../../util/index.js").FilterPropsOptions<{
    disabled: BooleanConstructor;
    group: BooleanConstructor;
    hideOnLeave: BooleanConstructor;
    leaveAbsolute: BooleanConstructor;
    mode: {
        type: import("vue").PropType<string | undefined>;
        default: string | undefined;
    };
    origin: {
        type: import("vue").PropType<string | undefined>;
        default: string | undefined;
    };
}, import("vue").ExtractPropTypes<{
    disabled: BooleanConstructor;
    group: BooleanConstructor;
    hideOnLeave: BooleanConstructor;
    leaveAbsolute: BooleanConstructor;
    mode: {
        type: import("vue").PropType<string | undefined>;
        default: string | undefined;
    };
    origin: {
        type: import("vue").PropType<string | undefined>;
        default: string | undefined;
    };
}>>;
export declare const VScrollXTransition: {
    new (...args: any[]): import("vue").CreateComponentPublicInstanceWithMixins<{
        origin: string | undefined;
        disabled: boolean;
        group: boolean;
        mode: string | undefined;
        hideOnLeave: boolean;
        leaveAbsolute: boolean;
    } & {} & {
        $children?: import("vue").VNodeChild | {
            default?: (() => import("vue").VNodeChild) | undefined;
        } | (() => import("vue").VNodeChild);
        'v-slots'?: {
            default?: false | (() => import("vue").VNodeChild) | undefined;
        } | undefined;
    } & {
        "v-slot:default"?: false | (() => import("vue").VNodeChild) | undefined;
    }, () => import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
        [key: string]: any;
    }>, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, {
        origin: string | undefined;
        disabled: boolean;
        group: boolean;
        mode: string | undefined;
        hideOnLeave: boolean;
        leaveAbsolute: boolean;
    }, true, {}, import("vue").SlotsType<Partial<{
        default: () => import("vue").VNode[];
    }>>, import("vue").GlobalComponents, import("vue").GlobalDirectives, string, {}, any, import("vue").ComponentProvideOptions, {
        P: {};
        B: {};
        D: {};
        C: {};
        M: {};
        Defaults: {};
    }, {
        origin: string | undefined;
        disabled: boolean;
        group: boolean;
        mode: string | undefined;
        hideOnLeave: boolean;
        leaveAbsolute: boolean;
    } & {} & {
        $children?: import("vue").VNodeChild | {
            default?: (() => import("vue").VNodeChild) | undefined;
        } | (() => import("vue").VNodeChild);
        'v-slots'?: {
            default?: false | (() => import("vue").VNodeChild) | undefined;
        } | undefined;
    } & {
        "v-slot:default"?: false | (() => import("vue").VNodeChild) | undefined;
    }, () => import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
        [key: string]: any;
    }>, {}, {}, {}, {
        origin: string | undefined;
        disabled: boolean;
        group: boolean;
        mode: string | undefined;
        hideOnLeave: boolean;
        leaveAbsolute: boolean;
    }>;
    __isFragment?: never;
    __isTeleport?: never;
    __isSuspense?: never;
} & import("vue").ComponentOptionsBase<{
    origin: string | undefined;
    disabled: boolean;
    group: boolean;
    mode: string | undefined;
    hideOnLeave: boolean;
    leaveAbsolute: boolean;
} & {} & {
    $children?: import("vue").VNodeChild | {
        default?: (() => import("vue").VNodeChild) | undefined;
    } | (() => import("vue").VNodeChild);
    'v-slots'?: {
        default?: false | (() => import("vue").VNodeChild) | undefined;
    } | undefined;
} & {
    "v-slot:default"?: false | (() => import("vue").VNodeChild) | undefined;
}, () => import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
    [key: string]: any;
}>, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, {
    origin: string | undefined;
    disabled: boolean;
    group: boolean;
    mode: string | undefined;
    hideOnLeave: boolean;
    leaveAbsolute: boolean;
}, {}, string, import("vue").SlotsType<Partial<{
    default: () => import("vue").VNode[];
}>>, import("vue").GlobalComponents, import("vue").GlobalDirectives, string, import("vue").ComponentProvideOptions> & import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps & import("../../util/index.js").FilterPropsOptions<{
    disabled: BooleanConstructor;
    group: BooleanConstructor;
    hideOnLeave: BooleanConstructor;
    leaveAbsolute: BooleanConstructor;
    mode: {
        type: import("vue").PropType<string | undefined>;
        default: string | undefined;
    };
    origin: {
        type: import("vue").PropType<string | undefined>;
        default: string | undefined;
    };
}, import("vue").ExtractPropTypes<{
    disabled: BooleanConstructor;
    group: BooleanConstructor;
    hideOnLeave: BooleanConstructor;
    leaveAbsolute: BooleanConstructor;
    mode: {
        type: import("vue").PropType<string | undefined>;
        default: string | undefined;
    };
    origin: {
        type: import("vue").PropType<string | undefined>;
        default: string | undefined;
    };
}>>;
export declare const VScrollXReverseTransition: {
    new (...args: any[]): import("vue").CreateComponentPublicInstanceWithMixins<{
        origin: string | undefined;
        disabled: boolean;
        group: boolean;
        mode: string | undefined;
        hideOnLeave: boolean;
        leaveAbsolute: boolean;
    } & {} & {
        $children?: import("vue").VNodeChild | {
            default?: (() => import("vue").VNodeChild) | undefined;
        } | (() => import("vue").VNodeChild);
        'v-slots'?: {
            default?: false | (() => import("vue").VNodeChild) | undefined;
        } | undefined;
    } & {
        "v-slot:default"?: false | (() => import("vue").VNodeChild) | undefined;
    }, () => import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
        [key: string]: any;
    }>, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, {
        origin: string | undefined;
        disabled: boolean;
        group: boolean;
        mode: string | undefined;
        hideOnLeave: boolean;
        leaveAbsolute: boolean;
    }, true, {}, import("vue").SlotsType<Partial<{
        default: () => import("vue").VNode[];
    }>>, import("vue").GlobalComponents, import("vue").GlobalDirectives, string, {}, any, import("vue").ComponentProvideOptions, {
        P: {};
        B: {};
        D: {};
        C: {};
        M: {};
        Defaults: {};
    }, {
        origin: string | undefined;
        disabled: boolean;
        group: boolean;
        mode: string | undefined;
        hideOnLeave: boolean;
        leaveAbsolute: boolean;
    } & {} & {
        $children?: import("vue").VNodeChild | {
            default?: (() => import("vue").VNodeChild) | undefined;
        } | (() => import("vue").VNodeChild);
        'v-slots'?: {
            default?: false | (() => import("vue").VNodeChild) | undefined;
        } | undefined;
    } & {
        "v-slot:default"?: false | (() => import("vue").VNodeChild) | undefined;
    }, () => import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
        [key: string]: any;
    }>, {}, {}, {}, {
        origin: string | undefined;
        disabled: boolean;
        group: boolean;
        mode: string | undefined;
        hideOnLeave: boolean;
        leaveAbsolute: boolean;
    }>;
    __isFragment?: never;
    __isTeleport?: never;
    __isSuspense?: never;
} & import("vue").ComponentOptionsBase<{
    origin: string | undefined;
    disabled: boolean;
    group: boolean;
    mode: string | undefined;
    hideOnLeave: boolean;
    leaveAbsolute: boolean;
} & {} & {
    $children?: import("vue").VNodeChild | {
        default?: (() => import("vue").VNodeChild) | undefined;
    } | (() => import("vue").VNodeChild);
    'v-slots'?: {
        default?: false | (() => import("vue").VNodeChild) | undefined;
    } | undefined;
} & {
    "v-slot:default"?: false | (() => import("vue").VNodeChild) | undefined;
}, () => import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
    [key: string]: any;
}>, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, {
    origin: string | undefined;
    disabled: boolean;
    group: boolean;
    mode: string | undefined;
    hideOnLeave: boolean;
    leaveAbsolute: boolean;
}, {}, string, import("vue").SlotsType<Partial<{
    default: () => import("vue").VNode[];
}>>, import("vue").GlobalComponents, import("vue").GlobalDirectives, string, import("vue").ComponentProvideOptions> & import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps & import("../../util/index.js").FilterPropsOptions<{
    disabled: BooleanConstructor;
    group: BooleanConstructor;
    hideOnLeave: BooleanConstructor;
    leaveAbsolute: BooleanConstructor;
    mode: {
        type: import("vue").PropType<string | undefined>;
        default: string | undefined;
    };
    origin: {
        type: import("vue").PropType<string | undefined>;
        default: string | undefined;
    };
}, import("vue").ExtractPropTypes<{
    disabled: BooleanConstructor;
    group: BooleanConstructor;
    hideOnLeave: BooleanConstructor;
    leaveAbsolute: BooleanConstructor;
    mode: {
        type: import("vue").PropType<string | undefined>;
        default: string | undefined;
    };
    origin: {
        type: import("vue").PropType<string | undefined>;
        default: string | undefined;
    };
}>>;
export declare const VScrollYTransition: {
    new (...args: any[]): import("vue").CreateComponentPublicInstanceWithMixins<{
        origin: string | undefined;
        disabled: boolean;
        group: boolean;
        mode: string | undefined;
        hideOnLeave: boolean;
        leaveAbsolute: boolean;
    } & {} & {
        $children?: import("vue").VNodeChild | {
            default?: (() => import("vue").VNodeChild) | undefined;
        } | (() => import("vue").VNodeChild);
        'v-slots'?: {
            default?: false | (() => import("vue").VNodeChild) | undefined;
        } | undefined;
    } & {
        "v-slot:default"?: false | (() => import("vue").VNodeChild) | undefined;
    }, () => import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
        [key: string]: any;
    }>, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, {
        origin: string | undefined;
        disabled: boolean;
        group: boolean;
        mode: string | undefined;
        hideOnLeave: boolean;
        leaveAbsolute: boolean;
    }, true, {}, import("vue").SlotsType<Partial<{
        default: () => import("vue").VNode[];
    }>>, import("vue").GlobalComponents, import("vue").GlobalDirectives, string, {}, any, import("vue").ComponentProvideOptions, {
        P: {};
        B: {};
        D: {};
        C: {};
        M: {};
        Defaults: {};
    }, {
        origin: string | undefined;
        disabled: boolean;
        group: boolean;
        mode: string | undefined;
        hideOnLeave: boolean;
        leaveAbsolute: boolean;
    } & {} & {
        $children?: import("vue").VNodeChild | {
            default?: (() => import("vue").VNodeChild) | undefined;
        } | (() => import("vue").VNodeChild);
        'v-slots'?: {
            default?: false | (() => import("vue").VNodeChild) | undefined;
        } | undefined;
    } & {
        "v-slot:default"?: false | (() => import("vue").VNodeChild) | undefined;
    }, () => import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
        [key: string]: any;
    }>, {}, {}, {}, {
        origin: string | undefined;
        disabled: boolean;
        group: boolean;
        mode: string | undefined;
        hideOnLeave: boolean;
        leaveAbsolute: boolean;
    }>;
    __isFragment?: never;
    __isTeleport?: never;
    __isSuspense?: never;
} & import("vue").ComponentOptionsBase<{
    origin: string | undefined;
    disabled: boolean;
    group: boolean;
    mode: string | undefined;
    hideOnLeave: boolean;
    leaveAbsolute: boolean;
} & {} & {
    $children?: import("vue").VNodeChild | {
        default?: (() => import("vue").VNodeChild) | undefined;
    } | (() => import("vue").VNodeChild);
    'v-slots'?: {
        default?: false | (() => import("vue").VNodeChild) | undefined;
    } | undefined;
} & {
    "v-slot:default"?: false | (() => import("vue").VNodeChild) | undefined;
}, () => import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
    [key: string]: any;
}>, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, {
    origin: string | undefined;
    disabled: boolean;
    group: boolean;
    mode: string | undefined;
    hideOnLeave: boolean;
    leaveAbsolute: boolean;
}, {}, string, import("vue").SlotsType<Partial<{
    default: () => import("vue").VNode[];
}>>, import("vue").GlobalComponents, import("vue").GlobalDirectives, string, import("vue").ComponentProvideOptions> & import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps & import("../../util/index.js").FilterPropsOptions<{
    disabled: BooleanConstructor;
    group: BooleanConstructor;
    hideOnLeave: BooleanConstructor;
    leaveAbsolute: BooleanConstructor;
    mode: {
        type: import("vue").PropType<string | undefined>;
        default: string | undefined;
    };
    origin: {
        type: import("vue").PropType<string | undefined>;
        default: string | undefined;
    };
}, import("vue").ExtractPropTypes<{
    disabled: BooleanConstructor;
    group: BooleanConstructor;
    hideOnLeave: BooleanConstructor;
    leaveAbsolute: BooleanConstructor;
    mode: {
        type: import("vue").PropType<string | undefined>;
        default: string | undefined;
    };
    origin: {
        type: import("vue").PropType<string | undefined>;
        default: string | undefined;
    };
}>>;
export declare const VScrollYReverseTransition: {
    new (...args: any[]): import("vue").CreateComponentPublicInstanceWithMixins<{
        origin: string | undefined;
        disabled: boolean;
        group: boolean;
        mode: string | undefined;
        hideOnLeave: boolean;
        leaveAbsolute: boolean;
    } & {} & {
        $children?: import("vue").VNodeChild | {
            default?: (() => import("vue").VNodeChild) | undefined;
        } | (() => import("vue").VNodeChild);
        'v-slots'?: {
            default?: false | (() => import("vue").VNodeChild) | undefined;
        } | undefined;
    } & {
        "v-slot:default"?: false | (() => import("vue").VNodeChild) | undefined;
    }, () => import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
        [key: string]: any;
    }>, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, {
        origin: string | undefined;
        disabled: boolean;
        group: boolean;
        mode: string | undefined;
        hideOnLeave: boolean;
        leaveAbsolute: boolean;
    }, true, {}, import("vue").SlotsType<Partial<{
        default: () => import("vue").VNode[];
    }>>, import("vue").GlobalComponents, import("vue").GlobalDirectives, string, {}, any, import("vue").ComponentProvideOptions, {
        P: {};
        B: {};
        D: {};
        C: {};
        M: {};
        Defaults: {};
    }, {
        origin: string | undefined;
        disabled: boolean;
        group: boolean;
        mode: string | undefined;
        hideOnLeave: boolean;
        leaveAbsolute: boolean;
    } & {} & {
        $children?: import("vue").VNodeChild | {
            default?: (() => import("vue").VNodeChild) | undefined;
        } | (() => import("vue").VNodeChild);
        'v-slots'?: {
            default?: false | (() => import("vue").VNodeChild) | undefined;
        } | undefined;
    } & {
        "v-slot:default"?: false | (() => import("vue").VNodeChild) | undefined;
    }, () => import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
        [key: string]: any;
    }>, {}, {}, {}, {
        origin: string | undefined;
        disabled: boolean;
        group: boolean;
        mode: string | undefined;
        hideOnLeave: boolean;
        leaveAbsolute: boolean;
    }>;
    __isFragment?: never;
    __isTeleport?: never;
    __isSuspense?: never;
} & import("vue").ComponentOptionsBase<{
    origin: string | undefined;
    disabled: boolean;
    group: boolean;
    mode: string | undefined;
    hideOnLeave: boolean;
    leaveAbsolute: boolean;
} & {} & {
    $children?: import("vue").VNodeChild | {
        default?: (() => import("vue").VNodeChild) | undefined;
    } | (() => import("vue").VNodeChild);
    'v-slots'?: {
        default?: false | (() => import("vue").VNodeChild) | undefined;
    } | undefined;
} & {
    "v-slot:default"?: false | (() => import("vue").VNodeChild) | undefined;
}, () => import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
    [key: string]: any;
}>, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, {
    origin: string | undefined;
    disabled: boolean;
    group: boolean;
    mode: string | undefined;
    hideOnLeave: boolean;
    leaveAbsolute: boolean;
}, {}, string, import("vue").SlotsType<Partial<{
    default: () => import("vue").VNode[];
}>>, import("vue").GlobalComponents, import("vue").GlobalDirectives, string, import("vue").ComponentProvideOptions> & import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps & import("../../util/index.js").FilterPropsOptions<{
    disabled: BooleanConstructor;
    group: BooleanConstructor;
    hideOnLeave: BooleanConstructor;
    leaveAbsolute: BooleanConstructor;
    mode: {
        type: import("vue").PropType<string | undefined>;
        default: string | undefined;
    };
    origin: {
        type: import("vue").PropType<string | undefined>;
        default: string | undefined;
    };
}, import("vue").ExtractPropTypes<{
    disabled: BooleanConstructor;
    group: BooleanConstructor;
    hideOnLeave: BooleanConstructor;
    leaveAbsolute: BooleanConstructor;
    mode: {
        type: import("vue").PropType<string | undefined>;
        default: string | undefined;
    };
    origin: {
        type: import("vue").PropType<string | undefined>;
        default: string | undefined;
    };
}>>;
export declare const VSlideXTransition: {
    new (...args: any[]): import("vue").CreateComponentPublicInstanceWithMixins<{
        origin: string | undefined;
        disabled: boolean;
        group: boolean;
        mode: string | undefined;
        hideOnLeave: boolean;
        leaveAbsolute: boolean;
    } & {} & {
        $children?: import("vue").VNodeChild | {
            default?: (() => import("vue").VNodeChild) | undefined;
        } | (() => import("vue").VNodeChild);
        'v-slots'?: {
            default?: false | (() => import("vue").VNodeChild) | undefined;
        } | undefined;
    } & {
        "v-slot:default"?: false | (() => import("vue").VNodeChild) | undefined;
    }, () => import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
        [key: string]: any;
    }>, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, {
        origin: string | undefined;
        disabled: boolean;
        group: boolean;
        mode: string | undefined;
        hideOnLeave: boolean;
        leaveAbsolute: boolean;
    }, true, {}, import("vue").SlotsType<Partial<{
        default: () => import("vue").VNode[];
    }>>, import("vue").GlobalComponents, import("vue").GlobalDirectives, string, {}, any, import("vue").ComponentProvideOptions, {
        P: {};
        B: {};
        D: {};
        C: {};
        M: {};
        Defaults: {};
    }, {
        origin: string | undefined;
        disabled: boolean;
        group: boolean;
        mode: string | undefined;
        hideOnLeave: boolean;
        leaveAbsolute: boolean;
    } & {} & {
        $children?: import("vue").VNodeChild | {
            default?: (() => import("vue").VNodeChild) | undefined;
        } | (() => import("vue").VNodeChild);
        'v-slots'?: {
            default?: false | (() => import("vue").VNodeChild) | undefined;
        } | undefined;
    } & {
        "v-slot:default"?: false | (() => import("vue").VNodeChild) | undefined;
    }, () => import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
        [key: string]: any;
    }>, {}, {}, {}, {
        origin: string | undefined;
        disabled: boolean;
        group: boolean;
        mode: string | undefined;
        hideOnLeave: boolean;
        leaveAbsolute: boolean;
    }>;
    __isFragment?: never;
    __isTeleport?: never;
    __isSuspense?: never;
} & import("vue").ComponentOptionsBase<{
    origin: string | undefined;
    disabled: boolean;
    group: boolean;
    mode: string | undefined;
    hideOnLeave: boolean;
    leaveAbsolute: boolean;
} & {} & {
    $children?: import("vue").VNodeChild | {
        default?: (() => import("vue").VNodeChild) | undefined;
    } | (() => import("vue").VNodeChild);
    'v-slots'?: {
        default?: false | (() => import("vue").VNodeChild) | undefined;
    } | undefined;
} & {
    "v-slot:default"?: false | (() => import("vue").VNodeChild) | undefined;
}, () => import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
    [key: string]: any;
}>, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, {
    origin: string | undefined;
    disabled: boolean;
    group: boolean;
    mode: string | undefined;
    hideOnLeave: boolean;
    leaveAbsolute: boolean;
}, {}, string, import("vue").SlotsType<Partial<{
    default: () => import("vue").VNode[];
}>>, import("vue").GlobalComponents, import("vue").GlobalDirectives, string, import("vue").ComponentProvideOptions> & import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps & import("../../util/index.js").FilterPropsOptions<{
    disabled: BooleanConstructor;
    group: BooleanConstructor;
    hideOnLeave: BooleanConstructor;
    leaveAbsolute: BooleanConstructor;
    mode: {
        type: import("vue").PropType<string | undefined>;
        default: string | undefined;
    };
    origin: {
        type: import("vue").PropType<string | undefined>;
        default: string | undefined;
    };
}, import("vue").ExtractPropTypes<{
    disabled: BooleanConstructor;
    group: BooleanConstructor;
    hideOnLeave: BooleanConstructor;
    leaveAbsolute: BooleanConstructor;
    mode: {
        type: import("vue").PropType<string | undefined>;
        default: string | undefined;
    };
    origin: {
        type: import("vue").PropType<string | undefined>;
        default: string | undefined;
    };
}>>;
export declare const VSlideXReverseTransition: {
    new (...args: any[]): import("vue").CreateComponentPublicInstanceWithMixins<{
        origin: string | undefined;
        disabled: boolean;
        group: boolean;
        mode: string | undefined;
        hideOnLeave: boolean;
        leaveAbsolute: boolean;
    } & {} & {
        $children?: import("vue").VNodeChild | {
            default?: (() => import("vue").VNodeChild) | undefined;
        } | (() => import("vue").VNodeChild);
        'v-slots'?: {
            default?: false | (() => import("vue").VNodeChild) | undefined;
        } | undefined;
    } & {
        "v-slot:default"?: false | (() => import("vue").VNodeChild) | undefined;
    }, () => import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
        [key: string]: any;
    }>, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, {
        origin: string | undefined;
        disabled: boolean;
        group: boolean;
        mode: string | undefined;
        hideOnLeave: boolean;
        leaveAbsolute: boolean;
    }, true, {}, import("vue").SlotsType<Partial<{
        default: () => import("vue").VNode[];
    }>>, import("vue").GlobalComponents, import("vue").GlobalDirectives, string, {}, any, import("vue").ComponentProvideOptions, {
        P: {};
        B: {};
        D: {};
        C: {};
        M: {};
        Defaults: {};
    }, {
        origin: string | undefined;
        disabled: boolean;
        group: boolean;
        mode: string | undefined;
        hideOnLeave: boolean;
        leaveAbsolute: boolean;
    } & {} & {
        $children?: import("vue").VNodeChild | {
            default?: (() => import("vue").VNodeChild) | undefined;
        } | (() => import("vue").VNodeChild);
        'v-slots'?: {
            default?: false | (() => import("vue").VNodeChild) | undefined;
        } | undefined;
    } & {
        "v-slot:default"?: false | (() => import("vue").VNodeChild) | undefined;
    }, () => import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
        [key: string]: any;
    }>, {}, {}, {}, {
        origin: string | undefined;
        disabled: boolean;
        group: boolean;
        mode: string | undefined;
        hideOnLeave: boolean;
        leaveAbsolute: boolean;
    }>;
    __isFragment?: never;
    __isTeleport?: never;
    __isSuspense?: never;
} & import("vue").ComponentOptionsBase<{
    origin: string | undefined;
    disabled: boolean;
    group: boolean;
    mode: string | undefined;
    hideOnLeave: boolean;
    leaveAbsolute: boolean;
} & {} & {
    $children?: import("vue").VNodeChild | {
        default?: (() => import("vue").VNodeChild) | undefined;
    } | (() => import("vue").VNodeChild);
    'v-slots'?: {
        default?: false | (() => import("vue").VNodeChild) | undefined;
    } | undefined;
} & {
    "v-slot:default"?: false | (() => import("vue").VNodeChild) | undefined;
}, () => import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
    [key: string]: any;
}>, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, {
    origin: string | undefined;
    disabled: boolean;
    group: boolean;
    mode: string | undefined;
    hideOnLeave: boolean;
    leaveAbsolute: boolean;
}, {}, string, import("vue").SlotsType<Partial<{
    default: () => import("vue").VNode[];
}>>, import("vue").GlobalComponents, import("vue").GlobalDirectives, string, import("vue").ComponentProvideOptions> & import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps & import("../../util/index.js").FilterPropsOptions<{
    disabled: BooleanConstructor;
    group: BooleanConstructor;
    hideOnLeave: BooleanConstructor;
    leaveAbsolute: BooleanConstructor;
    mode: {
        type: import("vue").PropType<string | undefined>;
        default: string | undefined;
    };
    origin: {
        type: import("vue").PropType<string | undefined>;
        default: string | undefined;
    };
}, import("vue").ExtractPropTypes<{
    disabled: BooleanConstructor;
    group: BooleanConstructor;
    hideOnLeave: BooleanConstructor;
    leaveAbsolute: BooleanConstructor;
    mode: {
        type: import("vue").PropType<string | undefined>;
        default: string | undefined;
    };
    origin: {
        type: import("vue").PropType<string | undefined>;
        default: string | undefined;
    };
}>>;
export declare const VSlideYTransition: {
    new (...args: any[]): import("vue").CreateComponentPublicInstanceWithMixins<{
        origin: string | undefined;
        disabled: boolean;
        group: boolean;
        mode: string | undefined;
        hideOnLeave: boolean;
        leaveAbsolute: boolean;
    } & {} & {
        $children?: import("vue").VNodeChild | {
            default?: (() => import("vue").VNodeChild) | undefined;
        } | (() => import("vue").VNodeChild);
        'v-slots'?: {
            default?: false | (() => import("vue").VNodeChild) | undefined;
        } | undefined;
    } & {
        "v-slot:default"?: false | (() => import("vue").VNodeChild) | undefined;
    }, () => import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
        [key: string]: any;
    }>, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, {
        origin: string | undefined;
        disabled: boolean;
        group: boolean;
        mode: string | undefined;
        hideOnLeave: boolean;
        leaveAbsolute: boolean;
    }, true, {}, import("vue").SlotsType<Partial<{
        default: () => import("vue").VNode[];
    }>>, import("vue").GlobalComponents, import("vue").GlobalDirectives, string, {}, any, import("vue").ComponentProvideOptions, {
        P: {};
        B: {};
        D: {};
        C: {};
        M: {};
        Defaults: {};
    }, {
        origin: string | undefined;
        disabled: boolean;
        group: boolean;
        mode: string | undefined;
        hideOnLeave: boolean;
        leaveAbsolute: boolean;
    } & {} & {
        $children?: import("vue").VNodeChild | {
            default?: (() => import("vue").VNodeChild) | undefined;
        } | (() => import("vue").VNodeChild);
        'v-slots'?: {
            default?: false | (() => import("vue").VNodeChild) | undefined;
        } | undefined;
    } & {
        "v-slot:default"?: false | (() => import("vue").VNodeChild) | undefined;
    }, () => import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
        [key: string]: any;
    }>, {}, {}, {}, {
        origin: string | undefined;
        disabled: boolean;
        group: boolean;
        mode: string | undefined;
        hideOnLeave: boolean;
        leaveAbsolute: boolean;
    }>;
    __isFragment?: never;
    __isTeleport?: never;
    __isSuspense?: never;
} & import("vue").ComponentOptionsBase<{
    origin: string | undefined;
    disabled: boolean;
    group: boolean;
    mode: string | undefined;
    hideOnLeave: boolean;
    leaveAbsolute: boolean;
} & {} & {
    $children?: import("vue").VNodeChild | {
        default?: (() => import("vue").VNodeChild) | undefined;
    } | (() => import("vue").VNodeChild);
    'v-slots'?: {
        default?: false | (() => import("vue").VNodeChild) | undefined;
    } | undefined;
} & {
    "v-slot:default"?: false | (() => import("vue").VNodeChild) | undefined;
}, () => import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
    [key: string]: any;
}>, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, {
    origin: string | undefined;
    disabled: boolean;
    group: boolean;
    mode: string | undefined;
    hideOnLeave: boolean;
    leaveAbsolute: boolean;
}, {}, string, import("vue").SlotsType<Partial<{
    default: () => import("vue").VNode[];
}>>, import("vue").GlobalComponents, import("vue").GlobalDirectives, string, import("vue").ComponentProvideOptions> & import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps & import("../../util/index.js").FilterPropsOptions<{
    disabled: BooleanConstructor;
    group: BooleanConstructor;
    hideOnLeave: BooleanConstructor;
    leaveAbsolute: BooleanConstructor;
    mode: {
        type: import("vue").PropType<string | undefined>;
        default: string | undefined;
    };
    origin: {
        type: import("vue").PropType<string | undefined>;
        default: string | undefined;
    };
}, import("vue").ExtractPropTypes<{
    disabled: BooleanConstructor;
    group: BooleanConstructor;
    hideOnLeave: BooleanConstructor;
    leaveAbsolute: BooleanConstructor;
    mode: {
        type: import("vue").PropType<string | undefined>;
        default: string | undefined;
    };
    origin: {
        type: import("vue").PropType<string | undefined>;
        default: string | undefined;
    };
}>>;
export declare const VSlideYReverseTransition: {
    new (...args: any[]): import("vue").CreateComponentPublicInstanceWithMixins<{
        origin: string | undefined;
        disabled: boolean;
        group: boolean;
        mode: string | undefined;
        hideOnLeave: boolean;
        leaveAbsolute: boolean;
    } & {} & {
        $children?: import("vue").VNodeChild | {
            default?: (() => import("vue").VNodeChild) | undefined;
        } | (() => import("vue").VNodeChild);
        'v-slots'?: {
            default?: false | (() => import("vue").VNodeChild) | undefined;
        } | undefined;
    } & {
        "v-slot:default"?: false | (() => import("vue").VNodeChild) | undefined;
    }, () => import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
        [key: string]: any;
    }>, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, {
        origin: string | undefined;
        disabled: boolean;
        group: boolean;
        mode: string | undefined;
        hideOnLeave: boolean;
        leaveAbsolute: boolean;
    }, true, {}, import("vue").SlotsType<Partial<{
        default: () => import("vue").VNode[];
    }>>, import("vue").GlobalComponents, import("vue").GlobalDirectives, string, {}, any, import("vue").ComponentProvideOptions, {
        P: {};
        B: {};
        D: {};
        C: {};
        M: {};
        Defaults: {};
    }, {
        origin: string | undefined;
        disabled: boolean;
        group: boolean;
        mode: string | undefined;
        hideOnLeave: boolean;
        leaveAbsolute: boolean;
    } & {} & {
        $children?: import("vue").VNodeChild | {
            default?: (() => import("vue").VNodeChild) | undefined;
        } | (() => import("vue").VNodeChild);
        'v-slots'?: {
            default?: false | (() => import("vue").VNodeChild) | undefined;
        } | undefined;
    } & {
        "v-slot:default"?: false | (() => import("vue").VNodeChild) | undefined;
    }, () => import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
        [key: string]: any;
    }>, {}, {}, {}, {
        origin: string | undefined;
        disabled: boolean;
        group: boolean;
        mode: string | undefined;
        hideOnLeave: boolean;
        leaveAbsolute: boolean;
    }>;
    __isFragment?: never;
    __isTeleport?: never;
    __isSuspense?: never;
} & import("vue").ComponentOptionsBase<{
    origin: string | undefined;
    disabled: boolean;
    group: boolean;
    mode: string | undefined;
    hideOnLeave: boolean;
    leaveAbsolute: boolean;
} & {} & {
    $children?: import("vue").VNodeChild | {
        default?: (() => import("vue").VNodeChild) | undefined;
    } | (() => import("vue").VNodeChild);
    'v-slots'?: {
        default?: false | (() => import("vue").VNodeChild) | undefined;
    } | undefined;
} & {
    "v-slot:default"?: false | (() => import("vue").VNodeChild) | undefined;
}, () => import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
    [key: string]: any;
}>, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, {
    origin: string | undefined;
    disabled: boolean;
    group: boolean;
    mode: string | undefined;
    hideOnLeave: boolean;
    leaveAbsolute: boolean;
}, {}, string, import("vue").SlotsType<Partial<{
    default: () => import("vue").VNode[];
}>>, import("vue").GlobalComponents, import("vue").GlobalDirectives, string, import("vue").ComponentProvideOptions> & import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps & import("../../util/index.js").FilterPropsOptions<{
    disabled: BooleanConstructor;
    group: BooleanConstructor;
    hideOnLeave: BooleanConstructor;
    leaveAbsolute: BooleanConstructor;
    mode: {
        type: import("vue").PropType<string | undefined>;
        default: string | undefined;
    };
    origin: {
        type: import("vue").PropType<string | undefined>;
        default: string | undefined;
    };
}, import("vue").ExtractPropTypes<{
    disabled: BooleanConstructor;
    group: BooleanConstructor;
    hideOnLeave: BooleanConstructor;
    leaveAbsolute: BooleanConstructor;
    mode: {
        type: import("vue").PropType<string | undefined>;
        default: string | undefined;
    };
    origin: {
        type: import("vue").PropType<string | undefined>;
        default: string | undefined;
    };
}>>;
export declare const VExpandTransition: {
    new (...args: any[]): import("vue").CreateComponentPublicInstanceWithMixins<{
        disabled: boolean;
        group: boolean;
        mode: "default" | "in-out" | "out-in";
    } & {} & {
        $children?: import("vue").VNodeChild | {
            default?: (() => import("vue").VNodeChild) | undefined;
        } | (() => import("vue").VNodeChild);
        'v-slots'?: {
            default?: false | (() => import("vue").VNodeChild) | undefined;
        } | undefined;
    } & {
        "v-slot:default"?: false | (() => import("vue").VNodeChild) | undefined;
    }, () => import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
        [key: string]: any;
    }>, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, {
        disabled: boolean;
        group: boolean;
        mode: "default" | "in-out" | "out-in";
    }, true, {}, import("vue").SlotsType<Partial<{
        default: () => import("vue").VNode[];
    }>>, import("vue").GlobalComponents, import("vue").GlobalDirectives, string, {}, any, import("vue").ComponentProvideOptions, {
        P: {};
        B: {};
        D: {};
        C: {};
        M: {};
        Defaults: {};
    }, {
        disabled: boolean;
        group: boolean;
        mode: "default" | "in-out" | "out-in";
    } & {} & {
        $children?: import("vue").VNodeChild | {
            default?: (() => import("vue").VNodeChild) | undefined;
        } | (() => import("vue").VNodeChild);
        'v-slots'?: {
            default?: false | (() => import("vue").VNodeChild) | undefined;
        } | undefined;
    } & {
        "v-slot:default"?: false | (() => import("vue").VNodeChild) | undefined;
    }, () => import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
        [key: string]: any;
    }>, {}, {}, {}, {
        disabled: boolean;
        group: boolean;
        mode: "default" | "in-out" | "out-in";
    }>;
    __isFragment?: never;
    __isTeleport?: never;
    __isSuspense?: never;
} & import("vue").ComponentOptionsBase<{
    disabled: boolean;
    group: boolean;
    mode: "default" | "in-out" | "out-in";
} & {} & {
    $children?: import("vue").VNodeChild | {
        default?: (() => import("vue").VNodeChild) | undefined;
    } | (() => import("vue").VNodeChild);
    'v-slots'?: {
        default?: false | (() => import("vue").VNodeChild) | undefined;
    } | undefined;
} & {
    "v-slot:default"?: false | (() => import("vue").VNodeChild) | undefined;
}, () => import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
    [key: string]: any;
}>, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, {
    disabled: boolean;
    group: boolean;
    mode: "default" | "in-out" | "out-in";
}, {}, string, import("vue").SlotsType<Partial<{
    default: () => import("vue").VNode[];
}>>, import("vue").GlobalComponents, import("vue").GlobalDirectives, string, import("vue").ComponentProvideOptions> & import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps & import("../../util/index.js").FilterPropsOptions<{
    mode: {
        type: import("vue").PropType<"in-out" | "out-in" | "default">;
        default: string;
    };
    disabled: BooleanConstructor;
    group: BooleanConstructor;
}, import("vue").ExtractPropTypes<{
    mode: {
        type: import("vue").PropType<"in-out" | "out-in" | "default">;
        default: string;
    };
    disabled: BooleanConstructor;
    group: BooleanConstructor;
}>>;
export declare const VExpandXTransition: {
    new (...args: any[]): import("vue").CreateComponentPublicInstanceWithMixins<{
        disabled: boolean;
        group: boolean;
        mode: "default" | "in-out" | "out-in";
    } & {} & {
        $children?: import("vue").VNodeChild | {
            default?: (() => import("vue").VNodeChild) | undefined;
        } | (() => import("vue").VNodeChild);
        'v-slots'?: {
            default?: false | (() => import("vue").VNodeChild) | undefined;
        } | undefined;
    } & {
        "v-slot:default"?: false | (() => import("vue").VNodeChild) | undefined;
    }, () => import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
        [key: string]: any;
    }>, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, {
        disabled: boolean;
        group: boolean;
        mode: "default" | "in-out" | "out-in";
    }, true, {}, import("vue").SlotsType<Partial<{
        default: () => import("vue").VNode[];
    }>>, import("vue").GlobalComponents, import("vue").GlobalDirectives, string, {}, any, import("vue").ComponentProvideOptions, {
        P: {};
        B: {};
        D: {};
        C: {};
        M: {};
        Defaults: {};
    }, {
        disabled: boolean;
        group: boolean;
        mode: "default" | "in-out" | "out-in";
    } & {} & {
        $children?: import("vue").VNodeChild | {
            default?: (() => import("vue").VNodeChild) | undefined;
        } | (() => import("vue").VNodeChild);
        'v-slots'?: {
            default?: false | (() => import("vue").VNodeChild) | undefined;
        } | undefined;
    } & {
        "v-slot:default"?: false | (() => import("vue").VNodeChild) | undefined;
    }, () => import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
        [key: string]: any;
    }>, {}, {}, {}, {
        disabled: boolean;
        group: boolean;
        mode: "default" | "in-out" | "out-in";
    }>;
    __isFragment?: never;
    __isTeleport?: never;
    __isSuspense?: never;
} & import("vue").ComponentOptionsBase<{
    disabled: boolean;
    group: boolean;
    mode: "default" | "in-out" | "out-in";
} & {} & {
    $children?: import("vue").VNodeChild | {
        default?: (() => import("vue").VNodeChild) | undefined;
    } | (() => import("vue").VNodeChild);
    'v-slots'?: {
        default?: false | (() => import("vue").VNodeChild) | undefined;
    } | undefined;
} & {
    "v-slot:default"?: false | (() => import("vue").VNodeChild) | undefined;
}, () => import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
    [key: string]: any;
}>, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, {
    disabled: boolean;
    group: boolean;
    mode: "default" | "in-out" | "out-in";
}, {}, string, import("vue").SlotsType<Partial<{
    default: () => import("vue").VNode[];
}>>, import("vue").GlobalComponents, import("vue").GlobalDirectives, string, import("vue").ComponentProvideOptions> & import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps & import("../../util/index.js").FilterPropsOptions<{
    mode: {
        type: import("vue").PropType<"in-out" | "out-in" | "default">;
        default: string;
    };
    disabled: BooleanConstructor;
    group: BooleanConstructor;
}, import("vue").ExtractPropTypes<{
    mode: {
        type: import("vue").PropType<"in-out" | "out-in" | "default">;
        default: string;
    };
    disabled: BooleanConstructor;
    group: BooleanConstructor;
}>>;
export { VDialogTransition } from './dialog-transition.js';
export type VFabTransition = InstanceType<typeof VFabTransition>;
export type VDialogBottomTransition = InstanceType<typeof VDialogBottomTransition>;
export type VDialogTopTransition = InstanceType<typeof VDialogTopTransition>;
export type VFadeTransition = InstanceType<typeof VFadeTransition>;
export type VScaleTransition = InstanceType<typeof VScaleTransition>;
export type VScrollXTransition = InstanceType<typeof VScrollXTransition>;
export type VScrollXReverseTransition = InstanceType<typeof VScrollXReverseTransition>;
export type VScrollYTransition = InstanceType<typeof VScrollYTransition>;
export type VScrollYReverseTransition = InstanceType<typeof VScrollYReverseTransition>;
export type VSlideXTransition = InstanceType<typeof VSlideXTransition>;
export type VSlideXReverseTransition = InstanceType<typeof VSlideXReverseTransition>;
export type VSlideYTransition = InstanceType<typeof VSlideYTransition>;
export type VSlideYReverseTransition = InstanceType<typeof VSlideYReverseTransition>;
export type VExpandTransition = InstanceType<typeof VExpandTransition>;
export type VExpandXTransition = InstanceType<typeof VExpandXTransition>;
