export interface BorderProps {
    border?: boolean | number | string;
}
export declare const makeBorderProps: <Defaults extends {
    border?: unknown;
} = {}>(defaults?: Defaults | undefined) => {
    border: unknown extends Defaults["border"] ? (StringConstructor | BooleanConstructor | NumberConstructor)[] : {
        type: import("vue").PropType<unknown extends Defaults["border"] ? string | number | boolean : string | number | boolean | Defaults["border"]>;
        default: unknown extends Defaults["border"] ? string | number | boolean : NonNullable<string | number | boolean> | Defaults["border"];
    };
};
export declare function useBorder(props: BorderProps, name?: string): {
    borderClasses: import("vue").ComputedRef<string | string[]>;
};
