import type { PropType } from 'vue';
declare const positionValues: readonly ["static", "relative", "fixed", "absolute", "sticky"];
type Position = typeof positionValues[number];
export interface PositionProps {
    position: Position | undefined;
}
export declare const makePositionProps: <Defaults extends {
    position?: unknown;
} = {}>(defaults?: Defaults | undefined) => {
    position: unknown extends Defaults["position"] ? {
        type: PropType<Position>;
        validator: (v: any) => boolean;
    } : Omit<{
        type: PropType<Position>;
        validator: (v: any) => boolean;
    }, "type" | "default"> & {
        type: PropType<unknown extends Defaults["position"] ? "fixed" | "absolute" | "relative" | "static" | "sticky" : "fixed" | "absolute" | "relative" | "static" | "sticky" | Defaults["position"]>;
        default: unknown extends Defaults["position"] ? "fixed" | "absolute" | "relative" | "static" | "sticky" : Defaults["position"] | NonNullable<"fixed" | "absolute" | "relative" | "static" | "sticky">;
    };
};
export declare function usePosition(props: PositionProps, name?: string): {
    positionClasses: Readonly<import("vue").Ref<string | undefined, string | undefined>>;
};

