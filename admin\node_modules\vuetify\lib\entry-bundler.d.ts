import * as blueprints from './blueprints/index.js';
import * as components from './components/index.js';
import * as directives from './directives/index.js';
import type { VuetifyOptions } from './framework.js';
export declare const createVuetify: {
    (options?: VuetifyOptions): {
        install: (app: import("vue").App) => void;
        unmount: () => void;
        defaults: import("vue").Ref<import("./types.js").DefaultsInstance, import("./types.js").DefaultsInstance>;
        display: import("./types.js").DisplayInstance;
        theme: import("./types.js").ThemeInstance & {
            install: (app: import("vue").App) => void;
        };
        icons: import("./composables/icons.js").InternalIconOptions;
        locale: {
            isRtl: import("vue").Ref<boolean>;
            rtl: import("vue").Ref<Record<string, boolean>>;
            rtlClasses: import("vue").Ref<string>;
            name: string;
            messages: import("vue").Ref<import("./types.js").LocaleMessages>;
            current: import("vue").Ref<string>;
            fallback: import("vue").Ref<string>;
            t: (key: string, ...params: unknown[]) => string;
            n: (value: number) => string;
            provide: (props: import("./types.js").LocaleOptions) => import("./types.js").LocaleInstance;
        };
        date: {
            options: import("./composables/date/date.js").InternalDateOptions;
            instance: {
                locale?: any;
                date: (value?: any) => unknown;
                format: (date: unknown, formatString: string) => string;
                toJsDate: (value: unknown) => Date;
                parseISO: (date: string) => unknown;
                toISO: (date: unknown) => string;
                startOfDay: (date: unknown) => unknown;
                endOfDay: (date: unknown) => unknown;
                startOfWeek: (date: unknown, firstDayOfWeek?: number | string) => unknown;
                endOfWeek: (date: unknown) => unknown;
                startOfMonth: (date: unknown) => unknown;
                endOfMonth: (date: unknown) => unknown;
                startOfYear: (date: unknown) => unknown;
                endOfYear: (date: unknown) => unknown;
                isAfter: (date: unknown, comparing: unknown) => boolean;
                isAfterDay: (value: unknown, comparing: unknown) => boolean;
                isSameDay: (date: unknown, comparing: unknown) => boolean;
                isSameMonth: (date: unknown, comparing: unknown) => boolean;
                isSameYear: (value: unknown, comparing: unknown) => boolean;
                isBefore: (date: unknown, comparing: unknown) => boolean;
                isEqual: (date: unknown, comparing: unknown) => boolean;
                isValid: (date: any) => boolean;
                isWithinRange: (date: unknown, range: [unknown, unknown]) => boolean;
                addMinutes: (date: unknown, amount: number) => unknown;
                addHours: (date: unknown, amount: number) => unknown;
                addDays: (date: unknown, amount: number) => unknown;
                addWeeks: (date: unknown, amount: number) => unknown;
                addMonths: (date: unknown, amount: number) => unknown;
                getYear: (date: unknown) => number;
                setYear: (date: unknown, year: number) => unknown;
                getDiff: (date: unknown, comparing: unknown, unit?: string) => number;
                getWeekArray: (date: unknown, firstDayOfWeek?: number | string) => unknown[][];
                getWeekdays: (firstDayOfWeek?: number | string) => string[];
                getWeek: (date: unknown, firstDayOfWeek?: number | string, firstWeekMinSize?: number) => number;
                getMonth: (date: unknown) => number;
                setMonth: (date: unknown, month: number) => unknown;
                getDate: (date: unknown) => number;
                setDate: (date: unknown, day: number) => unknown;
                getNextMonth: (date: unknown) => unknown;
                getPreviousMonth: (date: unknown) => unknown;
                getHours: (date: unknown) => number;
                setHours: (date: unknown, hours: number) => unknown;
                getMinutes: (date: unknown) => number;
                setMinutes: (date: unknown, minutes: number) => unknown;
            };
        };
        goTo: import("./types.js").GoToInstance;
    };
    version: string;
};
export declare const version: string;
export { blueprints, components, directives, };
export * from './composables/index.js';
