import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import axios from 'axios'

const API_BASE_URL = 'http://localhost:3000/api'

// 配置axios默认设置
axios.defaults.baseURL = API_BASE_URL

export const useAuthStore = defineStore('auth', () => {
  const user = ref(null)
  const token = ref(localStorage.getItem('admin_token'))
  const loading = ref(false)

  // 计算属性
  const isLoggedIn = computed(() => !!token.value && !!user.value)
  const isAdmin = computed(() => user.value?.role === 'admin')
  const isModerator = computed(() => user.value?.role === 'moderator' || user.value?.role === 'admin')

  // 设置认证头
  const setAuthHeader = (authToken) => {
    axios.defaults.headers.common['Authorization'] = `Bearer ${authToken}`
  }

  // 清除认证头
  const clearAuthHeader = () => {
    delete axios.defaults.headers.common['Authorization']
  }

  // 登录
  const login = async (username, password) => {
    loading.value = true
    try {
      const response = await axios.post('/auth/login', { username, password })
      const { token: authToken, user: userData } = response.data
      
      // 检查用户权限
      if (!['admin', 'moderator'].includes(userData.role)) {
        return { 
          success: false, 
          message: '权限不足，只有管理员和审核员可以访问管理后台' 
        }
      }
      
      token.value = authToken
      user.value = userData
      
      localStorage.setItem('admin_token', authToken)
      setAuthHeader(authToken)
      
      return { success: true }
    } catch (error) {
      return { 
        success: false, 
        message: error.response?.data?.message || '登录失败' 
      }
    } finally {
      loading.value = false
    }
  }

  // 获取当前用户信息
  const fetchCurrentUser = async () => {
    if (!token.value) return
    
    try {
      setAuthHeader(token.value)
      const response = await axios.get('/auth/me')
      const userData = response.data.user
      
      // 检查用户权限
      if (!['admin', 'moderator'].includes(userData.role)) {
        logout()
        return
      }
      
      user.value = userData
    } catch (error) {
      // Token可能已过期，清除本地存储
      logout()
    }
  }

  // 登出
  const logout = () => {
    user.value = null
    token.value = null
    localStorage.removeItem('admin_token')
    clearAuthHeader()
  }

  // 初始化时检查token
  if (token.value) {
    setAuthHeader(token.value)
    fetchCurrentUser()
  }

  return {
    user,
    token,
    loading,
    isLoggedIn,
    isAdmin,
    isModerator,
    login,
    fetchCurrentUser,
    logout
  }
})
