{"version": 3, "sources": ["../../vuetify/src/framework.ts"], "sourcesContent": ["// Composables\nimport { createDate, DateAdapterSymbol, DateOptionsSymbol } from '@/composables/date/date'\nimport { createDefaults, DefaultsSymbol } from '@/composables/defaults'\nimport { createDisplay, DisplaySymbol } from '@/composables/display'\nimport { createGoTo, GoToSymbol } from '@/composables/goto'\nimport { createIcons, IconSymbol } from '@/composables/icons'\nimport { createLocale, LocaleSymbol } from '@/composables/locale'\nimport { createTheme, ThemeSymbol } from '@/composables/theme'\n\n// Utilities\nimport { effectScope, nextTick, reactive } from 'vue'\nimport { defineComponent, IN_BROWSER, mergeDeep } from '@/util'\n\n// Types\nimport type { App, ComponentPublicInstance, InjectionKey } from 'vue'\nimport type { DateOptions } from '@/composables/date'\nimport type { DefaultsOptions } from '@/composables/defaults'\nimport type { DisplayOptions, SSROptions } from '@/composables/display'\nimport type { GoToOptions } from '@/composables/goto'\nimport type { IconOptions } from '@/composables/icons'\nimport type { LocaleOptions, RtlOptions } from '@/composables/locale'\nimport type { ThemeOptions } from '@/composables/theme'\n\n// Exports\nexport * from './composables'\nexport * from './types'\n\nexport interface VuetifyOptions {\n  aliases?: Record<string, any>\n  blueprint?: Blueprint\n  components?: Record<string, any>\n  date?: DateOptions\n  directives?: Record<string, any>\n  defaults?: DefaultsOptions\n  display?: DisplayOptions\n  goTo?: GoToOptions\n  theme?: ThemeOptions\n  icons?: IconOptions\n  locale?: LocaleOptions & RtlOptions\n  ssr?: SSROptions\n}\n\nexport interface Blueprint extends Omit<VuetifyOptions, 'blueprint'> {}\n\nexport function createVuetify (vuetify: VuetifyOptions = {}) {\n  const { blueprint, ...rest } = vuetify\n  const options: VuetifyOptions = mergeDeep(blueprint, rest)\n  const {\n    aliases = {},\n    components = {},\n    directives = {},\n  } = options\n\n  const scope = effectScope()\n  return scope.run(() => {\n    const defaults = createDefaults(options.defaults)\n    const display = createDisplay(options.display, options.ssr)\n    const theme = createTheme(options.theme)\n    const icons = createIcons(options.icons)\n    const locale = createLocale(options.locale)\n    const date = createDate(options.date, locale)\n    const goTo = createGoTo(options.goTo, locale)\n\n    function install (app: App) {\n      for (const key in directives) {\n        app.directive(key, directives[key])\n      }\n\n      for (const key in components) {\n        app.component(key, components[key])\n      }\n\n      for (const key in aliases) {\n        app.component(key, defineComponent({\n          ...aliases[key],\n          name: key,\n          aliasName: aliases[key].name,\n        }))\n      }\n\n      const appScope = effectScope()\n      appScope.run(() => {\n        theme.install(app)\n      })\n      app.onUnmount(() => appScope.stop())\n\n      app.provide(DefaultsSymbol, defaults)\n      app.provide(DisplaySymbol, display)\n      app.provide(ThemeSymbol, theme)\n      app.provide(IconSymbol, icons)\n      app.provide(LocaleSymbol, locale)\n      app.provide(DateOptionsSymbol, date.options)\n      app.provide(DateAdapterSymbol, date.instance)\n      app.provide(GoToSymbol, goTo)\n\n      if (IN_BROWSER && options.ssr) {\n        if (app.$nuxt) {\n          app.$nuxt.hook('app:suspense:resolve', () => {\n            display.update()\n          })\n        } else {\n          const { mount } = app\n          app.mount = (...args) => {\n            const vm = mount(...args)\n            nextTick(() => display.update())\n            app.mount = mount\n            return vm\n          }\n        }\n      }\n\n      if (typeof __VUE_OPTIONS_API__ !== 'boolean' || __VUE_OPTIONS_API__) {\n        app.mixin({\n          computed: {\n            $vuetify () {\n              return reactive({\n                defaults: inject.call(this, DefaultsSymbol),\n                display: inject.call(this, DisplaySymbol),\n                theme: inject.call(this, ThemeSymbol),\n                icons: inject.call(this, IconSymbol),\n                locale: inject.call(this, LocaleSymbol),\n                date: inject.call(this, DateAdapterSymbol),\n              })\n            },\n          },\n        })\n      }\n    }\n\n    function unmount () {\n      scope.stop()\n    }\n\n    return {\n      install,\n      unmount,\n      defaults,\n      display,\n      theme,\n      icons,\n      locale,\n      date,\n      goTo,\n    }\n  })!\n}\n\nexport const version = __VUETIFY_VERSION__\ncreateVuetify.version = version\n\n// Vue's inject() can only be used in setup\nfunction inject (this: ComponentPublicInstance, key: InjectionKey<any> | string) {\n  const vm = this.$\n\n  const provides = vm.parent?.provides ?? vm.vnode.appContext?.provides\n\n  if (provides && (key as any) in provides) {\n    return provides[(key as string)]\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4CO,SAASA,gBAA6C;AAAA,MAA9BC,UAAuBC,UAAAC,SAAA,KAAAD,UAAA,CAAA,MAAAE,SAAAF,UAAA,CAAA,IAAG,CAAC;AACxD,QAAM;IAAEG;IAAW,GAAGC;EAAK,IAAIL;AAC/B,QAAMM,UAA0BC,UAAUH,WAAWC,IAAI;AACzD,QAAM;IACJG,UAAU,CAAC;IACXC,aAAa,CAAC;IACdC,aAAa,CAAC;EAChB,IAAIJ;AAEJ,QAAMK,QAAQC,YAAY;AAC1B,SAAOD,MAAME,IAAI,MAAM;AACrB,UAAMC,WAAWC,eAAeT,QAAQQ,QAAQ;AAChD,UAAME,UAAUC,cAAcX,QAAQU,SAASV,QAAQY,GAAG;AAC1D,UAAMC,QAAQC,YAAYd,QAAQa,KAAK;AACvC,UAAME,QAAQC,YAAYhB,QAAQe,KAAK;AACvC,UAAME,SAASC,aAAalB,QAAQiB,MAAM;AAC1C,UAAME,OAAOC,WAAWpB,QAAQmB,MAAMF,MAAM;AAC5C,UAAMI,OAAOC,WAAWtB,QAAQqB,MAAMJ,MAAM;AAE5C,aAASM,QAASC,KAAU;AAC1B,iBAAWC,OAAOrB,YAAY;AAC5BoB,YAAIE,UAAUD,KAAKrB,WAAWqB,GAAG,CAAC;MACpC;AAEA,iBAAWA,OAAOtB,YAAY;AAC5BqB,YAAIG,UAAUF,KAAKtB,WAAWsB,GAAG,CAAC;MACpC;AAEA,iBAAWA,OAAOvB,SAAS;AACzBsB,YAAIG,UAAUF,KAAKG,gBAAgB;UACjC,GAAG1B,QAAQuB,GAAG;UACdI,MAAMJ;UACNK,WAAW5B,QAAQuB,GAAG,EAAEI;QAC1B,CAAC,CAAC;MACJ;AAEA,YAAME,WAAWzB,YAAY;AAC7ByB,eAASxB,IAAI,MAAM;AACjBM,cAAMU,QAAQC,GAAG;MACnB,CAAC;AACDA,UAAIQ,UAAU,MAAMD,SAASE,KAAK,CAAC;AAEnCT,UAAIU,QAAQC,gBAAgB3B,QAAQ;AACpCgB,UAAIU,QAAQE,eAAe1B,OAAO;AAClCc,UAAIU,QAAQG,aAAaxB,KAAK;AAC9BW,UAAIU,QAAQI,YAAYvB,KAAK;AAC7BS,UAAIU,QAAQK,cAActB,MAAM;AAChCO,UAAIU,QAAQM,mBAAmBrB,KAAKnB,OAAO;AAC3CwB,UAAIU,QAAQO,mBAAmBtB,KAAKuB,QAAQ;AAC5ClB,UAAIU,QAAQS,YAAYtB,IAAI;AAE5B,UAAIuB,cAAc5C,QAAQY,KAAK;AAC7B,YAAIY,IAAIqB,OAAO;AACbrB,cAAIqB,MAAMC,KAAK,wBAAwB,MAAM;AAC3CpC,oBAAQqC,OAAO;UACjB,CAAC;QACH,OAAO;AACL,gBAAM;YAAEC;UAAM,IAAIxB;AAClBA,cAAIwB,QAAQ,WAAa;AACvB,kBAAMC,KAAKD,MAAM,GAAArD,SAAO;AACxBuD,qBAAS,MAAMxC,QAAQqC,OAAO,CAAC;AAC/BvB,gBAAIwB,QAAQA;AACZ,mBAAOC;UACT;QACF;MACF;AAEA,UAAI,OAAOE,wBAAwB,aAAaA,qBAAqB;AACnE3B,YAAI4B,MAAM;UACRC,UAAU;YACRC,WAAY;AACV,qBAAOC,SAAS;gBACd/C,UAAUgD,OAAOC,KAAK,MAAMtB,cAAc;gBAC1CzB,SAAS8C,OAAOC,KAAK,MAAMrB,aAAa;gBACxCvB,OAAO2C,OAAOC,KAAK,MAAMpB,WAAW;gBACpCtB,OAAOyC,OAAOC,KAAK,MAAMnB,UAAU;gBACnCrB,QAAQuC,OAAOC,KAAK,MAAMlB,YAAY;gBACtCpB,MAAMqC,OAAOC,KAAK,MAAMhB,iBAAiB;cAC3C,CAAC;YACH;UACF;QACF,CAAC;MACH;IACF;AAEA,aAASiB,UAAW;AAClBrD,YAAM4B,KAAK;IACb;AAEA,WAAO;MACLV;MACAmC;MACAlD;MACAE;MACAG;MACAE;MACAE;MACAE;MACAE;IACF;EACF,CAAC;AACH;AAEO,IAAMsC,UAAO;AACpBlE,cAAckE,UAAUA;AAGxB,SAASH,OAAuC/B,KAAiC;AAvJjF;AAwJE,QAAMwB,KAAK,KAAKW;AAEhB,QAAMC,aAAWZ,QAAGa,WAAHb,mBAAWY,eAAYZ,QAAGc,MAAMC,eAATf,mBAAqBY;AAE7D,MAAIA,YAAapC,OAAeoC,UAAU;AACxC,WAAOA,SAAUpC,GAAG;EACtB;AACF;", "names": ["createVuetify", "vuetify", "arguments", "length", "undefined", "blueprint", "rest", "options", "mergeDeep", "aliases", "components", "directives", "scope", "effectScope", "run", "defaults", "createDefaults", "display", "createDisplay", "ssr", "theme", "createTheme", "icons", "createIcons", "locale", "createLocale", "date", "createDate", "goTo", "createGoTo", "install", "app", "key", "directive", "component", "defineComponent", "name", "<PERSON><PERSON><PERSON>", "appScope", "onUnmount", "stop", "provide", "DefaultsSymbol", "DisplaySymbol", "ThemeSymbol", "IconSymbol", "LocaleSymbol", "DateOptionsSymbol", "DateAdapterSymbol", "instance", "GoToSymbol", "IN_BROWSER", "$nuxt", "hook", "update", "mount", "vm", "nextTick", "__VUE_OPTIONS_API__", "mixin", "computed", "$vuetify", "reactive", "inject", "call", "unmount", "version", "$", "provides", "parent", "vnode", "appContext"]}