const express = require('express');
const router = express.Router();
const {
  getArticleList,
  getArticleDetail,
  createArticle,
  updateArticle,
  deleteArticle,
  reviewArticle,
  getPendingArticles,
  getCategories,
  likeArticle,
  articleValidation
} = require('../controllers/articleController');
const { authenticateToken, requireModerator, optionalAuth } = require('../middleware/auth');

// 公开路由
router.get('/', getArticleList);
router.get('/categories', getCategories);
router.get('/:id', optionalAuth, getArticleDetail);

// 需要登录的路由
router.post('/', authenticateToken, articleValidation, createArticle);
router.put('/:id', authenticateToken, updateArticle);
router.delete('/:id', authenticateToken, deleteArticle);
router.post('/:id/like', authenticateToken, likeArticle);

// 需要审核员权限的路由
router.get('/admin/pending', authenticateToken, requireModerator, getPendingArticles);
router.post('/:id/review', authenticateToken, requireModerator, reviewArticle);

module.exports = router;
