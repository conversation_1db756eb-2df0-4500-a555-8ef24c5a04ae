import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import axios from 'axios'

const API_BASE_URL = 'http://localhost:3000/api'

// 配置axios默认设置
axios.defaults.baseURL = API_BASE_URL

export interface User {
  id: number
  username: string
  email: string
  nickname: string
  avatar?: string
  role: 'admin' | 'moderator' | 'user'
  points: number
  created_at: string
}

export const useAuthStore = defineStore('auth', () => {
  const user = ref<User | null>(null)
  const token = ref<string | null>(localStorage.getItem('token'))
  const loading = ref(false)

  // 计算属性
  const isLoggedIn = computed(() => !!token.value && !!user.value)
  const isAdmin = computed(() => user.value?.role === 'admin')
  const isModerator = computed(() => user.value?.role === 'moderator' || user.value?.role === 'admin')

  // 设置认证头
  const setAuthHeader = (authToken: string) => {
    axios.defaults.headers.common['Authorization'] = `Bearer ${authToken}`
  }

  // 清除认证头
  const clearAuthHeader = () => {
    delete axios.defaults.headers.common['Authorization']
  }

  // 登录
  const login = async (username: string, password: string) => {
    loading.value = true
    try {
      const response = await axios.post('/auth/login', { username, password })
      const { token: authToken, user: userData } = response.data
      
      token.value = authToken
      user.value = userData
      
      localStorage.setItem('token', authToken)
      setAuthHeader(authToken)
      
      return { success: true }
    } catch (error: any) {
      return { 
        success: false, 
        message: error.response?.data?.message || '登录失败' 
      }
    } finally {
      loading.value = false
    }
  }

  // 注册
  const register = async (userData: {
    username: string
    email: string
    password: string
    nickname: string
  }) => {
    loading.value = true
    try {
      const response = await axios.post('/auth/register', userData)
      const { token: authToken, user: newUser } = response.data
      
      token.value = authToken
      user.value = newUser
      
      localStorage.setItem('token', authToken)
      setAuthHeader(authToken)
      
      return { success: true }
    } catch (error: any) {
      return { 
        success: false, 
        message: error.response?.data?.message || '注册失败' 
      }
    } finally {
      loading.value = false
    }
  }

  // 获取当前用户信息
  const fetchCurrentUser = async () => {
    if (!token.value) return
    
    try {
      setAuthHeader(token.value)
      const response = await axios.get('/auth/me')
      user.value = response.data.user
    } catch (error) {
      // Token可能已过期，清除本地存储
      logout()
    }
  }

  // 更新用户信息
  const updateProfile = async (profileData: { nickname?: string; avatar?: string }) => {
    loading.value = true
    try {
      const response = await axios.put('/auth/profile', profileData)
      user.value = response.data.user
      return { success: true }
    } catch (error: any) {
      return { 
        success: false, 
        message: error.response?.data?.message || '更新失败' 
      }
    } finally {
      loading.value = false
    }
  }

  // 登出
  const logout = () => {
    user.value = null
    token.value = null
    localStorage.removeItem('token')
    clearAuthHeader()
  }

  // 初始化时检查token
  if (token.value) {
    setAuthHeader(token.value)
    fetchCurrentUser()
  }

  return {
    user,
    token,
    loading,
    isLoggedIn,
    isAdmin,
    isModerator,
    login,
    register,
    fetchCurrentUser,
    updateProfile,
    logout
  }
})
