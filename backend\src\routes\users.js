const express = require('express');
const router = express.Router();
const User = require('../models/User');
const { authenticateToken, requireAdmin } = require('../middleware/auth');

// 获取用户列表（管理员）
router.get('/', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const filters = {
      role: req.query.role,
      status: req.query.status,
      search: req.query.search
    };

    const result = await User.getList(page, limit, filters);
    res.json(result);

  } catch (error) {
    console.error('获取用户列表失败:', error);
    res.status(500).json({ message: '服务器内部错误' });
  }
});

// 获取用户详情（管理员）
router.get('/:id', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const user = await User.findById(id);

    if (!user) {
      return res.status(404).json({ message: '用户不存在' });
    }

    res.json({ user });

  } catch (error) {
    console.error('获取用户详情失败:', error);
    res.status(500).json({ message: '服务器内部错误' });
  }
});

// 更新用户信息（管理员）
router.put('/:id', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const { role, status, points } = req.body;

    const user = await User.findById(id);
    if (!user) {
      return res.status(404).json({ message: '用户不存在' });
    }

    const updateData = {};
    if (role !== undefined) updateData.role = role;
    if (status !== undefined) updateData.status = status;
    if (points !== undefined) updateData.points = points;

    const success = await User.update(id, updateData);
    if (!success) {
      return res.status(400).json({ message: '更新失败' });
    }

    const updatedUser = await User.findById(id);
    res.json({
      message: '用户信息更新成功',
      user: updatedUser
    });

  } catch (error) {
    console.error('更新用户信息失败:', error);
    res.status(500).json({ message: '服务器内部错误' });
  }
});

// 删除用户（管理员）
router.delete('/:id', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;

    // 不能删除自己
    if (parseInt(id) === req.user.id) {
      return res.status(400).json({ message: '不能删除自己的账户' });
    }

    const user = await User.findById(id);
    if (!user) {
      return res.status(404).json({ message: '用户不存在' });
    }

    const success = await User.delete(id);
    if (!success) {
      return res.status(400).json({ message: '删除失败' });
    }

    res.json({ message: '用户删除成功' });

  } catch (error) {
    console.error('删除用户失败:', error);
    res.status(500).json({ message: '服务器内部错误' });
  }
});

module.exports = router;
