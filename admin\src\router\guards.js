import { useAuthStore } from '../stores/auth'

export function setupRouterGuards(router) {
  // 全局前置守卫
  router.beforeEach((to, from, next) => {
    const authStore = useAuthStore()
    
    // 登录页面直接放行
    if (to.path === '/login') {
      // 如果已经登录，跳转到仪表盘
      if (authStore.isLoggedIn) {
        next('/dashboard')
        return
      }
      next()
      return
    }
    
    // 检查是否需要认证
    if (to.meta.requiresAuth && !authStore.isLoggedIn) {
      next('/login')
      return
    }
    
    // 检查管理员权限
    if (to.meta.requiresAdmin && !authStore.isAdmin) {
      next('/dashboard')
      return
    }
    
    // 检查审核员权限
    if (to.meta.requiresModerator && !authStore.isModerator) {
      next('/dashboard')
      return
    }
    
    next()
  })
  
  // 全局后置钩子
  router.afterEach((to) => {
    // 设置页面标题
    document.title = to.meta.title ? `${to.meta.title} - 爱生网管理后台` : '爱生网管理后台'
  })
}
