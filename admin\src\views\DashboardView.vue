<template>
  <div>
    <!-- 页面标题 -->
    <v-row class="mb-6">
      <v-col cols="12">
        <h1 class="text-h3 mb-2">仪表盘</h1>
        <p class="text-body-1 text-grey-darken-1">
          欢迎回来，{{ authStore.user?.nickname }}！
        </p>
      </v-col>
    </v-row>

    <!-- 统计卡片 -->
    <v-row class="mb-6">
      <v-col cols="12" sm="6" md="3">
        <v-card color="primary" dark>
          <v-card-text>
            <div class="d-flex align-center">
              <div>
                <div class="text-h4 font-weight-bold">{{ stats.totalUsers || 0 }}</div>
                <div class="text-body-2">总用户数</div>
              </div>
              <v-spacer></v-spacer>
              <v-icon size="48">mdi-account-group</v-icon>
            </div>
          </v-card-text>
        </v-card>
      </v-col>

      <v-col cols="12" sm="6" md="3">
        <v-card color="success" dark>
          <v-card-text>
            <div class="d-flex align-center">
              <div>
                <div class="text-h4 font-weight-bold">{{ stats.totalArticles || 0 }}</div>
                <div class="text-body-2">总文章数</div>
              </div>
              <v-spacer></v-spacer>
              <v-icon size="48">mdi-book-open-page-variant</v-icon>
            </div>
          </v-card-text>
        </v-card>
      </v-col>

      <v-col cols="12" sm="6" md="3">
        <v-card color="warning" dark>
          <v-card-text>
            <div class="d-flex align-center">
              <div>
                <div class="text-h4 font-weight-bold">{{ stats.pendingArticles || 0 }}</div>
                <div class="text-body-2">待审核文章</div>
              </div>
              <v-spacer></v-spacer>
              <v-icon size="48">mdi-file-check</v-icon>
            </div>
          </v-card-text>
        </v-card>
      </v-col>

      <v-col cols="12" sm="6" md="3">
        <v-card color="info" dark>
          <v-card-text>
            <div class="d-flex align-center">
              <div>
                <div class="text-h4 font-weight-bold">{{ stats.totalNews || 0 }}</div>
                <div class="text-body-2">总新闻数</div>
              </div>
              <v-spacer></v-spacer>
              <v-icon size="48">mdi-newspaper</v-icon>
            </div>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <!-- 快速操作 -->
    <v-row class="mb-6">
      <v-col cols="12">
        <h2 class="text-h5 mb-4">快速操作</h2>
      </v-col>
    </v-row>

    <v-row class="mb-6">
      <v-col cols="12" md="6">
        <v-card>
          <v-card-title>
            <v-icon class="mr-2">mdi-file-check</v-icon>
            待审核文章
          </v-card-title>
          <v-card-text>
            <p class="text-body-2 mb-4">
              当前有 {{ stats.pendingArticles || 0 }} 篇文章等待审核
            </p>
            <v-btn
              to="/articles/pending"
              color="primary"
              prepend-icon="mdi-arrow-right"
            >
              去审核
            </v-btn>
          </v-card-text>
        </v-card>
      </v-col>

      <v-col cols="12" md="6">
        <v-card>
          <v-card-title>
            <v-icon class="mr-2">mdi-newspaper</v-icon>
            发布新闻
          </v-card-title>
          <v-card-text>
            <p class="text-body-2 mb-4">
              快速发布新闻资讯
            </p>
            <v-btn
              to="/news"
              color="success"
              prepend-icon="mdi-plus"
            >
              发布新闻
            </v-btn>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <!-- 最近活动 -->
    <v-row>
      <v-col cols="12">
        <v-card>
          <v-card-title>
            <v-icon class="mr-2">mdi-clock-outline</v-icon>
            最近活动
          </v-card-title>
          <v-card-text>
            <v-list>
              <v-list-item>
                <template v-slot:prepend>
                  <v-icon color="success">mdi-check-circle</v-icon>
                </template>
                <v-list-item-title>系统正常运行</v-list-item-title>
                <v-list-item-subtitle>{{ currentTime }}</v-list-item-subtitle>
              </v-list-item>
              
              <v-list-item>
                <template v-slot:prepend>
                  <v-icon color="info">mdi-information</v-icon>
                </template>
                <v-list-item-title>管理后台已启动</v-list-item-title>
                <v-list-item-subtitle>欢迎使用爱生网管理系统</v-list-item-subtitle>
              </v-list-item>
            </v-list>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useAuthStore } from '../stores/auth'

const authStore = useAuthStore()

const stats = ref({
  totalUsers: 0,
  totalArticles: 0,
  pendingArticles: 0,
  totalNews: 0
})

const currentTime = computed(() => {
  return new Date().toLocaleString('zh-CN')
})

// 模拟获取统计数据
const fetchStats = async () => {
  // 这里应该调用真实的API
  stats.value = {
    totalUsers: 156,
    totalArticles: 89,
    pendingArticles: 12,
    totalNews: 45
  }
}

onMounted(() => {
  fetchStats()
})
</script>

<style scoped>
.v-card {
  transition: transform 0.2s;
}

.v-card:hover {
  transform: translateY(-2px);
}
</style>
