import type { PropType } from 'vue';
import type { IconValue } from "../../composables/icons.js";
import type { Variant } from "../../composables/variant.js";
export type VIconBtnSlots = {
    default: never;
    loader: never;
};
export type VIconBtnSizes = 'x-small' | 'small' | 'default' | 'large' | 'x-large';
export declare const makeVIconBtnProps: <Defaults extends {
    color?: unknown;
    variant?: unknown;
    theme?: unknown;
    tag?: unknown;
    rounded?: unknown;
    tile?: unknown;
    elevation?: unknown;
    class?: unknown;
    style?: unknown;
    border?: unknown;
    active?: unknown;
    activeColor?: unknown;
    activeIcon?: unknown;
    activeVariant?: unknown;
    baseVariant?: unknown;
    disabled?: unknown;
    height?: unknown;
    width?: unknown;
    hideOverlay?: unknown;
    icon?: unknown;
    iconColor?: unknown;
    iconSize?: unknown;
    iconSizes?: unknown;
    loading?: unknown;
    opacity?: unknown;
    readonly?: unknown;
    rotate?: unknown;
    size?: unknown;
    sizes?: unknown;
    text?: unknown;
} = {}>(defaults?: Defaults | undefined) => {
    color: unknown extends Defaults["color"] ? StringConstructor : {
        type: PropType<unknown extends Defaults["color"] ? string : string | Defaults["color"]>;
        default: unknown extends Defaults["color"] ? string : string | Defaults["color"];
    };
    variant: unknown extends Defaults["variant"] ? Omit<{
        type: PropType<Variant>;
        default: string;
        validator: (v: any) => boolean;
    }, "type" | "default"> & {
        type: PropType<"flat" | "text" | "elevated" | "tonal" | "outlined" | "plain">;
        default: NonNullable<"flat" | "text" | "elevated" | "tonal" | "outlined" | "plain">;
    } : Omit<Omit<{
        type: PropType<Variant>;
        default: string;
        validator: (v: any) => boolean;
    }, "type" | "default"> & {
        type: PropType<"flat" | "text" | "elevated" | "tonal" | "outlined" | "plain">;
        default: NonNullable<"flat" | "text" | "elevated" | "tonal" | "outlined" | "plain">;
    }, "type" | "default"> & {
        type: PropType<unknown extends Defaults["variant"] ? "flat" | "text" | "elevated" | "tonal" | "outlined" | "plain" : "flat" | "text" | "elevated" | "tonal" | "outlined" | "plain" | Defaults["variant"]>;
        default: unknown extends Defaults["variant"] ? "flat" | "text" | "elevated" | "tonal" | "outlined" | "plain" : NonNullable<"flat" | "text" | "elevated" | "tonal" | "outlined" | "plain"> | Defaults["variant"];
    };
    theme: unknown extends Defaults["theme"] ? StringConstructor : {
        type: PropType<unknown extends Defaults["theme"] ? string : string | Defaults["theme"]>;
        default: unknown extends Defaults["theme"] ? string : string | Defaults["theme"];
    };
    tag: unknown extends Defaults["tag"] ? Omit<{
        type: PropType<string | import("../../util/index.js").JSXComponent>;
        default: string;
    }, "type" | "default"> & {
        type: PropType<string | import("../../util/index.js").JSXComponent>;
        default: NonNullable<string | import("../../util/index.js").JSXComponent>;
    } : Omit<Omit<{
        type: PropType<string | import("../../util/index.js").JSXComponent>;
        default: string;
    }, "type" | "default"> & {
        type: PropType<string | import("../../util/index.js").JSXComponent>;
        default: NonNullable<string | import("../../util/index.js").JSXComponent>;
    }, "type" | "default"> & {
        type: PropType<unknown extends Defaults["tag"] ? string | import("../../util/index.js").JSXComponent : string | import("../../util/index.js").JSXComponent | Defaults["tag"]>;
        default: unknown extends Defaults["tag"] ? string | import("../../util/index.js").JSXComponent : NonNullable<string | import("../../util/index.js").JSXComponent> | Defaults["tag"];
    };
    rounded: unknown extends Defaults["rounded"] ? {
        type: (StringConstructor | BooleanConstructor | NumberConstructor)[];
        default: undefined;
    } : Omit<{
        type: (StringConstructor | BooleanConstructor | NumberConstructor)[];
        default: undefined;
    }, "type" | "default"> & {
        type: PropType<unknown extends Defaults["rounded"] ? string | number | boolean : string | number | boolean | Defaults["rounded"]>;
        default: unknown extends Defaults["rounded"] ? string | number | boolean : NonNullable<string | number | boolean> | Defaults["rounded"];
    };
    tile: unknown extends Defaults["tile"] ? BooleanConstructor : {
        type: PropType<unknown extends Defaults["tile"] ? boolean : boolean | Defaults["tile"]>;
        default: unknown extends Defaults["tile"] ? boolean : boolean | Defaults["tile"];
    };
    elevation: unknown extends Defaults["elevation"] ? {
        type: (StringConstructor | NumberConstructor)[];
        validator(v: any): boolean;
    } : Omit<{
        type: (StringConstructor | NumberConstructor)[];
        validator(v: any): boolean;
    }, "type" | "default"> & {
        type: PropType<unknown extends Defaults["elevation"] ? string | number : string | number | Defaults["elevation"]>;
        default: unknown extends Defaults["elevation"] ? string | number : NonNullable<string | number> | Defaults["elevation"];
    };
    class: unknown extends Defaults["class"] ? PropType<any> : {
        type: PropType<unknown extends Defaults["class"] ? any : any>;
        default: unknown extends Defaults["class"] ? any : any;
    };
    style: unknown extends Defaults["style"] ? {
        type: PropType<import("vue").StyleValue>;
        default: null;
    } : Omit<{
        type: PropType<import("vue").StyleValue>;
        default: null;
    }, "type" | "default"> & {
        type: PropType<unknown extends Defaults["style"] ? import("vue").StyleValue : import("vue").StyleValue | Defaults["style"]>;
        default: unknown extends Defaults["style"] ? import("vue").StyleValue : NonNullable<import("vue").StyleValue> | Defaults["style"];
    };
    border: unknown extends Defaults["border"] ? (StringConstructor | BooleanConstructor | NumberConstructor)[] : {
        type: PropType<unknown extends Defaults["border"] ? string | number | boolean : string | number | boolean | Defaults["border"]>;
        default: unknown extends Defaults["border"] ? string | number | boolean : NonNullable<string | number | boolean> | Defaults["border"];
    };
    active: unknown extends Defaults["active"] ? {
        type: BooleanConstructor;
        default: undefined;
    } : Omit<{
        type: BooleanConstructor;
        default: undefined;
    }, "type" | "default"> & {
        type: PropType<unknown extends Defaults["active"] ? boolean : boolean | Defaults["active"]>;
        default: unknown extends Defaults["active"] ? boolean : boolean | Defaults["active"];
    };
    activeColor: unknown extends Defaults["activeColor"] ? StringConstructor : {
        type: PropType<unknown extends Defaults["activeColor"] ? string : string | Defaults["activeColor"]>;
        default: unknown extends Defaults["activeColor"] ? string : string | Defaults["activeColor"];
    };
    activeIcon: unknown extends Defaults["activeIcon"] ? PropType<IconValue> : {
        type: PropType<unknown extends Defaults["activeIcon"] ? IconValue : IconValue | Defaults["activeIcon"]>;
        default: unknown extends Defaults["activeIcon"] ? IconValue : NonNullable<IconValue> | Defaults["activeIcon"];
    };
    activeVariant: unknown extends Defaults["activeVariant"] ? PropType<"flat" | "text" | "elevated" | "tonal" | "outlined" | "plain"> : {
        type: PropType<unknown extends Defaults["activeVariant"] ? "flat" | "text" | "elevated" | "tonal" | "outlined" | "plain" : "flat" | "text" | "elevated" | "tonal" | "outlined" | "plain" | Defaults["activeVariant"]>;
        default: unknown extends Defaults["activeVariant"] ? "flat" | "text" | "elevated" | "tonal" | "outlined" | "plain" : NonNullable<"flat" | "text" | "elevated" | "tonal" | "outlined" | "plain"> | Defaults["activeVariant"];
    };
    baseVariant: unknown extends Defaults["baseVariant"] ? {
        type: PropType<Variant>;
        default: string;
    } : Omit<{
        type: PropType<Variant>;
        default: string;
    }, "type" | "default"> & {
        type: PropType<unknown extends Defaults["baseVariant"] ? "flat" | "text" | "elevated" | "tonal" | "outlined" | "plain" : "flat" | "text" | "elevated" | "tonal" | "outlined" | "plain" | Defaults["baseVariant"]>;
        default: unknown extends Defaults["baseVariant"] ? "flat" | "text" | "elevated" | "tonal" | "outlined" | "plain" : NonNullable<"flat" | "text" | "elevated" | "tonal" | "outlined" | "plain"> | Defaults["baseVariant"];
    };
    disabled: unknown extends Defaults["disabled"] ? BooleanConstructor : {
        type: PropType<unknown extends Defaults["disabled"] ? boolean : boolean | Defaults["disabled"]>;
        default: unknown extends Defaults["disabled"] ? boolean : boolean | Defaults["disabled"];
    };
    height: unknown extends Defaults["height"] ? (StringConstructor | NumberConstructor)[] : {
        type: PropType<unknown extends Defaults["height"] ? string | number : string | number | Defaults["height"]>;
        default: unknown extends Defaults["height"] ? string | number : NonNullable<string | number> | Defaults["height"];
    };
    width: unknown extends Defaults["width"] ? (StringConstructor | NumberConstructor)[] : {
        type: PropType<unknown extends Defaults["width"] ? string | number : string | number | Defaults["width"]>;
        default: unknown extends Defaults["width"] ? string | number : NonNullable<string | number> | Defaults["width"];
    };
    hideOverlay: unknown extends Defaults["hideOverlay"] ? BooleanConstructor : {
        type: PropType<unknown extends Defaults["hideOverlay"] ? boolean : boolean | Defaults["hideOverlay"]>;
        default: unknown extends Defaults["hideOverlay"] ? boolean : boolean | Defaults["hideOverlay"];
    };
    icon: unknown extends Defaults["icon"] ? PropType<IconValue> : {
        type: PropType<unknown extends Defaults["icon"] ? IconValue : IconValue | Defaults["icon"]>;
        default: unknown extends Defaults["icon"] ? IconValue : NonNullable<IconValue> | Defaults["icon"];
    };
    iconColor: unknown extends Defaults["iconColor"] ? StringConstructor : {
        type: PropType<unknown extends Defaults["iconColor"] ? string : string | Defaults["iconColor"]>;
        default: unknown extends Defaults["iconColor"] ? string : string | Defaults["iconColor"];
    };
    iconSize: unknown extends Defaults["iconSize"] ? PropType<string | number> : {
        type: PropType<unknown extends Defaults["iconSize"] ? string | number : string | number | Defaults["iconSize"]>;
        default: unknown extends Defaults["iconSize"] ? string | number : NonNullable<string | number> | Defaults["iconSize"];
    };
    iconSizes: unknown extends Defaults["iconSizes"] ? {
        type: PropType<[VIconBtnSizes, number][]>;
        default: () => (string | number)[][];
    } : Omit<{
        type: PropType<[VIconBtnSizes, number][]>;
        default: () => (string | number)[][];
    }, "type" | "default"> & {
        type: PropType<unknown extends Defaults["iconSizes"] ? [VIconBtnSizes, number][] : [VIconBtnSizes, number][] | Defaults["iconSizes"]>;
        default: unknown extends Defaults["iconSizes"] ? [VIconBtnSizes, number][] : [VIconBtnSizes, number][] | Defaults["iconSizes"];
    };
    loading: unknown extends Defaults["loading"] ? BooleanConstructor : {
        type: PropType<unknown extends Defaults["loading"] ? boolean : boolean | Defaults["loading"]>;
        default: unknown extends Defaults["loading"] ? boolean : boolean | Defaults["loading"];
    };
    opacity: unknown extends Defaults["opacity"] ? (StringConstructor | NumberConstructor)[] : {
        type: PropType<unknown extends Defaults["opacity"] ? string | number : string | number | Defaults["opacity"]>;
        default: unknown extends Defaults["opacity"] ? string | number : NonNullable<string | number> | Defaults["opacity"];
    };
    readonly: unknown extends Defaults["readonly"] ? BooleanConstructor : {
        type: PropType<unknown extends Defaults["readonly"] ? boolean : boolean | Defaults["readonly"]>;
        default: unknown extends Defaults["readonly"] ? boolean : boolean | Defaults["readonly"];
    };
    rotate: unknown extends Defaults["rotate"] ? (StringConstructor | NumberConstructor)[] : {
        type: PropType<unknown extends Defaults["rotate"] ? string | number : string | number | Defaults["rotate"]>;
        default: unknown extends Defaults["rotate"] ? string | number : NonNullable<string | number> | Defaults["rotate"];
    };
    size: unknown extends Defaults["size"] ? {
        type: PropType<VIconBtnSizes | number | string>;
        default: string;
    } : Omit<{
        type: PropType<VIconBtnSizes | number | string>;
        default: string;
    }, "type" | "default"> & {
        type: PropType<unknown extends Defaults["size"] ? string | number : string | number | Defaults["size"]>;
        default: unknown extends Defaults["size"] ? string | number : NonNullable<string | number> | Defaults["size"];
    };
    sizes: unknown extends Defaults["sizes"] ? {
        type: PropType<[VIconBtnSizes, number][]>;
        default: () => (string | number)[][];
    } : Omit<{
        type: PropType<[VIconBtnSizes, number][]>;
        default: () => (string | number)[][];
    }, "type" | "default"> & {
        type: PropType<unknown extends Defaults["sizes"] ? [VIconBtnSizes, number][] : [VIconBtnSizes, number][] | Defaults["sizes"]>;
        default: unknown extends Defaults["sizes"] ? [VIconBtnSizes, number][] : [VIconBtnSizes, number][] | Defaults["sizes"];
    };
    text: unknown extends Defaults["text"] ? {
        type: (StringConstructor | BooleanConstructor | NumberConstructor)[];
        default: undefined;
    } : Omit<{
        type: (StringConstructor | BooleanConstructor | NumberConstructor)[];
        default: undefined;
    }, "type" | "default"> & {
        type: PropType<unknown extends Defaults["text"] ? string | number | boolean : string | number | boolean | Defaults["text"]>;
        default: unknown extends Defaults["text"] ? string | number | boolean : NonNullable<string | number | boolean> | Defaults["text"];
    };
};
export declare const VIconBtn: {
    new (...args: any[]): import("vue").CreateComponentPublicInstanceWithMixins<{
        variant: "flat" | "text" | "elevated" | "tonal" | "outlined" | "plain";
        loading: boolean;
        style: import("vue").StyleValue;
        disabled: boolean;
        size: string | number;
        readonly: boolean;
        tag: string | import("../../util/index.js").JSXComponent;
        sizes: [VIconBtnSizes, number][];
        tile: boolean;
        baseVariant: "flat" | "text" | "elevated" | "tonal" | "outlined" | "plain";
        hideOverlay: boolean;
        iconSizes: [VIconBtnSizes, number][];
    } & {
        height?: string | number | undefined;
        width?: string | number | undefined;
        active?: boolean | undefined;
        border?: string | number | boolean | undefined;
        color?: string | undefined;
        opacity?: string | number | undefined;
        rotate?: string | number | undefined;
        text?: string | number | boolean | undefined;
        class?: any;
        theme?: string | undefined;
        icon?: IconValue | undefined;
        elevation?: string | number | undefined;
        rounded?: string | number | boolean | undefined;
        activeColor?: string | undefined;
        iconColor?: string | undefined;
        activeIcon?: IconValue | undefined;
        activeVariant?: "flat" | "text" | "elevated" | "tonal" | "outlined" | "plain" | undefined;
        iconSize?: string | number | undefined;
    } & {
        $children?: import("vue").VNodeChild | (() => import("vue").VNodeChild) | {
            default?: (() => import("vue").VNodeChild) | undefined;
            loader?: (() => import("vue").VNodeChild) | undefined;
        };
        'v-slots'?: {
            default?: false | (() => import("vue").VNodeChild) | undefined;
            loader?: false | (() => import("vue").VNodeChild) | undefined;
        } | undefined;
    } & {
        "v-slot:default"?: false | (() => import("vue").VNodeChild) | undefined;
        "v-slot:loader"?: false | (() => import("vue").VNodeChild) | undefined;
    } & {
        "onUpdate:active"?: ((value: boolean) => any) | undefined;
    }, {}, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
        'update:active': (value: boolean) => true;
    }, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, {
        variant: "flat" | "text" | "elevated" | "tonal" | "outlined" | "plain";
        active: boolean;
        loading: boolean;
        style: import("vue").StyleValue;
        text: string | number | boolean;
        disabled: boolean;
        size: string | number;
        readonly: boolean;
        tag: string | import("../../util/index.js").JSXComponent;
        sizes: [VIconBtnSizes, number][];
        rounded: string | number | boolean;
        tile: boolean;
        baseVariant: "flat" | "text" | "elevated" | "tonal" | "outlined" | "plain";
        hideOverlay: boolean;
        iconSizes: [VIconBtnSizes, number][];
    }, true, {}, import("vue").SlotsType<Partial<{
        default: () => import("vue").VNode[];
        loader: () => import("vue").VNode[];
    }>>, import("vue").GlobalComponents, import("vue").GlobalDirectives, string, {}, any, import("vue").ComponentProvideOptions, {
        P: {};
        B: {};
        D: {};
        C: {};
        M: {};
        Defaults: {};
    }, {
        variant: "flat" | "text" | "elevated" | "tonal" | "outlined" | "plain";
        loading: boolean;
        style: import("vue").StyleValue;
        disabled: boolean;
        size: string | number;
        readonly: boolean;
        tag: string | import("../../util/index.js").JSXComponent;
        sizes: [VIconBtnSizes, number][];
        tile: boolean;
        baseVariant: "flat" | "text" | "elevated" | "tonal" | "outlined" | "plain";
        hideOverlay: boolean;
        iconSizes: [VIconBtnSizes, number][];
    } & {
        height?: string | number | undefined;
        width?: string | number | undefined;
        active?: boolean | undefined;
        border?: string | number | boolean | undefined;
        color?: string | undefined;
        opacity?: string | number | undefined;
        rotate?: string | number | undefined;
        text?: string | number | boolean | undefined;
        class?: any;
        theme?: string | undefined;
        icon?: IconValue | undefined;
        elevation?: string | number | undefined;
        rounded?: string | number | boolean | undefined;
        activeColor?: string | undefined;
        iconColor?: string | undefined;
        activeIcon?: IconValue | undefined;
        activeVariant?: "flat" | "text" | "elevated" | "tonal" | "outlined" | "plain" | undefined;
        iconSize?: string | number | undefined;
    } & {
        $children?: import("vue").VNodeChild | (() => import("vue").VNodeChild) | {
            default?: (() => import("vue").VNodeChild) | undefined;
            loader?: (() => import("vue").VNodeChild) | undefined;
        };
        'v-slots'?: {
            default?: false | (() => import("vue").VNodeChild) | undefined;
            loader?: false | (() => import("vue").VNodeChild) | undefined;
        } | undefined;
    } & {
        "v-slot:default"?: false | (() => import("vue").VNodeChild) | undefined;
        "v-slot:loader"?: false | (() => import("vue").VNodeChild) | undefined;
    } & {
        "onUpdate:active"?: ((value: boolean) => any) | undefined;
    }, {}, {}, {}, {}, {
        variant: "flat" | "text" | "elevated" | "tonal" | "outlined" | "plain";
        active: boolean;
        loading: boolean;
        style: import("vue").StyleValue;
        text: string | number | boolean;
        disabled: boolean;
        size: string | number;
        readonly: boolean;
        tag: string | import("../../util/index.js").JSXComponent;
        sizes: [VIconBtnSizes, number][];
        rounded: string | number | boolean;
        tile: boolean;
        baseVariant: "flat" | "text" | "elevated" | "tonal" | "outlined" | "plain";
        hideOverlay: boolean;
        iconSizes: [VIconBtnSizes, number][];
    }>;
    __isFragment?: never;
    __isTeleport?: never;
    __isSuspense?: never;
} & import("vue").ComponentOptionsBase<{
    variant: "flat" | "text" | "elevated" | "tonal" | "outlined" | "plain";
    loading: boolean;
    style: import("vue").StyleValue;
    disabled: boolean;
    size: string | number;
    readonly: boolean;
    tag: string | import("../../util/index.js").JSXComponent;
    sizes: [VIconBtnSizes, number][];
    tile: boolean;
    baseVariant: "flat" | "text" | "elevated" | "tonal" | "outlined" | "plain";
    hideOverlay: boolean;
    iconSizes: [VIconBtnSizes, number][];
} & {
    height?: string | number | undefined;
    width?: string | number | undefined;
    active?: boolean | undefined;
    border?: string | number | boolean | undefined;
    color?: string | undefined;
    opacity?: string | number | undefined;
    rotate?: string | number | undefined;
    text?: string | number | boolean | undefined;
    class?: any;
    theme?: string | undefined;
    icon?: IconValue | undefined;
    elevation?: string | number | undefined;
    rounded?: string | number | boolean | undefined;
    activeColor?: string | undefined;
    iconColor?: string | undefined;
    activeIcon?: IconValue | undefined;
    activeVariant?: "flat" | "text" | "elevated" | "tonal" | "outlined" | "plain" | undefined;
    iconSize?: string | number | undefined;
} & {
    $children?: import("vue").VNodeChild | (() => import("vue").VNodeChild) | {
        default?: (() => import("vue").VNodeChild) | undefined;
        loader?: (() => import("vue").VNodeChild) | undefined;
    };
    'v-slots'?: {
        default?: false | (() => import("vue").VNodeChild) | undefined;
        loader?: false | (() => import("vue").VNodeChild) | undefined;
    } | undefined;
} & {
    "v-slot:default"?: false | (() => import("vue").VNodeChild) | undefined;
    "v-slot:loader"?: false | (() => import("vue").VNodeChild) | undefined;
} & {
    "onUpdate:active"?: ((value: boolean) => any) | undefined;
}, {}, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
    'update:active': (value: boolean) => true;
}, string, {
    variant: "flat" | "text" | "elevated" | "tonal" | "outlined" | "plain";
    active: boolean;
    loading: boolean;
    style: import("vue").StyleValue;
    text: string | number | boolean;
    disabled: boolean;
    size: string | number;
    readonly: boolean;
    tag: string | import("../../util/index.js").JSXComponent;
    sizes: [VIconBtnSizes, number][];
    rounded: string | number | boolean;
    tile: boolean;
    baseVariant: "flat" | "text" | "elevated" | "tonal" | "outlined" | "plain";
    hideOverlay: boolean;
    iconSizes: [VIconBtnSizes, number][];
}, {}, string, import("vue").SlotsType<Partial<{
    default: () => import("vue").VNode[];
    loader: () => import("vue").VNode[];
}>>, import("vue").GlobalComponents, import("vue").GlobalDirectives, string, import("vue").ComponentProvideOptions> & import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps & import("../../util/index.js").FilterPropsOptions<{
    color: StringConstructor;
    variant: Omit<{
        type: PropType<Variant>;
        default: string;
        validator: (v: any) => boolean;
    }, "type" | "default"> & {
        type: PropType<"flat" | "text" | "elevated" | "tonal" | "outlined" | "plain">;
        default: NonNullable<"flat" | "text" | "elevated" | "tonal" | "outlined" | "plain">;
    };
    theme: StringConstructor;
    tag: Omit<{
        type: PropType<string | import("../../util/index.js").JSXComponent>;
        default: string;
    }, "type" | "default"> & {
        type: PropType<string | import("../../util/index.js").JSXComponent>;
        default: NonNullable<string | import("../../util/index.js").JSXComponent>;
    };
    rounded: {
        type: (StringConstructor | BooleanConstructor | NumberConstructor)[];
        default: undefined;
    };
    tile: BooleanConstructor;
    elevation: {
        type: (StringConstructor | NumberConstructor)[];
        validator(v: any): boolean;
    };
    class: PropType<import("../../composables/component.js").ClassValue>;
    style: {
        type: PropType<import("vue").StyleValue>;
        default: null;
    };
    border: (StringConstructor | BooleanConstructor | NumberConstructor)[];
    active: {
        type: BooleanConstructor;
        default: undefined;
    };
    activeColor: StringConstructor;
    activeIcon: PropType<IconValue>;
    activeVariant: PropType<Variant>;
    baseVariant: {
        type: PropType<Variant>;
        default: string;
    };
    disabled: BooleanConstructor;
    height: (StringConstructor | NumberConstructor)[];
    width: (StringConstructor | NumberConstructor)[];
    hideOverlay: BooleanConstructor;
    icon: PropType<IconValue>;
    iconColor: StringConstructor;
    iconSize: PropType<VIconBtnSizes | number | string>;
    iconSizes: {
        type: PropType<[VIconBtnSizes, number][]>;
        default: () => (string | number)[][];
    };
    loading: BooleanConstructor;
    opacity: (StringConstructor | NumberConstructor)[];
    readonly: BooleanConstructor;
    rotate: (StringConstructor | NumberConstructor)[];
    size: {
        type: PropType<VIconBtnSizes | number | string>;
        default: string;
    };
    sizes: {
        type: PropType<[VIconBtnSizes, number][]>;
        default: () => (string | number)[][];
    };
    text: {
        type: (StringConstructor | BooleanConstructor | NumberConstructor)[];
        default: undefined;
    };
}, import("vue").ExtractPropTypes<{
    color: StringConstructor;
    variant: Omit<{
        type: PropType<Variant>;
        default: string;
        validator: (v: any) => boolean;
    }, "type" | "default"> & {
        type: PropType<"flat" | "text" | "elevated" | "tonal" | "outlined" | "plain">;
        default: NonNullable<"flat" | "text" | "elevated" | "tonal" | "outlined" | "plain">;
    };
    theme: StringConstructor;
    tag: Omit<{
        type: PropType<string | import("../../util/index.js").JSXComponent>;
        default: string;
    }, "type" | "default"> & {
        type: PropType<string | import("../../util/index.js").JSXComponent>;
        default: NonNullable<string | import("../../util/index.js").JSXComponent>;
    };
    rounded: {
        type: (StringConstructor | BooleanConstructor | NumberConstructor)[];
        default: undefined;
    };
    tile: BooleanConstructor;
    elevation: {
        type: (StringConstructor | NumberConstructor)[];
        validator(v: any): boolean;
    };
    class: PropType<import("../../composables/component.js").ClassValue>;
    style: {
        type: PropType<import("vue").StyleValue>;
        default: null;
    };
    border: (StringConstructor | BooleanConstructor | NumberConstructor)[];
    active: {
        type: BooleanConstructor;
        default: undefined;
    };
    activeColor: StringConstructor;
    activeIcon: PropType<IconValue>;
    activeVariant: PropType<Variant>;
    baseVariant: {
        type: PropType<Variant>;
        default: string;
    };
    disabled: BooleanConstructor;
    height: (StringConstructor | NumberConstructor)[];
    width: (StringConstructor | NumberConstructor)[];
    hideOverlay: BooleanConstructor;
    icon: PropType<IconValue>;
    iconColor: StringConstructor;
    iconSize: PropType<VIconBtnSizes | number | string>;
    iconSizes: {
        type: PropType<[VIconBtnSizes, number][]>;
        default: () => (string | number)[][];
    };
    loading: BooleanConstructor;
    opacity: (StringConstructor | NumberConstructor)[];
    readonly: BooleanConstructor;
    rotate: (StringConstructor | NumberConstructor)[];
    size: {
        type: PropType<VIconBtnSizes | number | string>;
        default: string;
    };
    sizes: {
        type: PropType<[VIconBtnSizes, number][]>;
        default: () => (string | number)[][];
    };
    text: {
        type: (StringConstructor | BooleanConstructor | NumberConstructor)[];
        default: undefined;
    };
}>>;
export type VIconBtn = InstanceType<typeof VIconBtn>;
