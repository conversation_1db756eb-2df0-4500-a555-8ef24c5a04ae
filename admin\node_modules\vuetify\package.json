{"name": "vuetify", "description": "Vue Material Component Framework", "version": "3.8.10", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "homepage": "https://vuetifyjs.com", "repository": {"type": "git", "url": "git+https://github.com/vuetifyjs/vuetify.git", "directory": "packages/vuetify"}, "keywords": ["vuetify", "ui framework", "component framework", "ui library", "component library", "material components", "vue framework"], "bugs": {"url": "https://issues.vuetifyjs.com"}, "funding": {"type": "github", "url": "https://github.com/sponsors/johnleider"}, "type": "module", "main": "lib/framework.js", "module": "lib/framework.js", "jsdelivr": "dist/vuetify.js", "unpkg": "dist/vuetify.js", "types": "lib/framework.d.ts", "sass": "lib/styles/main.sass", "styles": "lib/styles/main.css", "sideEffects": ["*.sass", "*.scss", "*.css", "*.vue"], "files": ["dist/", "lib/", "_settings.scss", "_styles.scss", "_tools.scss", "CHANGELOG.md"], "exports": {".": {"sass": "./lib/styles/main.sass", "style": "./lib/styles/main.css", "types": "./lib/framework.d.ts", "default": "./lib/framework.js"}, "./styles": {"sass": "./lib/styles/main.sass", "default": "./lib/styles/main.css"}, "./styles/*": "./lib/styles/*", "./framework": "./lib/framework.js", "./blueprints": "./lib/blueprints/index.js", "./blueprints/*": "./lib/blueprints/*.js", "./components": "./lib/components/index.js", "./components/*": "./lib/components/*/index.js", "./directives": "./lib/directives/index.js", "./directives/*": "./lib/directives/*/index.js", "./locale": "./lib/locale/index.js", "./locale/adapters/*": "./lib/locale/adapters/*.js", "./iconsets/*": "./lib/iconsets/*.js", "./labs/components": "./lib/labs/components.js", "./labs/*": "./lib/labs/*/index.js", "./util/colors": "./lib/util/colors.js", "./dist/vuetify.js": {"types": "./dist/vuetify.d.ts", "import": "./dist/vuetify.esm.js", "default": "./dist/vuetify.cjs"}, "./dist/vuetify-labs.js": {"types": "./dist/vuetify-labs.d.ts", "import": "./dist/vuetify-labs.esm.js", "default": "./dist/vuetify-labs.cjs"}, "./*.mjs": "./*.js", "./*": "./*"}, "typesVersions": {"*": {"*": ["*", "dist/*", "lib/*", "lib/*.d.ts", "lib/*/index.d.ts"]}}, "engines": {"node": "^12.20 || >=14.13"}, "scripts": {"watch": "pnpm build:lib --watch", "dev": "NODE_ENV=development vite", "dev:ssr": "NODE_ENV=development VITE_SSR=true vite-ssr", "dev:prod": "vite build -w", "dev:typecheck": "vue-tsc --noEmit --skipLib<PERSON>he<PERSON> --project ./tsconfig.dev.json", "build": "rimraf lib dist && pnpm build:lib && concurrently \"pnpm build:dist\" \"pnpm build:types:lib\" -n \"dist,types\" --kill-others-on-fail -gr && pnpm build:types:bundle", "build:dist": "rollup --config build/rollup.config.js", "build:lib": "NODE_ENV=lib babel src --out-dir lib --source-maps --extensions \".ts\",\".tsx\",\".snap\" --copy-files --no-copy-ignored --out-file-extension .js", "build:types": "pnpm build:types:lib && pnpm build:types:bundle", "build:types:lib": "tspc --pretty --emitDeclarationOnly -p tsconfig.dist.json && node build/transform-types.js", "build:types:bundle": "rollup --config build/rollup.types.config.js", "tsc": "tsc", "test": "vitest", "test:unit": "vitest --project unit", "test:browser": "vitest --project browser", "test:open": "TEST_BAIL=1 vitest --project browser -w", "test:coverage": "pnpm test --coverage", "test:percy": "percy exec -- vitest --project browser", "test:all": "concurrently -gr 'vitest --project unit' 'vitest --project browser --shard=1/5' 'vitest --project browser --shard=2/5' 'vitest --project browser --shard=3/5' 'vitest --project browser --shard=4/5' 'vitest --project browser --shard=5/5'", "lint": "concurrently -n \"tsc,eslint\" --kill-others-on-fail \"tsc -p tsconfig.checks.json --noEmit --pretty\" \"eslint src -f codeframe --max-warnings 0\"", "lint:fix": "concurrently -n \"tsc,eslint\" \"tsc -p tsconfig.checks.json --noEmit --pretty\" \"eslint --fix src\""}, "devDependencies": {"@date-io/core": "3.2.0", "@date-io/date-fns": "3.2.1", "@date-io/dayjs": "^3.2.0", "@date-io/luxon": "^3.2.0", "@date-io/moment": "^3.2.0", "@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/vue-fontawesome": "^3.0.8", "@intlify/devtools-types": "^11.1.3", "@percy/cli": "^1.30.10", "@percy/sdk-utils": "^1.30.10", "@rollup/plugin-alias": "^5.1.1", "@rollup/plugin-babel": "^6.0.4", "@rollup/plugin-node-resolve": "^16.0.1", "@rollup/plugin-typescript": "^12.1.2", "@testing-library/dom": "npm:@vuetify/testing-library-dom@1.0.2", "@testing-library/user-event": "^14.6.1", "@testing-library/vue": "^8.1.0", "@types/node": "22.15.31", "@types/resize-observer-browser": "^0.1.11", "@vitejs/plugin-vue": "^5.2.1", "@vitejs/plugin-vue-jsx": "^4.2.0", "@vitest/browser": "^3.1.1", "@vitest/coverage-istanbul": "^3.1.1", "@vitest/ui": "^3.1.1", "@vue/reactivity": "^3.5.13", "@vue/shared": "^3.5.13", "@vue/test-utils": "^2.4.6", "@vuetify/babel-plugin-jsx": "^1.6.0", "acorn-walk": "^8.3.4", "autoprefixer": "^10.4.21", "babel-plugin-add-import-extension": "1.5.1", "babel-plugin-module-resolver": "^5.0.2", "babel-plugin-transform-define": "^2.1.4", "babel-polyfill": "^6.26.0", "cssnano": "^7.0.6", "csstype": "^3.1.3", "cy-mobile-commands": "^0.3.0", "date-fns": "^3.6.0", "dayjs": "^1.11.13", "dotenv": "^16.5.0", "eslint-plugin-vitest": "0.5.4", "expect": "^29.7.0", "fast-glob": "^3.3.3", "identity-obj-proxy": "^3.0.0", "jsdom": "^26.1.0", "luxon": "^3.6.1", "micromatch": "^4.0.8", "moment": "^2.30.1", "postcss": "^8.5.3", "roboto-fontface": "^0.10.0", "rollup": "^4.43.0", "rollup-plugin-dts": "^6.2.1", "rollup-plugin-sass": "^1.15.2", "rollup-plugin-sourcemaps": "^0.6.3", "rollup-plugin-terser": "^7.0.2", "timezone-mock": "^1.3.6", "ts-node": "^10.9.2", "ts-patch": "^3.3.0", "typescript-transform-paths": "^3.5.5", "unplugin-auto-import": "19.3.0", "unplugin-vue-components": "^0.28.0", "upath": "^2.0.1", "vite": "^6.3.5", "vite-live-preview": "^0.3.2", "vite-ssr": "^0.17.2", "vitest": "^3.1.1", "vue": "^3.5.13", "vue-i18n": "^11.1.3", "vue-router": "^4.5.0", "webdriverio": "^9.15.0"}, "peerDependencies": {"typescript": ">=4.7", "vite-plugin-vuetify": ">=2.1.0", "vue": "^3.5.0", "webpack-plugin-vuetify": ">=3.1.0"}, "peerDependenciesMeta": {"typescript": {"optional": true}, "webpack-plugin-vuetify": {"optional": true}, "vite-plugin-vuetify": {"optional": true}}, "publishConfig": {"access": "public"}, "vetur": {"tags": "dist/json/tags.json", "attributes": "dist/json/attributes.json"}, "web-types": "dist/json/web-types.json", "gitHead": "420e3e3bda4d7ac0257282230c9eb2f2b9a15cf0"}