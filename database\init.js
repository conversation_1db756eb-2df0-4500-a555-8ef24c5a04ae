const mysql = require('mysql2/promise');
const fs = require('fs').promises;
const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '../backend/.env') });

async function initDatabase() {
  let connection;

  try {
    // 连接到MySQL服务器（不指定数据库）
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 3306,
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      multipleStatements: true
    });

    console.log('连接到MySQL服务器成功');

    // 首先创建数据库（如果不存在）
    const dbName = process.env.DB_NAME || 'aisun';
    console.log(`创建数据库: ${dbName}`);
    await connection.execute(`CREATE DATABASE IF NOT EXISTS \`${dbName}\` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci`);

    // 选择数据库
    await connection.execute(`USE \`${dbName}\``);
    console.log(`已选择数据库: ${dbName}`);

    // 读取并修改schema.sql，移除CREATE DATABASE语句
    let schemaSQL = await fs.readFile(path.join(__dirname, 'schema.sql'), 'utf8');

    // 移除CREATE DATABASE和USE语句，因为我们已经处理了
    schemaSQL = schemaSQL.replace(/CREATE DATABASE IF NOT EXISTS aisun[^;]*;/gi, '');
    schemaSQL = schemaSQL.replace(/USE aisun;/gi, '');

    // 执行表结构创建
    await connection.execute(schemaSQL);
    console.log('数据库结构创建成功');

    // 读取并执行分类数据
    const categoriesSQL = await fs.readFile(path.join(__dirname, 'seeds/categories.sql'), 'utf8');
    await connection.execute(categoriesSQL);
    console.log('分类数据插入成功');

    // 读取并执行示例数据
    const sampleDataSQL = await fs.readFile(path.join(__dirname, 'seeds/sample_data.sql'), 'utf8');
    await connection.execute(sampleDataSQL);
    console.log('示例数据插入成功');

    console.log('数据库初始化完成！');
    
  } catch (error) {
    console.error('数据库初始化失败:', error);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 如果直接运行此文件，则执行初始化
if (require.main === module) {
  initDatabase();
}

module.exports = { initDatabase };
