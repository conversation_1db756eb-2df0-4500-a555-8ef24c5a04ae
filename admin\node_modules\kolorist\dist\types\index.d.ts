export declare const enum SupportLevel {
    none = 0,
    ansi = 1,
    ansi256 = 2,
    trueColor = 3
}
export declare let options: {
    enabled: boolean;
    supportLevel: SupportLevel;
};
export declare function stripColors(str: string | number): string;
export declare const reset: (str: string | number) => string;
export declare const bold: (str: string | number) => string;
export declare const dim: (str: string | number) => string;
export declare const italic: (str: string | number) => string;
export declare const underline: (str: string | number) => string;
export declare const inverse: (str: string | number) => string;
export declare const hidden: (str: string | number) => string;
export declare const strikethrough: (str: string | number) => string;
export declare const black: (str: string | number) => string;
export declare const red: (str: string | number) => string;
export declare const green: (str: string | number) => string;
export declare const yellow: (str: string | number) => string;
export declare const blue: (str: string | number) => string;
export declare const magenta: (str: string | number) => string;
export declare const cyan: (str: string | number) => string;
export declare const white: (str: string | number) => string;
export declare const gray: (str: string | number) => string;
export declare const lightGray: (str: string | number) => string;
export declare const lightRed: (str: string | number) => string;
export declare const lightGreen: (str: string | number) => string;
export declare const lightYellow: (str: string | number) => string;
export declare const lightBlue: (str: string | number) => string;
export declare const lightMagenta: (str: string | number) => string;
export declare const lightCyan: (str: string | number) => string;
export declare const bgBlack: (str: string | number) => string;
export declare const bgRed: (str: string | number) => string;
export declare const bgGreen: (str: string | number) => string;
export declare const bgYellow: (str: string | number) => string;
export declare const bgBlue: (str: string | number) => string;
export declare const bgMagenta: (str: string | number) => string;
export declare const bgCyan: (str: string | number) => string;
export declare const bgWhite: (str: string | number) => string;
export declare const bgGray: (str: string | number) => string;
export declare const bgLightRed: (str: string | number) => string;
export declare const bgLightGreen: (str: string | number) => string;
export declare const bgLightYellow: (str: string | number) => string;
export declare const bgLightBlue: (str: string | number) => string;
export declare const bgLightMagenta: (str: string | number) => string;
export declare const bgLightCyan: (str: string | number) => string;
export declare const bgLightGray: (str: string | number) => string;
export declare const ansi256: (n: number) => (str: string | number) => string;
export declare const ansi256Bg: (n: number) => (str: string | number) => string;
export declare const trueColor: (r: number, g: number, b: number) => (str: string | number) => string;
export declare const trueColorBg: (r: number, g: number, b: number) => (str: string | number) => string;
export declare function link(text: string, url: string): string;
