const { body, validationResult } = require('express-validator');
const Banner = require('../models/Banner');

// 轮播图验证规则
const bannerValidation = [
  body('title')
    .isLength({ min: 1, max: 100 })
    .withMessage('标题长度必须在1-100个字符之间'),
  body('image')
    .isURL()
    .withMessage('请提供有效的图片URL'),
  body('link_type')
    .optional()
    .isIn(['news', 'article', 'video', 'external'])
    .withMessage('无效的链接类型')
];

// 获取活跃轮播图
const getActiveBanners = async (req, res) => {
  try {
    const banners = await Banner.getActiveBanners();
    res.json({ banners });

  } catch (error) {
    console.error('获取轮播图失败:', error);
    res.status(500).json({ message: '服务器内部错误' });
  }
};

// 获取轮播图列表（管理员）
const getBannerList = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const filters = {
      status: req.query.status,
      search: req.query.search
    };

    const result = await Banner.getList(page, limit, filters);
    res.json(result);

  } catch (error) {
    console.error('获取轮播图列表失败:', error);
    res.status(500).json({ message: '服务器内部错误' });
  }
};

// 创建轮播图
const createBanner = async (req, res) => {
  try {
    // 检查验证结果
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        message: '输入数据验证失败',
        errors: errors.array()
      });
    }

    const bannerId = await Banner.create(req.body);
    const banner = await Banner.findById(bannerId);

    res.status(201).json({
      message: '轮播图创建成功',
      banner
    });

  } catch (error) {
    console.error('创建轮播图失败:', error);
    res.status(500).json({ message: '服务器内部错误' });
  }
};

// 更新轮播图
const updateBanner = async (req, res) => {
  try {
    const { id } = req.params;
    const banner = await Banner.findById(id);

    if (!banner) {
      return res.status(404).json({ message: '轮播图不存在' });
    }

    const success = await Banner.update(id, req.body);
    if (!success) {
      return res.status(400).json({ message: '更新失败' });
    }

    const updatedBanner = await Banner.findById(id);
    res.json({
      message: '轮播图更新成功',
      banner: updatedBanner
    });

  } catch (error) {
    console.error('更新轮播图失败:', error);
    res.status(500).json({ message: '服务器内部错误' });
  }
};

// 删除轮播图
const deleteBanner = async (req, res) => {
  try {
    const { id } = req.params;
    const banner = await Banner.findById(id);

    if (!banner) {
      return res.status(404).json({ message: '轮播图不存在' });
    }

    const success = await Banner.delete(id);
    if (!success) {
      return res.status(400).json({ message: '删除失败' });
    }

    res.json({ message: '轮播图删除成功' });

  } catch (error) {
    console.error('删除轮播图失败:', error);
    res.status(500).json({ message: '服务器内部错误' });
  }
};

module.exports = {
  getActiveBanners,
  getBannerList,
  createBanner,
  updateBanner,
  deleteBanner,
  bannerValidation
};
