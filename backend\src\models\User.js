const { query, transaction } = require('../config/database');
const { hashPassword } = require('../utils/password');

class User {
  // 创建用户
  static async create(userData) {
    const { username, email, password, nickname, role = 'user' } = userData;
    
    const hashedPassword = await hashPassword(password);
    
    const result = await query(
      `INSERT INTO users (username, email, password_hash, nickname, role) 
       VALUES (?, ?, ?, ?, ?)`,
      [username, email, hashedPassword, nickname, role]
    );
    
    return result.insertId;
  }

  // 根据ID查找用户
  static async findById(id) {
    const users = await query(
      'SELECT id, username, email, nickname, avatar, role, status, points, created_at FROM users WHERE id = ?',
      [id]
    );
    return users[0] || null;
  }

  // 根据用户名查找用户
  static async findByUsername(username) {
    const users = await query(
      'SELECT * FROM users WHERE username = ?',
      [username]
    );
    return users[0] || null;
  }

  // 根据邮箱查找用户
  static async findByEmail(email) {
    const users = await query(
      'SELECT * FROM users WHERE email = ?',
      [email]
    );
    return users[0] || null;
  }

  // 更新用户信息
  static async update(id, updateData) {
    const fields = [];
    const values = [];
    
    Object.keys(updateData).forEach(key => {
      if (updateData[key] !== undefined) {
        fields.push(`${key} = ?`);
        values.push(updateData[key]);
      }
    });
    
    if (fields.length === 0) return false;
    
    values.push(id);
    
    const result = await query(
      `UPDATE users SET ${fields.join(', ')} WHERE id = ?`,
      values
    );
    
    return result.affectedRows > 0;
  }

  // 更新密码
  static async updatePassword(id, newPassword) {
    const hashedPassword = await hashPassword(newPassword);
    
    const result = await query(
      'UPDATE users SET password_hash = ? WHERE id = ?',
      [hashedPassword, id]
    );
    
    return result.affectedRows > 0;
  }

  // 获取用户列表
  static async getList(page = 1, limit = 10, filters = {}) {
    const offset = (page - 1) * limit;
    let whereClause = 'WHERE 1=1';
    const params = [];

    if (filters.role) {
      whereClause += ' AND role = ?';
      params.push(filters.role);
    }

    if (filters.status) {
      whereClause += ' AND status = ?';
      params.push(filters.status);
    }

    if (filters.search) {
      whereClause += ' AND (username LIKE ? OR email LIKE ? OR nickname LIKE ?)';
      const searchTerm = `%${filters.search}%`;
      params.push(searchTerm, searchTerm, searchTerm);
    }

    // 获取总数
    const countResult = await query(
      `SELECT COUNT(*) as total FROM users ${whereClause}`,
      params
    );
    const total = countResult[0].total;

    // 获取用户列表
    const users = await query(
      `SELECT id, username, email, nickname, avatar, role, status, points, created_at 
       FROM users ${whereClause} 
       ORDER BY created_at DESC 
       LIMIT ? OFFSET ?`,
      [...params, limit, offset]
    );

    return {
      users,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit)
    };
  }

  // 更新用户积分
  static async updatePoints(userId, points, type, source, description, sourceId = null) {
    return await transaction(async (connection) => {
      // 更新用户积分
      await connection.execute(
        'UPDATE users SET points = points + ? WHERE id = ?',
        [points, userId]
      );

      // 记录积分变化
      await connection.execute(
        `INSERT INTO point_logs (user_id, points, type, source, source_id, description) 
         VALUES (?, ?, ?, ?, ?, ?)`,
        [userId, points, type, source, sourceId, description]
      );

      return true;
    });
  }

  // 删除用户
  static async delete(id) {
    const result = await query('DELETE FROM users WHERE id = ?', [id]);
    return result.affectedRows > 0;
  }
}

module.exports = User;
