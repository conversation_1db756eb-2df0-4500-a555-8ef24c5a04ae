const { body, validationResult } = require('express-validator');
const Comment = require('../models/Comment');

// 评论验证规则
const commentValidation = [
  body('content')
    .isLength({ min: 1, max: 1000 })
    .withMessage('评论内容长度必须在1-1000个字符之间'),
  body('target_type')
    .isIn(['article', 'news', 'video', 'book'])
    .withMessage('无效的评论目标类型'),
  body('target_id')
    .isInt({ min: 1 })
    .withMessage('目标ID必须是正整数')
];

// 获取评论列表
const getComments = async (req, res) => {
  try {
    const { target_type, target_id } = req.params;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;

    const result = await Comment.getByTarget(target_type, target_id, page, limit);
    
    // 如果用户已登录，也返回用户自己的待审核评论
    if (req.user) {
      const userPendingComments = await Comment.getUserComments(
        req.user.id, 
        target_type, 
        parseInt(target_id), 
        'pending'
      );
      result.userPendingComments = userPendingComments;
    }

    res.json(result);

  } catch (error) {
    console.error('获取评论列表失败:', error);
    res.status(500).json({ message: '服务器内部错误' });
  }
};

// 创建评论
const createComment = async (req, res) => {
  try {
    // 检查验证结果
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        message: '输入数据验证失败',
        errors: errors.array()
      });
    }

    const commentData = {
      ...req.body,
      user_id: req.user.id,
      status: 'pending' // 评论需要审核
    };

    const commentId = await Comment.create(commentData);
    const comment = await Comment.findById(commentId);

    res.status(201).json({
      message: '评论提交成功，等待审核',
      comment
    });

  } catch (error) {
    console.error('创建评论失败:', error);
    res.status(500).json({ message: '服务器内部错误' });
  }
};

// 更新评论
const updateComment = async (req, res) => {
  try {
    const { id } = req.params;
    const comment = await Comment.findById(id);

    if (!comment) {
      return res.status(404).json({ message: '评论不存在' });
    }

    // 检查权限：只有评论作者可以编辑
    if (comment.user_id !== req.user.id) {
      return res.status(403).json({ message: '权限不足' });
    }

    // 只允许编辑待审核的评论
    if (comment.status !== 'pending') {
      return res.status(400).json({ message: '只能编辑待审核的评论' });
    }

    const success = await Comment.update(id, { content: req.body.content });
    if (!success) {
      return res.status(400).json({ message: '更新失败' });
    }

    const updatedComment = await Comment.findById(id);
    res.json({
      message: '评论更新成功',
      comment: updatedComment
    });

  } catch (error) {
    console.error('更新评论失败:', error);
    res.status(500).json({ message: '服务器内部错误' });
  }
};

// 删除评论
const deleteComment = async (req, res) => {
  try {
    const { id } = req.params;
    const comment = await Comment.findById(id);

    if (!comment) {
      return res.status(404).json({ message: '评论不存在' });
    }

    // 检查权限：评论作者或管理员可以删除
    if (comment.user_id !== req.user.id && req.user.role !== 'admin') {
      return res.status(403).json({ message: '权限不足' });
    }

    const success = await Comment.delete(id);
    if (!success) {
      return res.status(400).json({ message: '删除失败' });
    }

    res.json({ message: '评论删除成功' });

  } catch (error) {
    console.error('删除评论失败:', error);
    res.status(500).json({ message: '服务器内部错误' });
  }
};

// 获取待审核评论列表（管理员）
const getPendingComments = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;

    const result = await Comment.getPendingList(page, limit);
    res.json(result);

  } catch (error) {
    console.error('获取待审核评论失败:', error);
    res.status(500).json({ message: '服务器内部错误' });
  }
};

// 审核评论
const reviewComment = async (req, res) => {
  try {
    const { id } = req.params;
    const { status, reject_reason } = req.body;

    if (!['approved', 'rejected'].includes(status)) {
      return res.status(400).json({ message: '无效的审核状态' });
    }

    if (status === 'rejected' && !reject_reason) {
      return res.status(400).json({ message: '拒绝时必须提供拒绝原因' });
    }

    const comment = await Comment.findById(id);
    if (!comment) {
      return res.status(404).json({ message: '评论不存在' });
    }

    const success = await Comment.review(id, {
      status,
      reject_reason,
      reviewed_by: req.user.id
    });

    if (!success) {
      return res.status(400).json({ message: '审核失败' });
    }

    const updatedComment = await Comment.findById(id);
    res.json({
      message: status === 'approved' ? '评论审核通过' : '评论审核拒绝',
      comment: updatedComment
    });

  } catch (error) {
    console.error('审核评论失败:', error);
    res.status(500).json({ message: '服务器内部错误' });
  }
};

module.exports = {
  getComments,
  createComment,
  updateComment,
  deleteComment,
  getPendingComments,
  reviewComment,
  commentValidation
};
