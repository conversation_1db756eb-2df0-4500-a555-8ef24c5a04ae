{"name": "aisun-backend", "version": "1.0.0", "description": "爱生网后端API服务", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["nodejs", "express", "mysql", "api"], "author": "", "license": "ISC", "dependencies": {"bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.21.2", "express-rate-limit": "^7.5.1", "express-validator": "^7.2.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "multer": "^2.0.1", "mysql2": "^3.14.1"}, "devDependencies": {"nodemon": "^3.1.10"}}