import type { PropType } from 'vue';
export type VCalendarHeaderSlots = {
    title: {
        title?: string;
    };
};
export declare const makeVCalendarHeaderProps: <Defaults extends {
    nextIcon?: unknown;
    prevIcon?: unknown;
    title?: unknown;
    text?: unknown;
    viewMode?: unknown;
} = {}>(defaults?: Defaults | undefined) => {
    nextIcon: unknown extends Defaults["nextIcon"] ? {
        type: StringConstructor;
        default: string;
    } : Omit<{
        type: StringConstructor;
        default: string;
    }, "type" | "default"> & {
        type: PropType<unknown extends Defaults["nextIcon"] ? string : string | Defaults["nextIcon"]>;
        default: unknown extends Defaults["nextIcon"] ? string : string | Defaults["nextIcon"];
    };
    prevIcon: unknown extends Defaults["prevIcon"] ? {
        type: StringConstructor;
        default: string;
    } : Omit<{
        type: StringConstructor;
        default: string;
    }, "type" | "default"> & {
        type: PropType<unknown extends Defaults["prevIcon"] ? string : string | Defaults["prevIcon"]>;
        default: unknown extends Defaults["prevIcon"] ? string : string | Defaults["prevIcon"];
    };
    title: unknown extends Defaults["title"] ? StringConstructor : {
        type: PropType<unknown extends Defaults["title"] ? string : string | Defaults["title"]>;
        default: unknown extends Defaults["title"] ? string : string | Defaults["title"];
    };
    text: unknown extends Defaults["text"] ? {
        type: StringConstructor;
        default: string;
    } : Omit<{
        type: StringConstructor;
        default: string;
    }, "type" | "default"> & {
        type: PropType<unknown extends Defaults["text"] ? string : string | Defaults["text"]>;
        default: unknown extends Defaults["text"] ? string : string | Defaults["text"];
    };
    viewMode: unknown extends Defaults["viewMode"] ? {
        type: PropType<"month" | "week" | "day">;
        default: string;
    } : Omit<{
        type: PropType<"month" | "week" | "day">;
        default: string;
    }, "type" | "default"> & {
        type: PropType<unknown extends Defaults["viewMode"] ? "day" | "month" | "week" : "day" | "month" | "week" | Defaults["viewMode"]>;
        default: unknown extends Defaults["viewMode"] ? "day" | "month" | "week" : Defaults["viewMode"] | NonNullable<"day" | "month" | "week">;
    };
};
export declare const VCalendarHeader: {
    new (...args: any[]): import("vue").CreateComponentPublicInstanceWithMixins<{
        text: string;
        nextIcon: string;
        prevIcon: string;
        viewMode: "day" | "month" | "week";
    } & {
        title?: string | undefined;
    } & {
        $children?: {} | import("vue").VNodeChild | {
            title?: ((arg: {
                title?: string;
            }) => import("vue").VNodeChild) | undefined;
        };
        'v-slots'?: {
            title?: false | ((arg: {
                title?: string;
            }) => import("vue").VNodeChild) | undefined;
        } | undefined;
    } & {
        "v-slot:title"?: false | ((arg: {
            title?: string;
        }) => import("vue").VNodeChild) | undefined;
    } & {
        "onClick:prev"?: (() => any) | undefined;
        "onClick:next"?: (() => any) | undefined;
        "onClick:toToday"?: (() => any) | undefined;
    }, {}, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
        'click:next': () => true;
        'click:prev': () => true;
        'click:toToday': () => true;
    }, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, {
        text: string;
        nextIcon: string;
        prevIcon: string;
        viewMode: "day" | "month" | "week";
    }, true, {}, import("vue").SlotsType<Partial<{
        title: (arg: {
            title?: string;
        }) => import("vue").VNode[];
    }>>, import("vue").GlobalComponents, import("vue").GlobalDirectives, string, {}, any, import("vue").ComponentProvideOptions, {
        P: {};
        B: {};
        D: {};
        C: {};
        M: {};
        Defaults: {};
    }, {
        text: string;
        nextIcon: string;
        prevIcon: string;
        viewMode: "day" | "month" | "week";
    } & {
        title?: string | undefined;
    } & {
        $children?: {} | import("vue").VNodeChild | {
            title?: ((arg: {
                title?: string;
            }) => import("vue").VNodeChild) | undefined;
        };
        'v-slots'?: {
            title?: false | ((arg: {
                title?: string;
            }) => import("vue").VNodeChild) | undefined;
        } | undefined;
    } & {
        "v-slot:title"?: false | ((arg: {
            title?: string;
        }) => import("vue").VNodeChild) | undefined;
    } & {
        "onClick:prev"?: (() => any) | undefined;
        "onClick:next"?: (() => any) | undefined;
        "onClick:toToday"?: (() => any) | undefined;
    }, {}, {}, {}, {}, {
        text: string;
        nextIcon: string;
        prevIcon: string;
        viewMode: "day" | "month" | "week";
    }>;
    __isFragment?: never;
    __isTeleport?: never;
    __isSuspense?: never;
} & import("vue").ComponentOptionsBase<{
    text: string;
    nextIcon: string;
    prevIcon: string;
    viewMode: "day" | "month" | "week";
} & {
    title?: string | undefined;
} & {
    $children?: {} | import("vue").VNodeChild | {
        title?: ((arg: {
            title?: string;
        }) => import("vue").VNodeChild) | undefined;
    };
    'v-slots'?: {
        title?: false | ((arg: {
            title?: string;
        }) => import("vue").VNodeChild) | undefined;
    } | undefined;
} & {
    "v-slot:title"?: false | ((arg: {
        title?: string;
    }) => import("vue").VNodeChild) | undefined;
} & {
    "onClick:prev"?: (() => any) | undefined;
    "onClick:next"?: (() => any) | undefined;
    "onClick:toToday"?: (() => any) | undefined;
}, {}, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
    'click:next': () => true;
    'click:prev': () => true;
    'click:toToday': () => true;
}, string, {
    text: string;
    nextIcon: string;
    prevIcon: string;
    viewMode: "day" | "month" | "week";
}, {}, string, import("vue").SlotsType<Partial<{
    title: (arg: {
        title?: string;
    }) => import("vue").VNode[];
}>>, import("vue").GlobalComponents, import("vue").GlobalDirectives, string, import("vue").ComponentProvideOptions> & import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps & import("../../util/index.js").FilterPropsOptions<{
    nextIcon: {
        type: StringConstructor;
        default: string;
    };
    prevIcon: {
        type: StringConstructor;
        default: string;
    };
    title: StringConstructor;
    text: {
        type: StringConstructor;
        default: string;
    };
    viewMode: {
        type: PropType<"month" | "week" | "day">;
        default: string;
    };
}, import("vue").ExtractPropTypes<{
    nextIcon: {
        type: StringConstructor;
        default: string;
    };
    prevIcon: {
        type: StringConstructor;
        default: string;
    };
    title: StringConstructor;
    text: {
        type: StringConstructor;
        default: string;
    };
    viewMode: {
        type: PropType<"month" | "week" | "day">;
        default: string;
    };
}>>;
export type VCalendarHeader = InstanceType<typeof VCalendarHeader>;
