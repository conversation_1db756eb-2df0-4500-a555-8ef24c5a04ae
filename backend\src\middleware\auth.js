const jwt = require('jsonwebtoken');
const { query } = require('../config/database');

// 验证JWT token
const authenticateToken = async (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

  if (!token) {
    return res.status(401).json({ message: '访问令牌缺失' });
  }

  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    // 从数据库获取用户信息
    const users = await query(
      'SELECT id, username, email, nickname, role, status FROM users WHERE id = ?',
      [decoded.userId]
    );

    if (users.length === 0) {
      return res.status(401).json({ message: '用户不存在' });
    }

    const user = users[0];
    
    if (user.status !== 'active') {
      return res.status(401).json({ message: '用户账户已被禁用' });
    }

    req.user = user;
    next();
  } catch (error) {
    console.error('Token验证失败:', error);
    return res.status(403).json({ message: '访问令牌无效' });
  }
};

// 检查用户角色权限
const requireRole = (roles) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({ message: '未认证用户' });
    }

    if (!roles.includes(req.user.role)) {
      return res.status(403).json({ message: '权限不足' });
    }

    next();
  };
};

// 检查是否为管理员
const requireAdmin = requireRole(['admin']);

// 检查是否为管理员或审核员
const requireModerator = requireRole(['admin', 'moderator']);

// 可选的认证中间件（不强制要求登录）
const optionalAuth = async (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    req.user = null;
    return next();
  }

  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    const users = await query(
      'SELECT id, username, email, nickname, role, status FROM users WHERE id = ?',
      [decoded.userId]
    );

    if (users.length > 0 && users[0].status === 'active') {
      req.user = users[0];
    } else {
      req.user = null;
    }
  } catch (error) {
    req.user = null;
  }

  next();
};

module.exports = {
  authenticateToken,
  requireRole,
  requireAdmin,
  requireModerator,
  optionalAuth
};
