const { query } = require('../config/database');

class Banner {
  // 创建轮播图
  static async create(bannerData) {
    const {
      title,
      image,
      link_url,
      link_type,
      link_id,
      sort_order = 0,
      status = 'active',
      start_time,
      end_time
    } = bannerData;

    const result = await query(
      `INSERT INTO banners (title, image, link_url, link_type, link_id, sort_order, status, start_time, end_time) 
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [title, image, link_url, link_type, link_id, sort_order, status, start_time, end_time]
    );

    return result.insertId;
  }

  // 根据ID获取轮播图
  static async findById(id) {
    const banners = await query(
      'SELECT * FROM banners WHERE id = ?',
      [id]
    );
    return banners[0] || null;
  }

  // 获取活跃的轮播图
  static async getActiveBanners() {
    const now = new Date();
    const banners = await query(
      `SELECT * FROM banners 
       WHERE status = 'active' 
       AND (start_time IS NULL OR start_time <= ?) 
       AND (end_time IS NULL OR end_time >= ?)
       ORDER BY sort_order ASC, id ASC`,
      [now, now]
    );
    return banners;
  }

  // 获取轮播图列表（管理员）
  static async getList(page = 1, limit = 10, filters = {}) {
    const offset = (page - 1) * limit;
    let whereClause = 'WHERE 1=1';
    const params = [];

    if (filters.status) {
      whereClause += ' AND status = ?';
      params.push(filters.status);
    }

    if (filters.search) {
      whereClause += ' AND title LIKE ?';
      params.push(`%${filters.search}%`);
    }

    // 获取总数
    const countResult = await query(
      `SELECT COUNT(*) as total FROM banners ${whereClause}`,
      params
    );
    const total = countResult[0].total;

    // 获取轮播图列表
    const banners = await query(
      `SELECT * FROM banners ${whereClause} 
       ORDER BY sort_order ASC, created_at DESC 
       LIMIT ? OFFSET ?`,
      [...params, limit, offset]
    );

    return {
      banners,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit)
    };
  }

  // 更新轮播图
  static async update(id, updateData) {
    const fields = [];
    const values = [];

    Object.keys(updateData).forEach(key => {
      if (updateData[key] !== undefined) {
        fields.push(`${key} = ?`);
        values.push(updateData[key]);
      }
    });

    if (fields.length === 0) return false;

    values.push(id);

    const result = await query(
      `UPDATE banners SET ${fields.join(', ')} WHERE id = ?`,
      values
    );

    return result.affectedRows > 0;
  }

  // 删除轮播图
  static async delete(id) {
    const result = await query('DELETE FROM banners WHERE id = ?', [id]);
    return result.affectedRows > 0;
  }
}

module.exports = Banner;
