const axios = require('axios');

const API_BASE = 'http://localhost:3000/api';

// 测试API连接
async function testAPIConnection() {
  try {
    console.log('🔍 测试API连接...');
    const response = await axios.get(`${API_BASE}/../health`);
    console.log('✅ API连接正常:', response.data);
    return true;
  } catch (error) {
    console.error('❌ API连接失败:', error.message);
    return false;
  }
}

// 测试用户注册
async function testUserRegistration() {
  try {
    console.log('\n🔍 测试用户注册...');
    const userData = {
      username: 'testuser' + Date.now(),
      email: `test${Date.now()}@example.com`,
      password: '123456',
      nickname: '测试用户'
    };
    
    const response = await axios.post(`${API_BASE}/auth/register`, userData);
    console.log('✅ 用户注册成功:', response.data.user.username);
    return response.data.token;
  } catch (error) {
    console.error('❌ 用户注册失败:', error.response?.data?.message || error.message);
    return null;
  }
}

// 测试用户登录
async function testUserLogin() {
  try {
    console.log('\n🔍 测试管理员登录...');
    const loginData = {
      username: 'admin',
      password: 'admin123'
    };
    
    const response = await axios.post(`${API_BASE}/auth/login`, loginData);
    console.log('✅ 管理员登录成功:', response.data.user.username);
    return response.data.token;
  } catch (error) {
    console.error('❌ 管理员登录失败:', error.response?.data?.message || error.message);
    return null;
  }
}

// 测试新闻API
async function testNewsAPI(token) {
  try {
    console.log('\n🔍 测试新闻API...');
    
    // 获取新闻列表
    const response = await axios.get(`${API_BASE}/news`);
    console.log('✅ 获取新闻列表成功，数量:', response.data.news?.length || 0);
    
    // 获取新闻分类
    const categoriesResponse = await axios.get(`${API_BASE}/news/categories`);
    console.log('✅ 获取新闻分类成功，数量:', categoriesResponse.data.categories?.length || 0);
    
    return true;
  } catch (error) {
    console.error('❌ 新闻API测试失败:', error.response?.data?.message || error.message);
    return false;
  }
}

// 测试文章API
async function testArticlesAPI(token) {
  try {
    console.log('\n🔍 测试文章API...');
    
    // 获取文章列表
    const response = await axios.get(`${API_BASE}/articles`);
    console.log('✅ 获取文章列表成功，数量:', response.data.articles?.length || 0);
    
    // 获取文章分类
    const categoriesResponse = await axios.get(`${API_BASE}/articles/categories`);
    console.log('✅ 获取文章分类成功，数量:', categoriesResponse.data.categories?.length || 0);
    
    return true;
  } catch (error) {
    console.error('❌ 文章API测试失败:', error.response?.data?.message || error.message);
    return false;
  }
}

// 测试轮播图API
async function testBannersAPI() {
  try {
    console.log('\n🔍 测试轮播图API...');
    
    const response = await axios.get(`${API_BASE}/banners/active`);
    console.log('✅ 获取轮播图成功，数量:', response.data.banners?.length || 0);
    
    return true;
  } catch (error) {
    console.error('❌ 轮播图API测试失败:', error.response?.data?.message || error.message);
    return false;
  }
}

// 主测试函数
async function runTests() {
  console.log('🚀 开始API测试...\n');
  
  // 测试API连接
  const isConnected = await testAPIConnection();
  if (!isConnected) {
    console.log('\n❌ API服务器未启动，请先启动后端服务器');
    return;
  }
  
  // 测试用户功能
  const userToken = await testUserRegistration();
  const adminToken = await testUserLogin();
  
  // 测试各个API
  await testNewsAPI(adminToken);
  await testArticlesAPI(adminToken);
  await testBannersAPI();
  
  console.log('\n🎉 API测试完成！');
}

// 运行测试
runTests().catch(console.error);
