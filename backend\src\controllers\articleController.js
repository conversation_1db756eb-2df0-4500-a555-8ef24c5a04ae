const { body, validationResult } = require('express-validator');
const Article = require('../models/Article');

// 文章验证规则
const articleValidation = [
  body('title')
    .isLength({ min: 1, max: 200 })
    .withMessage('标题长度必须在1-200个字符之间'),
  body('content')
    .isLength({ min: 1 })
    .withMessage('内容不能为空'),
  body('category_id')
    .optional()
    .isInt({ min: 1 })
    .withMessage('分类ID必须是正整数')
];

// 获取文章列表
const getArticleList = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const filters = {
      category_id: req.query.category_id,
      status: req.query.status || 'published',
      author_id: req.query.author_id,
      search: req.query.search
    };

    const result = await Article.getList(page, limit, filters);
    res.json(result);

  } catch (error) {
    console.error('获取文章列表失败:', error);
    res.status(500).json({ message: '服务器内部错误' });
  }
};

// 获取文章详情
const getArticleDetail = async (req, res) => {
  try {
    const { id } = req.params;
    const article = await Article.findById(id);

    if (!article) {
      return res.status(404).json({ message: '文章不存在' });
    }

    // 检查权限：未发布的文章只有作者和管理员可以查看
    if (article.status !== 'published') {
      if (!req.user || (article.author_id !== req.user.id && !['admin', 'moderator'].includes(req.user.role))) {
        return res.status(403).json({ message: '权限不足' });
      }
    }

    // 如果是已发布的文章，增加浏览次数
    if (article.status === 'published') {
      await Article.incrementViewCount(id);
      article.view_count += 1;
    }

    res.json({ article });

  } catch (error) {
    console.error('获取文章详情失败:', error);
    res.status(500).json({ message: '服务器内部错误' });
  }
};

// 创建文章
const createArticle = async (req, res) => {
  try {
    // 检查验证结果
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        message: '输入数据验证失败',
        errors: errors.array()
      });
    }

    const articleData = {
      ...req.body,
      author_id: req.user.id,
      status: 'pending' // 普通用户投稿需要审核
    };

    const articleId = await Article.create(articleData);
    const article = await Article.findById(articleId);

    res.status(201).json({
      message: '文章投稿成功，等待审核',
      article
    });

  } catch (error) {
    console.error('创建文章失败:', error);
    res.status(500).json({ message: '服务器内部错误' });
  }
};

// 更新文章
const updateArticle = async (req, res) => {
  try {
    const { id } = req.params;
    const article = await Article.findById(id);

    if (!article) {
      return res.status(404).json({ message: '文章不存在' });
    }

    // 检查权限：只有作者或管理员可以编辑
    if (article.author_id !== req.user.id && req.user.role !== 'admin') {
      return res.status(403).json({ message: '权限不足' });
    }

    // 如果是普通用户编辑已发布的文章，需要重新审核
    let updateData = { ...req.body };
    if (req.user.role === 'user' && article.status === 'published') {
      updateData.status = 'pending';
      updateData.reviewed_by = null;
      updateData.reviewed_at = null;
      updateData.published_at = null;
    }

    const success = await Article.update(id, updateData);
    if (!success) {
      return res.status(400).json({ message: '更新失败' });
    }

    const updatedArticle = await Article.findById(id);
    res.json({
      message: '文章更新成功',
      article: updatedArticle
    });

  } catch (error) {
    console.error('更新文章失败:', error);
    res.status(500).json({ message: '服务器内部错误' });
  }
};

// 删除文章
const deleteArticle = async (req, res) => {
  try {
    const { id } = req.params;
    const article = await Article.findById(id);

    if (!article) {
      return res.status(404).json({ message: '文章不存在' });
    }

    // 检查权限：只有作者或管理员可以删除
    if (article.author_id !== req.user.id && req.user.role !== 'admin') {
      return res.status(403).json({ message: '权限不足' });
    }

    const success = await Article.delete(id);
    if (!success) {
      return res.status(400).json({ message: '删除失败' });
    }

    res.json({ message: '文章删除成功' });

  } catch (error) {
    console.error('删除文章失败:', error);
    res.status(500).json({ message: '服务器内部错误' });
  }
};

// 审核文章
const reviewArticle = async (req, res) => {
  try {
    const { id } = req.params;
    const { status, reject_reason } = req.body;

    if (!['published', 'rejected'].includes(status)) {
      return res.status(400).json({ message: '无效的审核状态' });
    }

    if (status === 'rejected' && !reject_reason) {
      return res.status(400).json({ message: '拒绝时必须提供拒绝原因' });
    }

    const article = await Article.findById(id);
    if (!article) {
      return res.status(404).json({ message: '文章不存在' });
    }

    const success = await Article.review(id, {
      status,
      reject_reason,
      reviewed_by: req.user.id
    });

    if (!success) {
      return res.status(400).json({ message: '审核失败' });
    }

    const updatedArticle = await Article.findById(id);
    res.json({
      message: status === 'published' ? '文章审核通过' : '文章审核拒绝',
      article: updatedArticle
    });

  } catch (error) {
    console.error('审核文章失败:', error);
    res.status(500).json({ message: '服务器内部错误' });
  }
};

// 获取待审核文章列表
const getPendingArticles = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;

    const result = await Article.getPendingList(page, limit);
    res.json(result);

  } catch (error) {
    console.error('获取待审核文章失败:', error);
    res.status(500).json({ message: '服务器内部错误' });
  }
};

// 获取文章分类
const getCategories = async (req, res) => {
  try {
    const categories = await Article.getCategories();
    res.json({ categories });

  } catch (error) {
    console.error('获取文章分类失败:', error);
    res.status(500).json({ message: '服务器内部错误' });
  }
};

// 点赞文章
const likeArticle = async (req, res) => {
  try {
    const { id } = req.params;
    const article = await Article.findById(id);

    if (!article) {
      return res.status(404).json({ message: '文章不存在' });
    }

    if (article.status !== 'published') {
      return res.status(400).json({ message: '只能点赞已发布的文章' });
    }

    await Article.incrementLikeCount(id);
    res.json({ message: '点赞成功' });

  } catch (error) {
    console.error('点赞文章失败:', error);
    res.status(500).json({ message: '服务器内部错误' });
  }
};

module.exports = {
  getArticleList,
  getArticleDetail,
  createArticle,
  updateArticle,
  deleteArticle,
  reviewArticle,
  getPendingArticles,
  getCategories,
  likeArticle,
  articleValidation
};
