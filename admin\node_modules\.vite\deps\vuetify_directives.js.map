{"version": 3, "sources": ["../../vuetify/src/directives/mutate/index.ts", "../../vuetify/src/directives/resize/index.ts", "../../vuetify/src/directives/scroll/index.ts", "../../vuetify/src/composables/directiveComponent.ts", "../../vuetify/src/directives/tooltip/index.ts"], "sourcesContent": ["// Types\nimport type { DirectiveBinding } from 'vue'\nimport type { MutationOptions } from '@/composables/mutationObserver'\n\nexport interface MutationDirectiveBinding extends Omit<DirectiveBinding, 'modifiers' | 'value'> {\n  value: MutationCallback | { handler: MutationCallback, options?: MutationObserverInit }\n  modifiers: MutationOptions\n}\n\nfunction mounted (el: HTMLElement, binding: MutationDirectiveBinding) {\n  const modifiers = binding.modifiers || {}\n  const value = binding.value\n  const { once, immediate, ...modifierKeys } = modifiers\n  const defaultValue = !Object.keys(modifierKeys).length\n\n  const { handler, options } = typeof value === 'object'\n    ? value\n    : {\n      handler: value,\n      options: {\n        attributes: modifierKeys?.attr ?? defaultValue,\n        characterData: modifierKeys?.char ?? defaultValue,\n        childList: modifierKeys?.child ?? defaultValue,\n        subtree: modifierKeys?.sub ?? defaultValue,\n      },\n    }\n\n  const observer = new MutationObserver((\n    mutations: MutationRecord[] = [],\n    observer: MutationObserver\n  ) => {\n    handler?.(mutations, observer)\n\n    if (once) unmounted(el, binding)\n  })\n\n  if (immediate) handler?.([], observer)\n\n  el._mutate = Object(el._mutate)\n  el._mutate![binding.instance!.$.uid] = { observer }\n\n  observer.observe(el, options)\n}\n\nfunction unmounted (el: HTMLElement, binding: MutationDirectiveBinding) {\n  if (!el._mutate?.[binding.instance!.$.uid]) return\n\n  el._mutate[binding.instance!.$.uid]!.observer.disconnect()\n  delete el._mutate[binding.instance!.$.uid]\n}\n\nexport const Mutate = {\n  mounted,\n  unmounted,\n}\n\nexport default Mutate\n", "// Types\nimport type { DirectiveBinding } from 'vue'\n\ninterface ResizeDirectiveBinding extends Omit<DirectiveBinding, 'modifiers'> {\n  value: () => void\n  modifiers?: {\n    active?: boolean\n    quiet?: boolean\n  }\n}\n\nfunction mounted (el: HTMLElement, binding: ResizeDirectiveBinding) {\n  const handler = binding.value\n  const options: AddEventListenerOptions = {\n    passive: !binding.modifiers?.active,\n  }\n\n  window.addEventListener('resize', handler, options)\n\n  el._onResize = Object(el._onResize)\n  el._onResize![binding.instance!.$.uid] = {\n    handler,\n    options,\n  }\n\n  if (!binding.modifiers?.quiet) {\n    handler()\n  }\n}\n\nfunction unmounted (el: HTMLElement, binding: ResizeDirectiveBinding) {\n  if (!el._onResize?.[binding.instance!.$.uid]) return\n\n  const { handler, options } = el._onResize[binding.instance!.$.uid]!\n\n  window.removeEventListener('resize', handler, options)\n\n  delete el._onResize[binding.instance!.$.uid]\n}\n\nexport const Resize = {\n  mounted,\n  unmounted,\n}\n\nexport default Resize\n", "// Types\nimport type { DirectiveBinding } from 'vue'\n\ninterface ScrollDirectiveBinding extends Omit<DirectiveBinding, 'modifiers'> {\n  value: EventListener | {\n    handler: EventListener\n    options?: AddEventListenerOptions\n  } | EventListenerObject & { options?: AddEventListenerOptions }\n  modifiers?: {\n    self?: boolean\n  }\n}\n\nfunction mounted (el: HTMLElement, binding: ScrollDirectiveBinding) {\n  const { self = false } = binding.modifiers ?? {}\n  const value = binding.value\n  const options = (typeof value === 'object' && value.options) || { passive: true }\n  const handler = typeof value === 'function' || 'handleEvent' in value ? value : value.handler\n\n  const target = self\n    ? el\n    : binding.arg\n      ? document.querySelector(binding.arg)\n      : window\n\n  if (!target) return\n\n  target.addEventListener('scroll', handler, options)\n\n  el._onScroll = Object(el._onScroll)\n  el._onScroll![binding.instance!.$.uid] = {\n    handler,\n    options,\n    // Don't reference self\n    target: self ? undefined : target,\n  }\n}\n\nfunction unmounted (el: HTMLElement, binding: ScrollDirectiveBinding) {\n  if (!el._onScroll?.[binding.instance!.$.uid]) return\n\n  const { handler, options, target = el } = el._onScroll[binding.instance!.$.uid]!\n\n  target.removeEventListener('scroll', handler, options)\n  delete el._onScroll[binding.instance!.$.uid]\n}\n\nfunction updated (el: HTMLElement, binding: ScrollDirectiveBinding) {\n  if (binding.value === binding.oldValue) return\n\n  unmounted(el, binding)\n  mounted(el, binding)\n}\n\nexport const Scroll = {\n  mounted,\n  unmounted,\n  updated,\n}\n\nexport default Scroll\n", "// Utilities\nimport { h, mergeProps, render, resolveComponent } from 'vue'\nimport { consoleError, isObject } from '@/util'\n\n// Types\nimport type {\n  Component,\n  ComponentInternalInstance,\n  ComponentPublicInstance,\n  ConcreteComponent,\n  DirectiveBinding,\n  ObjectDirective,\n  VNode,\n} from 'vue'\nimport type { ComponentInstance } from '@/util'\n\ntype ExcludeProps =\n  | 'v-slots'\n  | `v-slot:${string}`\n  | `on${Uppercase<string>}${string}`\n  | 'key'\n  | 'ref'\n  | 'ref_for'\n  | 'ref_key'\n  | '$children'\n\ndeclare const CustomDirectiveSymbol: unique symbol\ntype DirectiveHook<B extends DirectiveBinding> = (el: any, binding: B, vnode: VNode<any, any>, prevVNode: VNode<any, any>) => void\nexport interface CustomDirective<B extends DirectiveBinding = DirectiveBinding> {\n  created?: DirectiveHook<B>\n  beforeMount?: DirectiveHook<B>\n  mounted?: DirectiveHook<B>\n  beforeUpdate?: DirectiveHook<B>\n  updated?: DirectiveHook<B>\n  beforeUnmount?: DirectiveHook<B>\n  unmounted?: DirectiveHook<B>\n  [CustomDirectiveSymbol]: true\n}\n\nexport function useDirectiveComponent <\n  Binding extends DirectiveBinding,\n> (component: string | Component, props?: (binding: Binding) => Record<string, any>): CustomDirective<Binding>\nexport function useDirectiveComponent <\n  C extends Component,\n  Props = Omit<ComponentInstance<C>['$props'], ExcludeProps>\n> (component: string | C, props?: Record<string, any>): ObjectDirective<any, Props>\nexport function useDirectiveComponent (\n  component: string | Component,\n  props?: Record<string, any> | ((binding: DirectiveBinding) => Record<string, any>)\n): ObjectDirective | CustomDirective {\n  const concreteComponent = (typeof component === 'string'\n    ? resolveComponent(component)\n    : component) as ConcreteComponent\n\n  const hook = mountComponent(concreteComponent, props)\n\n  return {\n    mounted: hook,\n    updated: hook,\n    unmounted (el: HTMLElement) {\n      render(null, el)\n    },\n  }\n}\n\nfunction mountComponent (component: ConcreteComponent, props?: Record<string, any> | ((binding: DirectiveBinding) => Record<string, any>)) {\n  return function (el: HTMLElement, binding: DirectiveBinding, vnode: VNode) {\n    const _props = typeof props === 'function' ? props(binding) : props\n    const text = binding.value?.text ?? binding.value ?? _props?.text\n    const value = isObject(binding.value) ? binding.value : {}\n\n    // Get the children from the props or directive value, or the element's children\n    const children = () => text ?? el.textContent\n\n    // If vnode.ctx is the same as the instance, then we're bound to a plain element\n    // and need to find the nearest parent component instance to inherit provides from\n    const provides = (vnode.ctx === binding.instance!.$\n      ? findComponentParent(vnode, binding.instance!.$)?.provides\n      : vnode.ctx?.provides) ?? binding.instance!.$.provides\n\n    const node = h(component, mergeProps(_props, value), children)\n    node.appContext = Object.assign(\n      Object.create(null),\n      (binding.instance as ComponentPublicInstance).$.appContext,\n      { provides }\n    )\n\n    render(node, el)\n  }\n}\n\nfunction findComponentParent (vnode: VNode, root: ComponentInternalInstance): ComponentInternalInstance | null {\n  // Walk the tree from root until we find the child vnode\n  const stack = new Set<VNode>()\n  const walk = (children: VNode[]): boolean => {\n    for (const child of children) {\n      if (!child) continue\n\n      if (child === vnode || (child.el && vnode.el && child.el === vnode.el)) {\n        return true\n      }\n\n      stack.add(child)\n      let result\n      if (child.suspense) {\n        result = walk([child.ssContent!])\n      } else if (Array.isArray(child.children)) {\n        result = walk(child.children as VNode[])\n      } else if (child.component?.vnode) {\n        result = walk([child.component?.subTree])\n      }\n      if (result) {\n        return result\n      }\n      stack.delete(child)\n    }\n\n    return false\n  }\n  if (!walk([root.subTree])) {\n    consoleError('Could not find original vnode, component will not inherit provides')\n    return root\n  }\n\n  // Return the first component parent\n  const result = Array.from(stack).reverse()\n  for (const child of result) {\n    if (child.component) {\n      return child.component\n    }\n  }\n  return root\n}\n", "// Components\nimport { VTooltip } from '@/components/VTooltip'\n\n// Composables\nimport { useDirectiveComponent } from '@/composables/directiveComponent'\n\n// Types\nimport type { DirectiveBinding } from 'vue'\nimport type { Anchor } from '@/util'\n\nexport interface TooltipDirectiveBinding extends Omit<DirectiveBinding<string>, 'arg' | 'value'> {\n  arg?: { [T in Anchor]: T extends `${infer A} ${infer B}` ? `${A}-${B}` : T }[Anchor]\n  value: boolean | string | Record<string, any>\n}\n\nexport const Tooltip = useDirectiveComponent<TooltipDirectiveBinding>(VTooltip, binding => {\n  return {\n    activator: 'parent',\n    location: binding.arg?.replace('-', ' '),\n    text: typeof binding.value === 'boolean' ? undefined : binding.value,\n  }\n})\n\nexport default Tooltip\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AASA,SAASA,QAASC,IAAiBC,SAAmC;AACpE,QAAMC,YAAYD,QAAQC,aAAa,CAAC;AACxC,QAAMC,QAAQF,QAAQE;AACtB,QAAM;IAAEC;IAAMC;IAAW,GAAGC;EAAa,IAAIJ;AAC7C,QAAMK,eAAe,CAACC,OAAOC,KAAKH,YAAY,EAAEI;AAEhD,QAAM;IAAEC;IAASC;EAAQ,IAAI,OAAOT,UAAU,WAC1CA,QACA;IACAQ,SAASR;IACTS,SAAS;MACPC,aAAYP,6CAAcQ,SAAQP;MAClCQ,gBAAeT,6CAAcU,SAAQT;MACrCU,YAAWX,6CAAcY,UAASX;MAClCY,UAASb,6CAAcc,QAAOb;IAChC;EACF;AAEF,QAAMc,WAAW,IAAIC,iBAAiB,WAGjC;AAAA,QAFHC,YAA2BC,UAAAd,SAAA,KAAAc,UAAA,CAAA,MAAAC,SAAAD,UAAA,CAAA,IAAG,CAAA;AAAE,QAChCH,YAA0BG,UAAAd,SAAA,IAAAc,UAAA,CAAA,IAAAC;AAE1Bd,uCAAUY,WAAWF;AAErB,QAAIjB,KAAMsB,WAAU1B,IAAIC,OAAO;EACjC,CAAC;AAED,MAAII,UAAWM,oCAAU,CAAA,GAAIU;AAE7BrB,KAAG2B,UAAUnB,OAAOR,GAAG2B,OAAO;AAC9B3B,KAAG2B,QAAS1B,QAAQ2B,SAAUC,EAAEC,GAAG,IAAI;IAAET;EAAS;AAElDA,WAASU,QAAQ/B,IAAIY,OAAO;AAC9B;AAEA,SAASc,UAAW1B,IAAiBC,SAAmC;AA5CxE;AA6CE,MAAI,GAACD,QAAG2B,YAAH3B,mBAAaC,QAAQ2B,SAAUC,EAAEC,MAAM;AAE5C9B,KAAG2B,QAAQ1B,QAAQ2B,SAAUC,EAAEC,GAAG,EAAGT,SAASW,WAAW;AACzD,SAAOhC,GAAG2B,QAAQ1B,QAAQ2B,SAAUC,EAAEC,GAAG;AAC3C;AAEO,IAAMG,SAAS;EACpBlC;EACA2B;AACF;;;AC3CA,SAASQ,SAASC,IAAiBC,SAAiC;AAXpE;AAYE,QAAMC,UAAUD,QAAQE;AACxB,QAAMC,UAAmC;IACvCC,SAAS,GAACJ,aAAQK,cAARL,mBAAmBM;EAC/B;AAEAC,SAAOC,iBAAiB,UAAUP,SAASE,OAAO;AAElDJ,KAAGU,YAAYC,OAAOX,GAAGU,SAAS;AAClCV,KAAGU,UAAWT,QAAQW,SAAUC,EAAEC,GAAG,IAAI;IACvCZ;IACAE;EACF;AAEA,MAAI,GAACH,aAAQK,cAARL,mBAAmBc,QAAO;AAC7Bb,YAAQ;EACV;AACF;AAEA,SAASc,WAAWhB,IAAiBC,SAAiC;AA9BtE;AA+BE,MAAI,GAACD,QAAGU,cAAHV,mBAAeC,QAAQW,SAAUC,EAAEC,MAAM;AAE9C,QAAM;IAAEZ;IAASE;EAAQ,IAAIJ,GAAGU,UAAUT,QAAQW,SAAUC,EAAEC,GAAG;AAEjEN,SAAOS,oBAAoB,UAAUf,SAASE,OAAO;AAErD,SAAOJ,GAAGU,UAAUT,QAAQW,SAAUC,EAAEC,GAAG;AAC7C;AAEO,IAAMI,SAAS;EACpBnB,SAAAA;EACAiB,WAAAA;AACF;;;AC9BA,SAASG,SAASC,IAAiBC,SAAiC;AAClE,QAAM;IAAEC,OAAO;EAAM,IAAID,QAAQE,aAAa,CAAC;AAC/C,QAAMC,QAAQH,QAAQG;AACtB,QAAMC,UAAW,OAAOD,UAAU,YAAYA,MAAMC,WAAY;IAAEC,SAAS;EAAK;AAChF,QAAMC,UAAU,OAAOH,UAAU,cAAc,iBAAiBA,QAAQA,QAAQA,MAAMG;AAEtF,QAAMC,SAASN,OACXF,KACAC,QAAQQ,MACNC,SAASC,cAAcV,QAAQQ,GAAG,IAClCG;AAEN,MAAI,CAACJ,OAAQ;AAEbA,SAAOK,iBAAiB,UAAUN,SAASF,OAAO;AAElDL,KAAGc,YAAYC,OAAOf,GAAGc,SAAS;AAClCd,KAAGc,UAAWb,QAAQe,SAAUC,EAAEC,GAAG,IAAI;IACvCX;IACAF;;IAEAG,QAAQN,OAAOiB,SAAYX;EAC7B;AACF;AAEA,SAASY,WAAWpB,IAAiBC,SAAiC;AAtCtE;AAuCE,MAAI,GAACD,QAAGc,cAAHd,mBAAeC,QAAQe,SAAUC,EAAEC,MAAM;AAE9C,QAAM;IAAEX;IAASF;IAASG,SAASR;EAAG,IAAIA,GAAGc,UAAUb,QAAQe,SAAUC,EAAEC,GAAG;AAE9EV,SAAOa,oBAAoB,UAAUd,SAASF,OAAO;AACrD,SAAOL,GAAGc,UAAUb,QAAQe,SAAUC,EAAEC,GAAG;AAC7C;AAEA,SAASI,QAAStB,IAAiBC,SAAiC;AAClE,MAAIA,QAAQG,UAAUH,QAAQsB,SAAU;AAExCH,EAAAA,WAAUpB,IAAIC,OAAO;AACrBF,EAAAA,SAAQC,IAAIC,OAAO;AACrB;AAEO,IAAMuB,SAAS;EACpBzB,SAAAA;EACAqB,WAAAA;EACAE;AACF;;;ACZO,SAASG,sBACdC,WACAC,OACmC;AACnC,QAAMC,oBAAqB,OAAOF,cAAc,WAC5CG,iBAAiBH,SAAS,IAC1BA;AAEJ,QAAMI,OAAOC,eAAeH,mBAAmBD,KAAK;AAEpD,SAAO;IACLK,SAASF;IACTG,SAASH;IACTI,UAAWC,IAAiB;AAC1BC,aAAO,MAAMD,EAAE;IACjB;EACF;AACF;AAEA,SAASJ,eAAgBL,WAA8BC,OAAoF;AACzI,SAAO,SAAUQ,IAAiBE,SAA2BC,OAAc;AAlE7E;AAmEI,UAAMC,SAAS,OAAOZ,UAAU,aAAaA,MAAMU,OAAO,IAAIV;AAC9D,UAAMa,SAAOH,aAAQI,UAARJ,mBAAeG,SAAQH,QAAQI,UAASF,iCAAQC;AAC7D,UAAMC,QAAQC,SAASL,QAAQI,KAAK,IAAIJ,QAAQI,QAAQ,CAAC;AAGzD,UAAME,WAAWA,MAAMH,QAAQL,GAAGS;AAIlC,UAAMC,YAAYP,MAAMQ,QAAQT,QAAQU,SAAUC,KAC9CC,yBAAoBX,OAAOD,QAAQU,SAAUC,CAAC,MAA9CC,mBAAiDJ,YACjDP,WAAMQ,QAANR,mBAAWO,aAAaR,QAAQU,SAAUC,EAAEH;AAEhD,UAAMK,OAAOC,EAAEzB,WAAW0B,WAAWb,QAAQE,KAAK,GAAGE,QAAQ;AAC7DO,SAAKG,aAAaC,OAAOC,OACvBD,uBAAOE,OAAO,IAAI,GACjBnB,QAAQU,SAAqCC,EAAEK,YAChD;MAAER;IAAS,CACb;AAEAT,WAAOc,MAAMf,EAAE;EACjB;AACF;AAEA,SAASc,oBAAqBX,OAAcmB,MAAmE;AAE7G,QAAMC,QAAQ,oBAAIC,IAAW;AAC7B,QAAMC,OAAQjB,cAA+B;AA9F/C;AA+FI,eAAWkB,SAASlB,UAAU;AAC5B,UAAI,CAACkB,MAAO;AAEZ,UAAIA,UAAUvB,SAAUuB,MAAM1B,MAAMG,MAAMH,MAAM0B,MAAM1B,OAAOG,MAAMH,IAAK;AACtE,eAAO;MACT;AAEAuB,YAAMI,IAAID,KAAK;AACf,UAAIE;AACJ,UAAIF,MAAMG,UAAU;AAClBD,QAAAA,UAASH,KAAK,CAACC,MAAMI,SAAS,CAAE;MAClC,WAAWC,MAAMC,QAAQN,MAAMlB,QAAQ,GAAG;AACxCoB,QAAAA,UAASH,KAAKC,MAAMlB,QAAmB;MACzC,YAAWkB,WAAMnC,cAANmC,mBAAiBvB,OAAO;AACjCyB,QAAAA,UAASH,KAAK,EAACC,WAAMnC,cAANmC,mBAAiBO,OAAO,CAAC;MAC1C;AACA,UAAIL,SAAQ;AACV,eAAOA;MACT;AACAL,YAAMW,OAAOR,KAAK;IACpB;AAEA,WAAO;EACT;AACA,MAAI,CAACD,KAAK,CAACH,KAAKW,OAAO,CAAC,GAAG;AACzBE,iBAAa,oEAAoE;AACjF,WAAOb;EACT;AAGA,QAAMM,SAASG,MAAMK,KAAKb,KAAK,EAAEc,QAAQ;AACzC,aAAWX,SAASE,QAAQ;AAC1B,QAAIF,MAAMnC,WAAW;AACnB,aAAOmC,MAAMnC;IACf;EACF;AACA,SAAO+B;AACT;;;ACrHO,IAAMgB,UAAUC,sBAA+CC,UAAUC,aAAW;AAf3F;AAgBE,SAAO;IACLC,WAAW;IACXC,WAAUF,aAAQG,QAARH,mBAAaI,QAAQ,KAAK;IACpCC,MAAM,OAAOL,QAAQM,UAAU,YAAYC,SAAYP,QAAQM;EACjE;AACF,CAAC;", "names": ["mounted", "el", "binding", "modifiers", "value", "once", "immediate", "modifierKeys", "defaultValue", "Object", "keys", "length", "handler", "options", "attributes", "attr", "characterData", "char", "childList", "child", "subtree", "sub", "observer", "MutationObserver", "mutations", "arguments", "undefined", "unmounted", "_mutate", "instance", "$", "uid", "observe", "disconnect", "Mutate", "mounted", "el", "binding", "handler", "value", "options", "passive", "modifiers", "active", "window", "addEventListener", "_onResize", "Object", "instance", "$", "uid", "quiet", "unmounted", "removeEventListener", "Resize", "mounted", "el", "binding", "self", "modifiers", "value", "options", "passive", "handler", "target", "arg", "document", "querySelector", "window", "addEventListener", "_onScroll", "Object", "instance", "$", "uid", "undefined", "unmounted", "removeEventListener", "updated", "oldValue", "<PERSON><PERSON>", "useDirectiveComponent", "component", "props", "concreteComponent", "resolveComponent", "hook", "mountComponent", "mounted", "updated", "unmounted", "el", "render", "binding", "vnode", "_props", "text", "value", "isObject", "children", "textContent", "provides", "ctx", "instance", "$", "findComponentParent", "node", "h", "mergeProps", "appContext", "Object", "assign", "create", "root", "stack", "Set", "walk", "child", "add", "result", "suspense", "ss<PERSON><PERSON><PERSON>", "Array", "isArray", "subTree", "delete", "consoleError", "from", "reverse", "<PERSON><PERSON><PERSON>", "useDirectiveComponent", "VTooltip", "binding", "activator", "location", "arg", "replace", "text", "value", "undefined"]}