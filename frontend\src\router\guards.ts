import type { Router } from 'vue-router'
import { useAuthStore } from '../stores/auth'

export function setupRouterGuards(router: Router) {
  // 全局前置守卫
  router.beforeEach((to, from, next) => {
    const authStore = useAuthStore()
    
    // 检查是否需要认证
    if (to.meta.requiresAuth && !authStore.isLoggedIn) {
      // 保存目标路由，登录后跳转
      next({
        path: '/login',
        query: { redirect: to.fullPath }
      })
      return
    }
    
    // 检查管理员权限
    if (to.meta.requiresAdmin && !authStore.isAdmin) {
      next({ path: '/' })
      return
    }
    
    // 检查审核员权限
    if (to.meta.requiresModerator && !authStore.isModerator) {
      next({ path: '/' })
      return
    }
    
    next()
  })
  
  // 全局后置钩子
  router.afterEach((to) => {
    // 设置页面标题
    document.title = to.meta.title ? `${to.meta.title} - 爱生网` : '爱生网'
  })
}
