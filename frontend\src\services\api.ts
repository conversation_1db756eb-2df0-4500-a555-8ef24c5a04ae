import axios from 'axios'

const API_BASE_URL = 'http://localhost:3000/api'

// 创建axios实例
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
})

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    return response
  },
  (error) => {
    if (error.response?.status === 401) {
      // Token过期或无效，清除本地存储并跳转到登录页
      localStorage.removeItem('token')
      window.location.href = '/login'
    }
    return Promise.reject(error)
  }
)

// 新闻相关API
export const newsApi = {
  getList: (params?: any) => api.get('/news', { params }),
  getDetail: (id: number) => api.get(`/news/${id}`),
  getTop: (limit?: number) => api.get('/news/top', { params: { limit } }),
  getCategories: () => api.get('/news/categories'),
  create: (data: any) => api.post('/news', data),
  update: (id: number, data: any) => api.put(`/news/${id}`, data),
  delete: (id: number) => api.delete(`/news/${id}`),
}

// 文章相关API
export const articlesApi = {
  getList: (params?: any) => api.get('/articles', { params }),
  getDetail: (id: number) => api.get(`/articles/${id}`),
  getCategories: () => api.get('/articles/categories'),
  create: (data: any) => api.post('/articles', data),
  update: (id: number, data: any) => api.put(`/articles/${id}`, data),
  delete: (id: number) => api.delete(`/articles/${id}`),
  like: (id: number) => api.post(`/articles/${id}/like`),
  getPending: (params?: any) => api.get('/articles/admin/pending', { params }),
  review: (id: number, data: any) => api.post(`/articles/${id}/review`, data),
}

// 用户相关API
export const usersApi = {
  getList: (params?: any) => api.get('/users', { params }),
  getDetail: (id: number) => api.get(`/users/${id}`),
  update: (id: number, data: any) => api.put(`/users/${id}`, data),
  delete: (id: number) => api.delete(`/users/${id}`),
}

// 认证相关API
export const authApi = {
  login: (data: any) => api.post('/auth/login', data),
  register: (data: any) => api.post('/auth/register', data),
  getCurrentUser: () => api.get('/auth/me'),
  updateProfile: (data: any) => api.put('/auth/profile', data),
}

export default api
