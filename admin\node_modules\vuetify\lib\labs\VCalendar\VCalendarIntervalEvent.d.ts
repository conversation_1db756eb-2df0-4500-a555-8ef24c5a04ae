export type VCalendarIntervalEventSlots = {
    intervalEvent: {
        height: string;
        margin: string;
        eventClass: string;
        event: any;
        interval: any;
    };
};
export declare const makeVCalendarIntervalEventProps: <Defaults extends {
    allDay?: unknown;
    interval?: unknown;
    intervalDivisions?: unknown;
    intervalDuration?: unknown;
    intervalHeight?: unknown;
    event?: unknown;
} = {}>(defaults?: Defaults | undefined) => {
    allDay: unknown extends Defaults["allDay"] ? BooleanConstructor : {
        type: import("vue").PropType<unknown extends Defaults["allDay"] ? boolean : boolean | Defaults["allDay"]>;
        default: unknown extends Defaults["allDay"] ? boolean : boolean | Defaults["allDay"];
    };
    interval: unknown extends Defaults["interval"] ? ObjectConstructor : {
        type: import("vue").PropType<unknown extends Defaults["interval"] ? Record<string, any> : Record<string, any> | Defaults["interval"]>;
        default: unknown extends Defaults["interval"] ? Record<string, any> : Record<string, any> | Defaults["interval"];
    };
    intervalDivisions: unknown extends Defaults["intervalDivisions"] ? {
        type: NumberConstructor;
        required: true;
    } : Omit<{
        type: NumberConstructor;
        required: true;
    }, "type" | "default"> & {
        type: import("vue").PropType<unknown extends Defaults["intervalDivisions"] ? number : number | Defaults["intervalDivisions"]>;
        default: unknown extends Defaults["intervalDivisions"] ? number : number | Defaults["intervalDivisions"];
    };
    intervalDuration: unknown extends Defaults["intervalDuration"] ? {
        type: NumberConstructor;
        required: true;
    } : Omit<{
        type: NumberConstructor;
        required: true;
    }, "type" | "default"> & {
        type: import("vue").PropType<unknown extends Defaults["intervalDuration"] ? number : number | Defaults["intervalDuration"]>;
        default: unknown extends Defaults["intervalDuration"] ? number : number | Defaults["intervalDuration"];
    };
    intervalHeight: unknown extends Defaults["intervalHeight"] ? {
        type: NumberConstructor;
        required: true;
    } : Omit<{
        type: NumberConstructor;
        required: true;
    }, "type" | "default"> & {
        type: import("vue").PropType<unknown extends Defaults["intervalHeight"] ? number : number | Defaults["intervalHeight"]>;
        default: unknown extends Defaults["intervalHeight"] ? number : number | Defaults["intervalHeight"];
    };
    event: unknown extends Defaults["event"] ? ObjectConstructor : {
        type: import("vue").PropType<unknown extends Defaults["event"] ? Record<string, any> : Record<string, any> | Defaults["event"]>;
        default: unknown extends Defaults["event"] ? Record<string, any> : Record<string, any> | Defaults["event"];
    };
};
export declare const VCalendarIntervalEvent: {
    new (...args: any[]): import("vue").CreateComponentPublicInstanceWithMixins<{
        allDay: boolean;
        intervalDivisions: number;
        intervalDuration: number;
        intervalHeight: number;
    } & {
        interval?: Record<string, any> | undefined;
        event?: Record<string, any> | undefined;
    } & {
        $children?: {} | import("vue").VNodeChild | {
            intervalEvent?: ((arg: {
                height: string;
                margin: string;
                eventClass: string;
                event: any;
                interval: any;
            }) => import("vue").VNodeChild) | undefined;
        };
        'v-slots'?: {
            intervalEvent?: false | ((arg: {
                height: string;
                margin: string;
                eventClass: string;
                event: any;
                interval: any;
            }) => import("vue").VNodeChild) | undefined;
        } | undefined;
    } & {
        "v-slot:intervalEvent"?: false | ((arg: {
            height: string;
            margin: string;
            eventClass: string;
            event: any;
            interval: any;
        }) => import("vue").VNodeChild) | undefined;
    }, {}, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, {
        allDay: boolean;
    }, true, {}, import("vue").SlotsType<Partial<{
        intervalEvent: (arg: {
            height: string;
            margin: string;
            eventClass: string;
            event: any;
            interval: any;
        }) => import("vue").VNode[];
    }>>, import("vue").GlobalComponents, import("vue").GlobalDirectives, string, {}, any, import("vue").ComponentProvideOptions, {
        P: {};
        B: {};
        D: {};
        C: {};
        M: {};
        Defaults: {};
    }, {
        allDay: boolean;
        intervalDivisions: number;
        intervalDuration: number;
        intervalHeight: number;
    } & {
        interval?: Record<string, any> | undefined;
        event?: Record<string, any> | undefined;
    } & {
        $children?: {} | import("vue").VNodeChild | {
            intervalEvent?: ((arg: {
                height: string;
                margin: string;
                eventClass: string;
                event: any;
                interval: any;
            }) => import("vue").VNodeChild) | undefined;
        };
        'v-slots'?: {
            intervalEvent?: false | ((arg: {
                height: string;
                margin: string;
                eventClass: string;
                event: any;
                interval: any;
            }) => import("vue").VNodeChild) | undefined;
        } | undefined;
    } & {
        "v-slot:intervalEvent"?: false | ((arg: {
            height: string;
            margin: string;
            eventClass: string;
            event: any;
            interval: any;
        }) => import("vue").VNodeChild) | undefined;
    }, {}, {}, {}, {}, {
        allDay: boolean;
    }>;
    __isFragment?: never;
    __isTeleport?: never;
    __isSuspense?: never;
} & import("vue").ComponentOptionsBase<{
    allDay: boolean;
    intervalDivisions: number;
    intervalDuration: number;
    intervalHeight: number;
} & {
    interval?: Record<string, any> | undefined;
    event?: Record<string, any> | undefined;
} & {
    $children?: {} | import("vue").VNodeChild | {
        intervalEvent?: ((arg: {
            height: string;
            margin: string;
            eventClass: string;
            event: any;
            interval: any;
        }) => import("vue").VNodeChild) | undefined;
    };
    'v-slots'?: {
        intervalEvent?: false | ((arg: {
            height: string;
            margin: string;
            eventClass: string;
            event: any;
            interval: any;
        }) => import("vue").VNodeChild) | undefined;
    } | undefined;
} & {
    "v-slot:intervalEvent"?: false | ((arg: {
        height: string;
        margin: string;
        eventClass: string;
        event: any;
        interval: any;
    }) => import("vue").VNodeChild) | undefined;
}, {}, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, {
    allDay: boolean;
}, {}, string, import("vue").SlotsType<Partial<{
    intervalEvent: (arg: {
        height: string;
        margin: string;
        eventClass: string;
        event: any;
        interval: any;
    }) => import("vue").VNode[];
}>>, import("vue").GlobalComponents, import("vue").GlobalDirectives, string, import("vue").ComponentProvideOptions> & import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps & import("../../util/index.js").FilterPropsOptions<{
    allDay: BooleanConstructor;
    interval: ObjectConstructor;
    intervalDivisions: {
        type: NumberConstructor;
        required: true;
    };
    intervalDuration: {
        type: NumberConstructor;
        required: true;
    };
    intervalHeight: {
        type: NumberConstructor;
        required: true;
    };
    event: ObjectConstructor;
}, import("vue").ExtractPropTypes<{
    allDay: BooleanConstructor;
    interval: ObjectConstructor;
    intervalDivisions: {
        type: NumberConstructor;
        required: true;
    };
    intervalDuration: {
        type: NumberConstructor;
        required: true;
    };
    intervalHeight: {
        type: NumberConstructor;
        required: true;
    };
    event: ObjectConstructor;
}>>;
export type VCalendarIntervalEvent = InstanceType<typeof VCalendarIntervalEvent>;
