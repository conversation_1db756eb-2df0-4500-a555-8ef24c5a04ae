<template>
  <div>
    <!-- 页面标题 -->
    <v-row class="mb-6">
      <v-col cols="12">
        <h1 class="text-h3 mb-2">文章审核</h1>
        <p class="text-body-1 text-grey-darken-1">
          审核用户投稿的文章内容
        </p>
      </v-col>
    </v-row>

    <!-- 文章列表 -->
    <v-card>
      <v-card-title>
        <v-icon class="mr-2">mdi-file-check</v-icon>
        待审核文章列表
        <v-spacer></v-spacer>
        <v-btn @click="fetchArticles" icon variant="text">
          <v-icon>mdi-refresh</v-icon>
        </v-btn>
      </v-card-title>

      <v-data-table
        :headers="headers"
        :items="articles"
        :loading="loading"
        :items-per-page="10"
        class="elevation-1"
      >
        <template v-slot:item.title="{ item }">
          <div class="text-truncate" style="max-width: 200px;">
            {{ item.title }}
          </div>
        </template>

        <template v-slot:item.author_name="{ item }">
          <v-chip size="small" color="primary" variant="outlined">
            {{ item.author_name }}
          </v-chip>
        </template>

        <template v-slot:item.category_name="{ item }">
          <v-chip size="small" color="secondary" variant="outlined">
            {{ item.category_name || '未分类' }}
          </v-chip>
        </template>

        <template v-slot:item.created_at="{ item }">
          {{ formatDate(item.created_at) }}
        </template>

        <template v-slot:item.actions="{ item }">
          <v-btn
            @click="viewArticle(item)"
            color="info"
            size="small"
            variant="outlined"
            class="mr-2"
          >
            查看
          </v-btn>
          <v-btn
            @click="approveArticle(item)"
            color="success"
            size="small"
            variant="outlined"
            class="mr-2"
          >
            通过
          </v-btn>
          <v-btn
            @click="rejectArticle(item)"
            color="error"
            size="small"
            variant="outlined"
          >
            拒绝
          </v-btn>
        </template>
      </v-data-table>
    </v-card>

    <!-- 文章详情对话框 -->
    <v-dialog v-model="detailDialog" max-width="800px" scrollable>
      <v-card v-if="selectedArticle">
        <v-card-title>
          <span class="text-h5">{{ selectedArticle.title }}</span>
          <v-spacer></v-spacer>
          <v-btn @click="detailDialog = false" icon variant="text">
            <v-icon>mdi-close</v-icon>
          </v-btn>
        </v-card-title>

        <v-card-text>
          <div class="mb-4">
            <v-chip color="primary" variant="outlined" class="mr-2">
              作者: {{ selectedArticle.author_name }}
            </v-chip>
            <v-chip color="secondary" variant="outlined" class="mr-2">
              分类: {{ selectedArticle.category_name || '未分类' }}
            </v-chip>
            <v-chip color="info" variant="outlined">
              投稿时间: {{ formatDate(selectedArticle.created_at) }}
            </v-chip>
          </div>

          <div class="article-content" v-html="selectedArticle.content"></div>
        </v-card-text>

        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn
            @click="approveArticle(selectedArticle)"
            color="success"
            variant="outlined"
          >
            通过审核
          </v-btn>
          <v-btn
            @click="rejectArticle(selectedArticle)"
            color="error"
            variant="outlined"
          >
            拒绝审核
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- 拒绝原因对话框 -->
    <v-dialog v-model="rejectDialog" max-width="500px">
      <v-card>
        <v-card-title>拒绝审核</v-card-title>
        <v-card-text>
          <v-textarea
            v-model="rejectReason"
            label="拒绝原因"
            placeholder="请输入拒绝原因..."
            rows="4"
            variant="outlined"
            required
          ></v-textarea>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn @click="rejectDialog = false" variant="text">取消</v-btn>
          <v-btn @click="confirmReject" color="error" :loading="reviewing">确认拒绝</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { articlesApi } from '../services/api'

const loading = ref(true)
const reviewing = ref(false)
const articles = ref([])
const detailDialog = ref(false)
const rejectDialog = ref(false)
const selectedArticle = ref(null)
const rejectReason = ref('')

const headers = [
  { title: '标题', key: 'title', sortable: false },
  { title: '作者', key: 'author_name', sortable: false },
  { title: '分类', key: 'category_name', sortable: false },
  { title: '投稿时间', key: 'created_at', sortable: true },
  { title: '操作', key: 'actions', sortable: false },
]

const fetchArticles = async () => {
  loading.value = true
  try {
    const response = await articlesApi.getPending()
    articles.value = response.data.articles
  } catch (error) {
    console.error('获取待审核文章失败:', error)
  } finally {
    loading.value = false
  }
}

const viewArticle = (article) => {
  selectedArticle.value = article
  detailDialog.value = true
}

const approveArticle = async (article) => {
  reviewing.value = true
  try {
    await articlesApi.review(article.id, { status: 'published' })
    // 从列表中移除已审核的文章
    articles.value = articles.value.filter(a => a.id !== article.id)
    detailDialog.value = false
  } catch (error) {
    console.error('审核通过失败:', error)
  } finally {
    reviewing.value = false
  }
}

const rejectArticle = (article) => {
  selectedArticle.value = article
  rejectReason.value = ''
  rejectDialog.value = true
  detailDialog.value = false
}

const confirmReject = async () => {
  if (!rejectReason.value.trim()) {
    return
  }

  reviewing.value = true
  try {
    await articlesApi.review(selectedArticle.value.id, {
      status: 'rejected',
      reject_reason: rejectReason.value
    })
    // 从列表中移除已审核的文章
    articles.value = articles.value.filter(a => a.id !== selectedArticle.value.id)
    rejectDialog.value = false
  } catch (error) {
    console.error('审核拒绝失败:', error)
  } finally {
    reviewing.value = false
  }
}

const formatDate = (dateString) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

onMounted(() => {
  fetchArticles()
})
</script>

<style scoped>
.article-content {
  line-height: 1.6;
  max-height: 400px;
  overflow-y: auto;
}

.article-content :deep(p) {
  margin-bottom: 1rem;
}

.article-content :deep(img) {
  max-width: 100%;
  height: auto;
}
</style>
