const routes = [
  {
    path: '/',
    redirect: '/dashboard'
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('../views/LoginView.vue'),
    meta: { title: '管理员登录', layout: 'auth' }
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: () => import('../views/DashboardView.vue'),
    meta: { title: '仪表盘', requiresAuth: true }
  },
  {
    path: '/users',
    name: 'Users',
    component: () => import('../views/UsersView.vue'),
    meta: { title: '用户管理', requiresAuth: true, requiresAdmin: true }
  },
  {
    path: '/news',
    name: 'News',
    component: () => import('../views/NewsView.vue'),
    meta: { title: '新闻管理', requiresAuth: true, requiresModerator: true }
  },
  {
    path: '/articles',
    name: 'Articles',
    component: () => import('../views/ArticlesView.vue'),
    meta: { title: '文章管理', requiresAuth: true, requiresModerator: true }
  },
  {
    path: '/articles/pending',
    name: 'PendingArticles',
    component: () => import('../views/PendingArticlesView.vue'),
    meta: { title: '文章审核', requiresAuth: true, requiresModerator: true }
  },
  {
    path: '/videos',
    name: 'Videos',
    component: () => import('../views/VideosView.vue'),
    meta: { title: '视频管理', requiresAuth: true, requiresModerator: true }
  },
  {
    path: '/books',
    name: 'Books',
    component: () => import('../views/BooksView.vue'),
    meta: { title: '图书管理', requiresAuth: true, requiresModerator: true }
  },
  {
    path: '/products',
    name: 'Products',
    component: () => import('../views/ProductsView.vue'),
    meta: { title: '商品管理', requiresAuth: true, requiresAdmin: true }
  },
  {
    path: '/settings',
    name: 'Settings',
    component: () => import('../views/SettingsView.vue'),
    meta: { title: '系统设置', requiresAuth: true, requiresAdmin: true }
  }
]

export default routes
