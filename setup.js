const { initDatabase } = require('./database/init');
const mysql = require('mysql2/promise');
const path = require('path');
require('dotenv').config({ path: path.join(__dirname, 'backend/.env') });

async function testConnection() {
  try {
    console.log('� 尝试连接数据库...');
    console.log(`主机: ${process.env.DB_HOST}`);
    console.log(`端口: ${process.env.DB_PORT}`);
    console.log(`用户: ${process.env.DB_USER}`);
    console.log(`目标数据库: ${process.env.DB_NAME}`);

    const connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 3306,
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      // 先不指定数据库，测试基本连接
    });

    console.log('✅ MySQL服务器连接成功');

    // 检查目标数据库是否存在
    const dbName = process.env.DB_NAME || 'aisun';
    const [databases] = await connection.execute('SHOW DATABASES LIKE ?', [dbName]);

    if (databases.length > 0) {
      console.log(`✅ 数据库 '${dbName}' 已存在`);
    } else {
      console.log(`ℹ️  数据库 '${dbName}' 不存在，将会创建`);
    }

    await connection.end();
    return true;
  } catch (error) {
    console.error('❌ 数据库连接失败:', error.message);
    console.error('错误代码:', error.code);

    // 提供具体的错误建议
    if (error.code === 'ECONNREFUSED') {
      console.log('💡 建议: MySQL服务可能未启动');
    } else if (error.code === 'ER_ACCESS_DENIED_ERROR') {
      console.log('💡 建议: 用户名或密码错误');
    } else if (error.code === 'ENOTFOUND') {
      console.log('💡 建议: 主机地址无法解析');
    }

    return false;
  }
}

async function setup() {
  console.log('🚀 开始设置爱生网项目...\n');

  try {
    // 测试数据库连接
    const isConnected = await testConnection();

    if (!isConnected) {
      console.log('\n🔧 故障排除建议：');
      console.log('1. 确保MySQL服务正在运行');
      console.log('2. 检查backend/.env文件中的数据库配置');
      console.log('3. 确保数据库用户有连接权限');
      console.log('4. 检查防火墙设置');
      console.log('5. 如果使用远程数据库，确保允许远程连接');
      return;
    }

    // 初始化数据库
    console.log('\n📊 初始化数据库...');
    await initDatabase();

    console.log('\n✅ 项目设置完成！');
    console.log('\n📋 接下来的步骤：');
    console.log('1. 启动后端服务器: cd backend && npm run dev');
    console.log('2. 启动前端服务器: cd frontend && npm run dev');
    console.log('3. 启动管理后台: cd admin && npm run dev');
    console.log('\n🌐 访问地址：');
    console.log('- 用户端: http://localhost:5173');
    console.log('- 管理后台: http://localhost:5174');
    console.log('- API服务: http://localhost:3000');
    console.log('\n👤 默认管理员账户：');
    console.log('- 用户名: admin');
    console.log('- 密码: admin123');

  } catch (error) {
    console.error('❌ 设置失败:', error.message);
    console.error('详细错误:', error);
    console.log('\n🔧 故障排除：');
    console.log('1. 确保MySQL服务正在运行');
    console.log('2. 检查backend/.env文件中的数据库配置');
    console.log('3. 确保数据库用户有创建数据库和表的权限');
    console.log('4. 检查数据库用户密码是否正确');
  }
}

setup();
