const { initDatabase } = require('./database/init');
const { testConnection } = require('./backend/src/config/database');

async function setup() {
  console.log('🚀 开始设置爱生网项目...\n');
  
  try {
    // 测试数据库连接
    console.log('🔍 测试数据库连接...');
    const isConnected = await testConnection();
    
    if (!isConnected) {
      console.log('❌ 数据库连接失败，请检查以下配置：');
      console.log('1. MySQL服务是否已启动');
      console.log('2. 数据库连接配置是否正确 (backend/.env)');
      console.log('3. 数据库用户权限是否足够');
      return;
    }
    
    // 初始化数据库
    console.log('📊 初始化数据库...');
    await initDatabase();
    
    console.log('\n✅ 项目设置完成！');
    console.log('\n📋 接下来的步骤：');
    console.log('1. 启动后端服务器: cd backend && npm run dev');
    console.log('2. 启动前端服务器: cd frontend && npm run dev');
    console.log('3. 启动管理后台: cd admin && npm run dev');
    console.log('\n🌐 访问地址：');
    console.log('- 用户端: http://localhost:5173');
    console.log('- 管理后台: http://localhost:5174');
    console.log('- API服务: http://localhost:3000');
    console.log('\n👤 默认管理员账户：');
    console.log('- 用户名: admin');
    console.log('- 密码: admin123');
    
  } catch (error) {
    console.error('❌ 设置失败:', error.message);
    console.log('\n🔧 故障排除：');
    console.log('1. 确保MySQL服务正在运行');
    console.log('2. 检查backend/.env文件中的数据库配置');
    console.log('3. 确保数据库用户有创建数据库和表的权限');
  }
}

setup();
