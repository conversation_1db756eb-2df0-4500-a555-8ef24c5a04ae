import type { PropType } from 'vue';
export type Density = null | 'default' | 'comfortable' | 'compact';
export interface DensityProps {
    density?: Density;
}
export declare const makeDensityProps: <Defaults extends {
    density?: unknown;
} = {}>(defaults?: Defaults | undefined) => {
    density: unknown extends Defaults["density"] ? {
        type: PropType<Density>;
        default: string;
        validator: (v: any) => boolean;
    } : Omit<{
        type: PropType<Density>;
        default: string;
        validator: (v: any) => boolean;
    }, "type" | "default"> & {
        type: PropType<unknown extends Defaults["density"] ? Density : Density | Defaults["density"]>;
        default: unknown extends Defaults["density"] ? Density : Defaults["density"] | NonNullable<Density>;
    };
};
export declare function useDensity(props: DensityProps, name?: string): {
    densityClasses: Readonly<import("vue").Ref<string, string>>;
};
