{"version": 3, "file": "zh-Hans.js", "names": ["badge", "open", "close", "dismiss", "confirmEdit", "ok", "cancel", "dataIterator", "noResultsText", "loadingText", "dataTable", "itemsPerPageText", "aria<PERSON><PERSON><PERSON>", "sortDescending", "sortAscending", "sortNone", "activateNone", "activateDescending", "activateAscending", "sortBy", "dataFooter", "itemsPerPageAll", "nextPage", "prevPage", "firstPage", "lastPage", "pageText", "dateRangeInput", "divider", "datePicker", "itemsSelected", "range", "title", "header", "input", "placeholder", "noDataText", "carousel", "prev", "next", "delimiter", "calendar", "moreEvents", "today", "clear", "prependAction", "appendAction", "otp", "fileInput", "counter", "counterSize", "fileUpload", "browse", "timePicker", "am", "pm", "pagination", "root", "previous", "page", "currentPage", "first", "last", "stepper", "rating", "item", "loading", "infiniteScroll", "loadMore", "empty", "rules", "required", "email", "number", "integer", "capital", "max<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "strictLength", "exclude", "notEmpty", "pattern"], "sources": ["../../src/locale/zh-Hans.ts"], "sourcesContent": ["export default {\n  badge: '徽章',\n  open: '打开',\n  close: '关闭',\n  dismiss: '取消',\n  confirmEdit: {\n    ok: '确定',\n    cancel: '取消',\n  },\n  dataIterator: {\n    noResultsText: '没有符合条件的结果',\n    loadingText: '加载中……',\n  },\n  dataTable: {\n    itemsPerPageText: '每页数目：',\n    ariaLabel: {\n      sortDescending: '：降序排列。',\n      sortAscending: '：升序排列。',\n      sortNone: '：未排序。',\n      activateNone: '点击以移除排序。',\n      activateDescending: '点击以降序排列。',\n      activateAscending: '点击以升序排列。',\n    },\n    sortBy: '排序方式',\n  },\n  dataFooter: {\n    itemsPerPageText: '每页数目：',\n    itemsPerPageAll: '全部',\n    nextPage: '下一页',\n    prevPage: '上一页',\n    firstPage: '首页',\n    lastPage: '尾页',\n    pageText: '{0}-{1} 共 {2}',\n  },\n  dateRangeInput: {\n    divider: '至',\n  },\n  datePicker: {\n    itemsSelected: '已选择 {0} 项',\n    range: {\n      title: '选择日期',\n      header: '输入日期',\n    },\n    title: '选择日期',\n    header: '输入日期',\n    input: {\n      placeholder: '输入日期',\n    },\n  },\n  noDataText: '没有数据',\n  carousel: {\n    prev: '上一张',\n    next: '下一张',\n    ariaLabel: {\n      delimiter: '幻灯片 {0} / {1}',\n    },\n  },\n  calendar: {\n    moreEvents: '还有 {0} 项',\n    today: '今天',\n  },\n  input: {\n    clear: '清除 {0}',\n    prependAction: '{0} 前置操作',\n    appendAction: '{0} 后置操作',\n    otp: '请输入第 {0} 位 OTP',\n  },\n  fileInput: {\n    counter: '{0} 个文件',\n    counterSize: '{0} 个文件（共 {1}）',\n  },\n  fileUpload: {\n    title: '拖放文件到此处',\n    divider: '或',\n    browse: '浏览文件',\n  },\n  timePicker: {\n    am: '上午',\n    pm: '下午',\n    title: '选择时间',\n  },\n  pagination: {\n    ariaLabel: {\n      root: '分页导航',\n      next: '下一页',\n      previous: '上一页',\n      page: '转到页面 {0}',\n      currentPage: '当前页 {0}',\n      first: '第一页',\n      last: '最后一页',\n    },\n  },\n  stepper: {\n    next: '下一步',\n    prev: '上一步',\n  },\n  rating: {\n    ariaLabel: {\n      item: '评分 {0} / {1}',\n    },\n  },\n  loading: '加载中...',\n  infiniteScroll: {\n    loadMore: '加载更多',\n    empty: '没有更多内容',\n  },\n  rules: {\n    required: '此字段为必填项',\n    email: '请输入有效的电子邮件地址',\n    number: '此字段只能包含数字',\n    integer: '此字段只能包含整数',\n    capital: '此字段只能包含大写字母',\n    maxLength: '您最多可以输入{0}个字符',\n    minLength: '您必须至少输入{0}个字符',\n    strictLength: '输入字段的长度无效',\n    exclude: '字符{0}是不允许的',\n    notEmpty: '请至少选择一个值',\n    pattern: '格式无效',\n  },\n}\n"], "mappings": "AAAA,eAAe;EACbA,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE,IAAI;EACVC,KAAK,EAAE,IAAI;EACXC,OAAO,EAAE,IAAI;EACbC,WAAW,EAAE;IACXC,EAAE,EAAE,IAAI;IACRC,MAAM,EAAE;EACV,CAAC;EACDC,YAAY,EAAE;IACZC,aAAa,EAAE,WAAW;IAC1BC,WAAW,EAAE;EACf,CAAC;EACDC,SAAS,EAAE;IACTC,gBAAgB,EAAE,OAAO;IACzBC,SAAS,EAAE;MACTC,cAAc,EAAE,QAAQ;MACxBC,aAAa,EAAE,QAAQ;MACvBC,QAAQ,EAAE,OAAO;MACjBC,YAAY,EAAE,UAAU;MACxBC,kBAAkB,EAAE,UAAU;MAC9BC,iBAAiB,EAAE;IACrB,CAAC;IACDC,MAAM,EAAE;EACV,CAAC;EACDC,UAAU,EAAE;IACVT,gBAAgB,EAAE,OAAO;IACzBU,eAAe,EAAE,IAAI;IACrBC,QAAQ,EAAE,KAAK;IACfC,QAAQ,EAAE,KAAK;IACfC,SAAS,EAAE,IAAI;IACfC,QAAQ,EAAE,IAAI;IACdC,QAAQ,EAAE;EACZ,CAAC;EACDC,cAAc,EAAE;IACdC,OAAO,EAAE;EACX,CAAC;EACDC,UAAU,EAAE;IACVC,aAAa,EAAE,WAAW;IAC1BC,KAAK,EAAE;MACLC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE;IACV,CAAC;IACDD,KAAK,EAAE,MAAM;IACbC,MAAM,EAAE,MAAM;IACdC,KAAK,EAAE;MACLC,WAAW,EAAE;IACf;EACF,CAAC;EACDC,UAAU,EAAE,MAAM;EAClBC,QAAQ,EAAE;IACRC,IAAI,EAAE,KAAK;IACXC,IAAI,EAAE,KAAK;IACX3B,SAAS,EAAE;MACT4B,SAAS,EAAE;IACb;EACF,CAAC;EACDC,QAAQ,EAAE;IACRC,UAAU,EAAE,UAAU;IACtBC,KAAK,EAAE;EACT,CAAC;EACDT,KAAK,EAAE;IACLU,KAAK,EAAE,QAAQ;IACfC,aAAa,EAAE,UAAU;IACzBC,YAAY,EAAE,UAAU;IACxBC,GAAG,EAAE;EACP,CAAC;EACDC,SAAS,EAAE;IACTC,OAAO,EAAE,SAAS;IAClBC,WAAW,EAAE;EACf,CAAC;EACDC,UAAU,EAAE;IACVnB,KAAK,EAAE,SAAS;IAChBJ,OAAO,EAAE,GAAG;IACZwB,MAAM,EAAE;EACV,CAAC;EACDC,UAAU,EAAE;IACVC,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRvB,KAAK,EAAE;EACT,CAAC;EACDwB,UAAU,EAAE;IACV5C,SAAS,EAAE;MACT6C,IAAI,EAAE,MAAM;MACZlB,IAAI,EAAE,KAAK;MACXmB,QAAQ,EAAE,KAAK;MACfC,IAAI,EAAE,UAAU;MAChBC,WAAW,EAAE,SAAS;MACtBC,KAAK,EAAE,KAAK;MACZC,IAAI,EAAE;IACR;EACF,CAAC;EACDC,OAAO,EAAE;IACPxB,IAAI,EAAE,KAAK;IACXD,IAAI,EAAE;EACR,CAAC;EACD0B,MAAM,EAAE;IACNpD,SAAS,EAAE;MACTqD,IAAI,EAAE;IACR;EACF,CAAC;EACDC,OAAO,EAAE,QAAQ;EACjBC,cAAc,EAAE;IACdC,QAAQ,EAAE,MAAM;IAChBC,KAAK,EAAE;EACT,CAAC;EACDC,KAAK,EAAE;IACLC,QAAQ,EAAE,SAAS;IACnBC,KAAK,EAAE,cAAc;IACrBC,MAAM,EAAE,WAAW;IACnBC,OAAO,EAAE,WAAW;IACpBC,OAAO,EAAE,aAAa;IACtBC,SAAS,EAAE,eAAe;IAC1BC,SAAS,EAAE,eAAe;IAC1BC,YAAY,EAAE,WAAW;IACzBC,OAAO,EAAE,YAAY;IACrBC,QAAQ,EAAE,UAAU;IACpBC,OAAO,EAAE;EACX;AACF,CAAC", "ignoreList": []}