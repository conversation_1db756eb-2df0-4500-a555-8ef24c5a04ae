@echo off
echo 启动爱生网项目...

echo.
echo 启动后端服务器...
start "Backend Server" cmd /k "cd backend && npm run dev"

timeout /t 3 /nobreak >nul

echo 启动前端服务器...
start "Frontend Server" cmd /k "cd frontend && npm run dev"

timeout /t 3 /nobreak >nul

echo 启动管理后台...
start "Admin Panel" cmd /k "cd admin && npm run dev"

echo.
echo 所有服务已启动！
echo.
echo 访问地址：
echo 用户端: http://localhost:5173
echo 管理后台: http://localhost:5174
echo API服务: http://localhost:3000
echo.
echo 按任意键退出...
pause >nul
