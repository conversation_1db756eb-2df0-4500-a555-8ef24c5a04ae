import type { ComponentInternalInstance, InjectionKey, PropType, Raw, Ref } from 'vue';
import type { ValidationProps } from './validation.js';
import type { EventProp } from "../util/index.js";
export interface FormProvide {
    register: (item: {
        id: number | string;
        vm: ComponentInternalInstance;
        validate: () => Promise<string[]>;
        reset: () => Promise<void>;
        resetValidation: () => Promise<void>;
    }) => void;
    unregister: (id: number | string) => void;
    update: (id: number | string, isValid: boolean | null, errorMessages: string[]) => void;
    items: Ref<FormField[]>;
    isDisabled: Readonly<Ref<boolean>>;
    isReadonly: Readonly<Ref<boolean>>;
    isValidating: Ref<boolean>;
    isValid: Ref<boolean | null>;
    validateOn: Ref<FormProps['validateOn']>;
}
export interface FormField {
    id: number | string;
    validate: () => Promise<string[]>;
    reset: () => Promise<void>;
    resetValidation: () => Promise<void>;
    vm: Raw<ComponentInternalInstance>;
    isValid: boolean | null;
    errorMessages: string[];
}
export interface FieldValidationResult {
    id: number | string;
    errorMessages: string[];
}
export interface FormValidationResult {
    valid: boolean;
    errors: FieldValidationResult[];
}
export interface SubmitEventPromise extends SubmitEvent, Promise<FormValidationResult> {
}
export declare const FormKey: InjectionKey<FormProvide>;
export interface FormProps {
    disabled: boolean;
    fastFail: boolean;
    readonly: boolean;
    modelValue: boolean | null;
    'onUpdate:modelValue': EventProp<[boolean | null]> | undefined;
    validateOn: ValidationProps['validateOn'];
}
export declare const makeFormProps: <Defaults extends {
    disabled?: unknown;
    fastFail?: unknown;
    readonly?: unknown;
    modelValue?: unknown;
    validateOn?: unknown;
} = {}>(defaults?: Defaults | undefined) => {
    disabled: unknown extends Defaults["disabled"] ? BooleanConstructor : {
        type: PropType<unknown extends Defaults["disabled"] ? boolean : boolean | Defaults["disabled"]>;
        default: unknown extends Defaults["disabled"] ? boolean : boolean | Defaults["disabled"];
    };
    fastFail: unknown extends Defaults["fastFail"] ? BooleanConstructor : {
        type: PropType<unknown extends Defaults["fastFail"] ? boolean : boolean | Defaults["fastFail"]>;
        default: unknown extends Defaults["fastFail"] ? boolean : boolean | Defaults["fastFail"];
    };
    readonly: unknown extends Defaults["readonly"] ? BooleanConstructor : {
        type: PropType<unknown extends Defaults["readonly"] ? boolean : boolean | Defaults["readonly"]>;
        default: unknown extends Defaults["readonly"] ? boolean : boolean | Defaults["readonly"];
    };
    modelValue: unknown extends Defaults["modelValue"] ? {
        type: PropType<boolean | null>;
        default: null;
    } : Omit<{
        type: PropType<boolean | null>;
        default: null;
    }, "type" | "default"> & {
        type: PropType<unknown extends Defaults["modelValue"] ? boolean | null : boolean | Defaults["modelValue"] | null>;
        default: unknown extends Defaults["modelValue"] ? boolean | null : NonNullable<boolean | null> | Defaults["modelValue"];
    };
    validateOn: unknown extends Defaults["validateOn"] ? {
        type: PropType<FormProps["validateOn"]>;
        default: string;
    } : Omit<{
        type: PropType<FormProps["validateOn"]>;
        default: string;
    }, "type" | "default"> & {
        type: PropType<unknown extends Defaults["validateOn"] ? ("eager" | "lazy" | ("input" | "blur" | "submit" | "invalid-input") | "input lazy" | "blur lazy" | "submit lazy" | "invalid-input lazy" | "input eager" | "blur eager" | "submit eager" | "invalid-input eager" | "lazy input" | "lazy blur" | "lazy submit" | "lazy invalid-input" | "eager input" | "eager blur" | "eager submit" | "eager invalid-input") | undefined : ("eager" | "lazy" | ("input" | "blur" | "submit" | "invalid-input") | "input lazy" | "blur lazy" | "submit lazy" | "invalid-input lazy" | "input eager" | "blur eager" | "submit eager" | "invalid-input eager" | "lazy input" | "lazy blur" | "lazy submit" | "lazy invalid-input" | "eager input" | "eager blur" | "eager submit" | "eager invalid-input") | Defaults["validateOn"] | undefined>;
        default: unknown extends Defaults["validateOn"] ? ("eager" | "lazy" | ("input" | "blur" | "submit" | "invalid-input") | "input lazy" | "blur lazy" | "submit lazy" | "invalid-input lazy" | "input eager" | "blur eager" | "submit eager" | "invalid-input eager" | "lazy input" | "lazy blur" | "lazy submit" | "lazy invalid-input" | "eager input" | "eager blur" | "eager submit" | "eager invalid-input") | undefined : NonNullable<("eager" | "lazy" | ("input" | "blur" | "submit" | "invalid-input") | "input lazy" | "blur lazy" | "submit lazy" | "invalid-input lazy" | "input eager" | "blur eager" | "submit eager" | "invalid-input eager" | "lazy input" | "lazy blur" | "lazy submit" | "lazy invalid-input" | "eager input" | "eager blur" | "eager submit" | "eager invalid-input") | undefined> | Defaults["validateOn"];
    };
};
export declare function createForm(props: FormProps): {
    errors: Ref<{
        id: number | string;
        errorMessages: string[];
    }[], FieldValidationResult[] | {
        id: number | string;
        errorMessages: string[];
    }[]>;
    isDisabled: Readonly<Ref<boolean, boolean>>;
    isReadonly: Readonly<Ref<boolean, boolean>>;
    isValidating: import("vue").ShallowRef<boolean, boolean>;
    isValid: Ref<boolean | null, boolean | null> & {
        readonly externalValue: boolean | null;
    };
    items: Ref<{
        id: number | string;
        validate: () => Promise<string[]>;
        reset: () => Promise<void>;
        resetValidation: () => Promise<void>;
        vm: Raw<ComponentInternalInstance>;
        isValid: boolean | null;
        errorMessages: string[];
    }[], FormField[] | {
        id: number | string;
        validate: () => Promise<string[]>;
        reset: () => Promise<void>;
        resetValidation: () => Promise<void>;
        vm: Raw<ComponentInternalInstance>;
        isValid: boolean | null;
        errorMessages: string[];
    }[]>;
    validate: () => Promise<{
        valid: boolean;
        errors: {
            id: number | string;
            errorMessages: string[];
        }[];
    }>;
    reset: () => void;
    resetValidation: () => void;
};
export declare function useForm(props?: {
    readonly: boolean | null;
    disabled: boolean | null;
}): {
    isReadonly: import("vue").ComputedRef<boolean>;
    isDisabled: import("vue").ComputedRef<boolean>;
    register?: ((item: {
        id: number | string;
        vm: ComponentInternalInstance;
        validate: () => Promise<string[]>;
        reset: () => Promise<void>;
        resetValidation: () => Promise<void>;
    }) => void) | undefined;
    unregister?: ((id: number | string) => void) | undefined;
    update?: ((id: number | string, isValid: boolean | null, errorMessages: string[]) => void) | undefined;
    items?: Ref<FormField[], FormField[]> | undefined;
    isValidating?: Ref<boolean, boolean> | undefined;
    isValid?: Ref<boolean | null, boolean | null> | undefined;
    validateOn?: Ref<("eager" | "lazy" | ("input" | "blur" | "submit" | "invalid-input") | "input lazy" | "blur lazy" | "submit lazy" | "invalid-input lazy" | "input eager" | "blur eager" | "submit eager" | "invalid-input eager" | "lazy input" | "lazy blur" | "lazy submit" | "lazy invalid-input" | "eager input" | "eager blur" | "eager submit" | "eager invalid-input") | undefined, ("eager" | "lazy" | ("input" | "blur" | "submit" | "invalid-input") | "input lazy" | "blur lazy" | "submit lazy" | "invalid-input lazy" | "input eager" | "blur eager" | "submit eager" | "invalid-input eager" | "lazy input" | "lazy blur" | "lazy submit" | "lazy invalid-input" | "eager input" | "eager blur" | "eager submit" | "eager invalid-input") | undefined> | undefined;
};
