import type { VCalendarIntervalSlots } from './VCalendarInterval.js';
export type VCalendarDaySlots = VCalendarIntervalSlots & {
    interval: Record<string, unknown>;
};
export declare const makeVCalendarDayProps: <Defaults extends {
    day?: unknown;
    dayIndex?: unknown;
    events?: unknown;
    intervalDivisions?: unknown;
    intervalDuration?: unknown;
    intervalHeight?: unknown;
    intervalFormat?: unknown;
    intervalStart?: unknown;
    hideDayHeader?: unknown;
    intervals?: unknown;
} = {}>(defaults?: Defaults | undefined) => {
    day: unknown extends Defaults["day"] ? {
        type: ObjectConstructor;
        default: () => {};
    } : Omit<{
        type: ObjectConstructor;
        default: () => {};
    }, "type" | "default"> & {
        type: import("vue").PropType<unknown extends Defaults["day"] ? Record<string, any> : Record<string, any> | Defaults["day"]>;
        default: unknown extends Defaults["day"] ? Record<string, any> : Record<string, any> | Defaults["day"];
    };
    dayIndex: unknown extends Defaults["dayIndex"] ? NumberConstructor : {
        type: import("vue").PropType<unknown extends Defaults["dayIndex"] ? number : number | Defaults["dayIndex"]>;
        default: unknown extends Defaults["dayIndex"] ? number : number | Defaults["dayIndex"];
    };
    events: unknown extends Defaults["events"] ? {
        (arrayLength: number): any[];
        (...items: any[]): any[];
        new (arrayLength: number): any[];
        new (...items: any[]): any[];
        isArray(arg: any): arg is any[];
        readonly prototype: any[];
        from<T>(arrayLike: ArrayLike<T>): T[];
        from<T, U>(arrayLike: ArrayLike<T>, mapfn: (v: T, k: number) => U, thisArg?: any): U[];
        from<T>(iterable: Iterable<T> | ArrayLike<T>): T[];
        from<T, U_1>(iterable: Iterable<T> | ArrayLike<T>, mapfn: (v: T, k: number) => U_1, thisArg?: any): U_1[];
        of<T>(...items: T[]): T[];
        fromAsync<T>(iterableOrArrayLike: AsyncIterable<T> | Iterable<T | PromiseLike<T>> | ArrayLike<T | PromiseLike<T>>): Promise<T[]>;
        fromAsync<T, U_2>(iterableOrArrayLike: AsyncIterable<T> | Iterable<T> | ArrayLike<T>, mapFn: (value: Awaited<T>, index: number) => U_2, thisArg?: any): Promise<Awaited<U_2>[]>;
        readonly [Symbol.species]: ArrayConstructor;
    } : {
        type: import("vue").PropType<unknown extends Defaults["events"] ? any[] : any[] | Defaults["events"]>;
        default: unknown extends Defaults["events"] ? any[] : any[] | Defaults["events"];
    };
    intervalDivisions: unknown extends Defaults["intervalDivisions"] ? {
        type: NumberConstructor;
        default: number;
    } : Omit<{
        type: NumberConstructor;
        default: number;
    }, "type" | "default"> & {
        type: import("vue").PropType<unknown extends Defaults["intervalDivisions"] ? number : number | Defaults["intervalDivisions"]>;
        default: unknown extends Defaults["intervalDivisions"] ? number : number | Defaults["intervalDivisions"];
    };
    intervalDuration: unknown extends Defaults["intervalDuration"] ? {
        type: NumberConstructor;
        default: number;
    } : Omit<{
        type: NumberConstructor;
        default: number;
    }, "type" | "default"> & {
        type: import("vue").PropType<unknown extends Defaults["intervalDuration"] ? number : number | Defaults["intervalDuration"]>;
        default: unknown extends Defaults["intervalDuration"] ? number : number | Defaults["intervalDuration"];
    };
    intervalHeight: unknown extends Defaults["intervalHeight"] ? {
        type: NumberConstructor;
        default: number;
    } : Omit<{
        type: NumberConstructor;
        default: number;
    }, "type" | "default"> & {
        type: import("vue").PropType<unknown extends Defaults["intervalHeight"] ? number : number | Defaults["intervalHeight"]>;
        default: unknown extends Defaults["intervalHeight"] ? number : number | Defaults["intervalHeight"];
    };
    intervalFormat: unknown extends Defaults["intervalFormat"] ? {
        type: (FunctionConstructor | StringConstructor)[];
        default: string;
    } : Omit<{
        type: (FunctionConstructor | StringConstructor)[];
        default: string;
    }, "type" | "default"> & {
        type: import("vue").PropType<unknown extends Defaults["intervalFormat"] ? string | Function : string | Function | Defaults["intervalFormat"]>;
        default: unknown extends Defaults["intervalFormat"] ? string | Function : NonNullable<string | Function> | Defaults["intervalFormat"];
    };
    intervalStart: unknown extends Defaults["intervalStart"] ? {
        type: NumberConstructor;
        default: number;
    } : Omit<{
        type: NumberConstructor;
        default: number;
    }, "type" | "default"> & {
        type: import("vue").PropType<unknown extends Defaults["intervalStart"] ? number : number | Defaults["intervalStart"]>;
        default: unknown extends Defaults["intervalStart"] ? number : number | Defaults["intervalStart"];
    };
    hideDayHeader: unknown extends Defaults["hideDayHeader"] ? BooleanConstructor : {
        type: import("vue").PropType<unknown extends Defaults["hideDayHeader"] ? boolean : boolean | Defaults["hideDayHeader"]>;
        default: unknown extends Defaults["hideDayHeader"] ? boolean : boolean | Defaults["hideDayHeader"];
    };
    intervals: unknown extends Defaults["intervals"] ? {
        type: NumberConstructor;
        default: number;
    } : Omit<{
        type: NumberConstructor;
        default: number;
    }, "type" | "default"> & {
        type: import("vue").PropType<unknown extends Defaults["intervals"] ? number : number | Defaults["intervals"]>;
        default: unknown extends Defaults["intervals"] ? number : number | Defaults["intervals"];
    };
};
export declare const VCalendarDay: {
    new (...args: any[]): import("vue").CreateComponentPublicInstanceWithMixins<{
        day: Record<string, any>;
        intervalDivisions: number;
        intervalDuration: number;
        intervalHeight: number;
        intervalFormat: string | Function;
        intervalStart: number;
        hideDayHeader: boolean;
        intervals: number;
    } & {
        events?: any[] | undefined;
        dayIndex?: number | undefined;
    } & {
        $children?: {} | import("vue").VNodeChild | {
            intervalBody?: ((arg: {
                interval: {
                    label: string;
                    start: unknown;
                    end: unknown;
                    events: any[];
                };
            }) => import("vue").VNodeChild) | undefined;
            intervalEvent?: ((arg: {
                height: string;
                margin: string;
                eventClass: string;
                event: any;
                interval: {
                    label: string;
                    start: unknown;
                    end: unknown;
                    events: any[];
                };
            }) => import("vue").VNodeChild) | undefined;
            intervalTitle?: ((arg: {
                interval: {
                    label: string;
                    start: unknown;
                    end: unknown;
                    events: any[];
                };
            }) => import("vue").VNodeChild) | undefined;
            interval?: ((arg: Record<string, unknown>) => import("vue").VNodeChild) | undefined;
        };
        'v-slots'?: {
            intervalBody?: false | ((arg: {
                interval: {
                    label: string;
                    start: unknown;
                    end: unknown;
                    events: any[];
                };
            }) => import("vue").VNodeChild) | undefined;
            intervalEvent?: false | ((arg: {
                height: string;
                margin: string;
                eventClass: string;
                event: any;
                interval: {
                    label: string;
                    start: unknown;
                    end: unknown;
                    events: any[];
                };
            }) => import("vue").VNodeChild) | undefined;
            intervalTitle?: false | ((arg: {
                interval: {
                    label: string;
                    start: unknown;
                    end: unknown;
                    events: any[];
                };
            }) => import("vue").VNodeChild) | undefined;
            interval?: false | ((arg: Record<string, unknown>) => import("vue").VNodeChild) | undefined;
        } | undefined;
    } & {
        "v-slot:intervalBody"?: false | ((arg: {
            interval: {
                label: string;
                start: unknown;
                end: unknown;
                events: any[];
            };
        }) => import("vue").VNodeChild) | undefined;
        "v-slot:intervalEvent"?: false | ((arg: {
            height: string;
            margin: string;
            eventClass: string;
            event: any;
            interval: {
                label: string;
                start: unknown;
                end: unknown;
                events: any[];
            };
        }) => import("vue").VNodeChild) | undefined;
        "v-slot:intervalTitle"?: false | ((arg: {
            interval: {
                label: string;
                start: unknown;
                end: unknown;
                events: any[];
            };
        }) => import("vue").VNodeChild) | undefined;
        "v-slot:interval"?: false | ((arg: Record<string, unknown>) => import("vue").VNodeChild) | undefined;
    }, {
        intervals: import("vue").ComputedRef<number[]>;
    }, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, {
        day: Record<string, any>;
        intervalDivisions: number;
        intervalDuration: number;
        intervalHeight: number;
        intervalFormat: string | Function;
        intervalStart: number;
        hideDayHeader: boolean;
        intervals: number;
    }, true, {}, import("vue").SlotsType<Partial<{
        intervalBody: (arg: {
            interval: {
                label: string;
                start: unknown;
                end: unknown;
                events: any[];
            };
        }) => import("vue").VNode[];
        intervalEvent: (arg: {
            height: string;
            margin: string;
            eventClass: string;
            event: any;
            interval: {
                label: string;
                start: unknown;
                end: unknown;
                events: any[];
            };
        }) => import("vue").VNode[];
        intervalTitle: (arg: {
            interval: {
                label: string;
                start: unknown;
                end: unknown;
                events: any[];
            };
        }) => import("vue").VNode[];
        interval: (arg: Record<string, unknown>) => import("vue").VNode[];
    }>>, import("vue").GlobalComponents, import("vue").GlobalDirectives, string, {}, any, import("vue").ComponentProvideOptions, {
        P: {};
        B: {};
        D: {};
        C: {};
        M: {};
        Defaults: {};
    }, {
        day: Record<string, any>;
        intervalDivisions: number;
        intervalDuration: number;
        intervalHeight: number;
        intervalFormat: string | Function;
        intervalStart: number;
        hideDayHeader: boolean;
        intervals: number;
    } & {
        events?: any[] | undefined;
        dayIndex?: number | undefined;
    } & {
        $children?: {} | import("vue").VNodeChild | {
            intervalBody?: ((arg: {
                interval: {
                    label: string;
                    start: unknown;
                    end: unknown;
                    events: any[];
                };
            }) => import("vue").VNodeChild) | undefined;
            intervalEvent?: ((arg: {
                height: string;
                margin: string;
                eventClass: string;
                event: any;
                interval: {
                    label: string;
                    start: unknown;
                    end: unknown;
                    events: any[];
                };
            }) => import("vue").VNodeChild) | undefined;
            intervalTitle?: ((arg: {
                interval: {
                    label: string;
                    start: unknown;
                    end: unknown;
                    events: any[];
                };
            }) => import("vue").VNodeChild) | undefined;
            interval?: ((arg: Record<string, unknown>) => import("vue").VNodeChild) | undefined;
        };
        'v-slots'?: {
            intervalBody?: false | ((arg: {
                interval: {
                    label: string;
                    start: unknown;
                    end: unknown;
                    events: any[];
                };
            }) => import("vue").VNodeChild) | undefined;
            intervalEvent?: false | ((arg: {
                height: string;
                margin: string;
                eventClass: string;
                event: any;
                interval: {
                    label: string;
                    start: unknown;
                    end: unknown;
                    events: any[];
                };
            }) => import("vue").VNodeChild) | undefined;
            intervalTitle?: false | ((arg: {
                interval: {
                    label: string;
                    start: unknown;
                    end: unknown;
                    events: any[];
                };
            }) => import("vue").VNodeChild) | undefined;
            interval?: false | ((arg: Record<string, unknown>) => import("vue").VNodeChild) | undefined;
        } | undefined;
    } & {
        "v-slot:intervalBody"?: false | ((arg: {
            interval: {
                label: string;
                start: unknown;
                end: unknown;
                events: any[];
            };
        }) => import("vue").VNodeChild) | undefined;
        "v-slot:intervalEvent"?: false | ((arg: {
            height: string;
            margin: string;
            eventClass: string;
            event: any;
            interval: {
                label: string;
                start: unknown;
                end: unknown;
                events: any[];
            };
        }) => import("vue").VNodeChild) | undefined;
        "v-slot:intervalTitle"?: false | ((arg: {
            interval: {
                label: string;
                start: unknown;
                end: unknown;
                events: any[];
            };
        }) => import("vue").VNodeChild) | undefined;
        "v-slot:interval"?: false | ((arg: Record<string, unknown>) => import("vue").VNodeChild) | undefined;
    }, {
        intervals: import("vue").ComputedRef<number[]>;
    }, {}, {}, {}, {
        day: Record<string, any>;
        intervalDivisions: number;
        intervalDuration: number;
        intervalHeight: number;
        intervalFormat: string | Function;
        intervalStart: number;
        hideDayHeader: boolean;
        intervals: number;
    }>;
    __isFragment?: never;
    __isTeleport?: never;
    __isSuspense?: never;
} & import("vue").ComponentOptionsBase<{
    day: Record<string, any>;
    intervalDivisions: number;
    intervalDuration: number;
    intervalHeight: number;
    intervalFormat: string | Function;
    intervalStart: number;
    hideDayHeader: boolean;
    intervals: number;
} & {
    events?: any[] | undefined;
    dayIndex?: number | undefined;
} & {
    $children?: {} | import("vue").VNodeChild | {
        intervalBody?: ((arg: {
            interval: {
                label: string;
                start: unknown;
                end: unknown;
                events: any[];
            };
        }) => import("vue").VNodeChild) | undefined;
        intervalEvent?: ((arg: {
            height: string;
            margin: string;
            eventClass: string;
            event: any;
            interval: {
                label: string;
                start: unknown;
                end: unknown;
                events: any[];
            };
        }) => import("vue").VNodeChild) | undefined;
        intervalTitle?: ((arg: {
            interval: {
                label: string;
                start: unknown;
                end: unknown;
                events: any[];
            };
        }) => import("vue").VNodeChild) | undefined;
        interval?: ((arg: Record<string, unknown>) => import("vue").VNodeChild) | undefined;
    };
    'v-slots'?: {
        intervalBody?: false | ((arg: {
            interval: {
                label: string;
                start: unknown;
                end: unknown;
                events: any[];
            };
        }) => import("vue").VNodeChild) | undefined;
        intervalEvent?: false | ((arg: {
            height: string;
            margin: string;
            eventClass: string;
            event: any;
            interval: {
                label: string;
                start: unknown;
                end: unknown;
                events: any[];
            };
        }) => import("vue").VNodeChild) | undefined;
        intervalTitle?: false | ((arg: {
            interval: {
                label: string;
                start: unknown;
                end: unknown;
                events: any[];
            };
        }) => import("vue").VNodeChild) | undefined;
        interval?: false | ((arg: Record<string, unknown>) => import("vue").VNodeChild) | undefined;
    } | undefined;
} & {
    "v-slot:intervalBody"?: false | ((arg: {
        interval: {
            label: string;
            start: unknown;
            end: unknown;
            events: any[];
        };
    }) => import("vue").VNodeChild) | undefined;
    "v-slot:intervalEvent"?: false | ((arg: {
        height: string;
        margin: string;
        eventClass: string;
        event: any;
        interval: {
            label: string;
            start: unknown;
            end: unknown;
            events: any[];
        };
    }) => import("vue").VNodeChild) | undefined;
    "v-slot:intervalTitle"?: false | ((arg: {
        interval: {
            label: string;
            start: unknown;
            end: unknown;
            events: any[];
        };
    }) => import("vue").VNodeChild) | undefined;
    "v-slot:interval"?: false | ((arg: Record<string, unknown>) => import("vue").VNodeChild) | undefined;
}, {
    intervals: import("vue").ComputedRef<number[]>;
}, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, {
    day: Record<string, any>;
    intervalDivisions: number;
    intervalDuration: number;
    intervalHeight: number;
    intervalFormat: string | Function;
    intervalStart: number;
    hideDayHeader: boolean;
    intervals: number;
}, {}, string, import("vue").SlotsType<Partial<{
    intervalBody: (arg: {
        interval: {
            label: string;
            start: unknown;
            end: unknown;
            events: any[];
        };
    }) => import("vue").VNode[];
    intervalEvent: (arg: {
        height: string;
        margin: string;
        eventClass: string;
        event: any;
        interval: {
            label: string;
            start: unknown;
            end: unknown;
            events: any[];
        };
    }) => import("vue").VNode[];
    intervalTitle: (arg: {
        interval: {
            label: string;
            start: unknown;
            end: unknown;
            events: any[];
        };
    }) => import("vue").VNode[];
    interval: (arg: Record<string, unknown>) => import("vue").VNode[];
}>>, import("vue").GlobalComponents, import("vue").GlobalDirectives, string, import("vue").ComponentProvideOptions> & import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps & import("../../util/index.js").FilterPropsOptions<{
    day: {
        type: ObjectConstructor;
        default: () => {};
    };
    dayIndex: NumberConstructor;
    events: {
        (arrayLength: number): any[];
        (...items: any[]): any[];
        new (arrayLength: number): any[];
        new (...items: any[]): any[];
        isArray(arg: any): arg is any[];
        readonly prototype: any[];
        from<T>(arrayLike: ArrayLike<T>): T[];
        from<T, U>(arrayLike: ArrayLike<T>, mapfn: (v: T, k: number) => U, thisArg?: any): U[];
        from<T>(iterable: Iterable<T> | ArrayLike<T>): T[];
        from<T, U>(iterable: Iterable<T> | ArrayLike<T>, mapfn: (v: T, k: number) => U, thisArg?: any): U[];
        of<T>(...items: T[]): T[];
        fromAsync<T>(iterableOrArrayLike: AsyncIterable<T> | Iterable<T | PromiseLike<T>> | ArrayLike<T | PromiseLike<T>>): Promise<T[]>;
        fromAsync<T, U>(iterableOrArrayLike: AsyncIterable<T> | Iterable<T> | ArrayLike<T>, mapFn: (value: Awaited<T>, index: number) => U, thisArg?: any): Promise<Awaited<U>[]>;
        readonly [Symbol.species]: ArrayConstructor;
    };
    intervalDivisions: {
        type: NumberConstructor;
        default: number;
    };
    intervalDuration: {
        type: NumberConstructor;
        default: number;
    };
    intervalHeight: {
        type: NumberConstructor;
        default: number;
    };
    intervalFormat: {
        type: (FunctionConstructor | StringConstructor)[];
        default: string;
    };
    intervalStart: {
        type: NumberConstructor;
        default: number;
    };
    hideDayHeader: BooleanConstructor;
    intervals: {
        type: NumberConstructor;
        default: number;
    };
}, import("vue").ExtractPropTypes<{
    day: {
        type: ObjectConstructor;
        default: () => {};
    };
    dayIndex: NumberConstructor;
    events: {
        (arrayLength: number): any[];
        (...items: any[]): any[];
        new (arrayLength: number): any[];
        new (...items: any[]): any[];
        isArray(arg: any): arg is any[];
        readonly prototype: any[];
        from<T>(arrayLike: ArrayLike<T>): T[];
        from<T, U>(arrayLike: ArrayLike<T>, mapfn: (v: T, k: number) => U, thisArg?: any): U[];
        from<T>(iterable: Iterable<T> | ArrayLike<T>): T[];
        from<T, U>(iterable: Iterable<T> | ArrayLike<T>, mapfn: (v: T, k: number) => U, thisArg?: any): U[];
        of<T>(...items: T[]): T[];
        fromAsync<T>(iterableOrArrayLike: AsyncIterable<T> | Iterable<T | PromiseLike<T>> | ArrayLike<T | PromiseLike<T>>): Promise<T[]>;
        fromAsync<T, U>(iterableOrArrayLike: AsyncIterable<T> | Iterable<T> | ArrayLike<T>, mapFn: (value: Awaited<T>, index: number) => U, thisArg?: any): Promise<Awaited<U>[]>;
        readonly [Symbol.species]: ArrayConstructor;
    };
    intervalDivisions: {
        type: NumberConstructor;
        default: number;
    };
    intervalDuration: {
        type: NumberConstructor;
        default: number;
    };
    intervalHeight: {
        type: NumberConstructor;
        default: number;
    };
    intervalFormat: {
        type: (FunctionConstructor | StringConstructor)[];
        default: string;
    };
    intervalStart: {
        type: NumberConstructor;
        default: number;
    };
    hideDayHeader: BooleanConstructor;
    intervals: {
        type: NumberConstructor;
        default: number;
    };
}>>;
export type VCalendarDay = InstanceType<typeof VCalendarDay>;
