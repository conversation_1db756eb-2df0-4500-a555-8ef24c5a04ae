import { provideExpanded } from './composables/expand.js';
import { provideSelection } from './composables/select.js';
import type { DeepReadonly } from 'vue';
import type { VDataTableSlotProps } from './VDataTable.js';
import type { VDataTableHeadersSlots } from './VDataTableHeaders.js';
import type { VDataTableRowsSlots } from './VDataTableRows.js';
import type { CellProps, DataTableHeader, RowProps } from "./types.js";
import type { GenericProps, SelectItemKey, TemplateRef } from "../../util/index.js";
type VDataTableVirtualSlotProps<T> = Omit<VDataTableSlotProps<T>, 'setItemsPerPage' | 'page' | 'pageCount' | 'itemsPerPage'>;
export type VDataTableVirtualSlots<T> = VDataTableRowsSlots<T> & VDataTableHeadersSlots & {
    colgroup: VDataTableVirtualSlotProps<T>;
    top: VDataTableVirtualSlotProps<T>;
    headers: VDataTableHeadersSlots['headers'];
    tbody: VDataTableVirtualSlotProps<T>;
    thead: VDataTableVirtualSlotProps<T>;
    tfoot: VDataTableVirtualSlotProps<T>;
    bottom: VDataTableVirtualSlotProps<T>;
    'body.prepend': VDataTableVirtualSlotProps<T>;
    'body.append': VDataTableVirtualSlotProps<T>;
    item: {
        itemRef: TemplateRef;
    };
};
export declare const makeVDataTableVirtualProps: <Defaults extends {
    customFilter?: unknown;
    customKeyFilter?: unknown;
    filterKeys?: unknown;
    filterMode?: unknown;
    noFilter?: unknown;
    itemHeight?: unknown;
    itemKey?: unknown;
    height?: unknown;
    groupBy?: unknown;
    search?: unknown;
    width?: unknown;
    color?: unknown;
    expanded?: unknown;
    loading?: unknown;
    style?: unknown;
    mobile?: unknown;
    class?: unknown;
    theme?: unknown;
    headers?: unknown;
    tag?: unknown;
    sticky?: unknown;
    noDataText?: unknown;
    loadingText?: unknown;
    sortBy?: unknown;
    mobileBreakpoint?: unknown;
    modelValue?: unknown;
    expandOnClick?: unknown;
    showExpand?: unknown;
    multiSort?: unknown;
    mustSort?: unknown;
    customKeySort?: unknown;
    headerProps?: unknown;
    cellProps?: unknown;
    disableSort?: unknown;
    items?: unknown;
    itemValue?: unknown;
    itemSelectable?: unknown;
    returnObject?: unknown;
    rowProps?: unknown;
    showSelect?: unknown;
    selectStrategy?: unknown;
    valueComparator?: unknown;
    density?: unknown;
    hideNoData?: unknown;
    hover?: unknown;
    fixedHeader?: unknown;
    sortAscIcon?: unknown;
    sortDescIcon?: unknown;
    fixedFooter?: unknown;
    hideDefaultBody?: unknown;
    hideDefaultHeader?: unknown;
} = {}>(defaults?: Defaults | undefined) => {
    customFilter: unknown extends Defaults["customFilter"] ? import("vue").PropType<import("../../composables/filter.js").FilterFunction> : {
        type: import("vue").PropType<unknown extends Defaults["customFilter"] ? import("../../composables/filter.js").FilterFunction : import("../../composables/filter.js").FilterFunction | Defaults["customFilter"]>;
        default: unknown extends Defaults["customFilter"] ? import("../../composables/filter.js").FilterFunction : import("../../composables/filter.js").FilterFunction | Defaults["customFilter"];
    };
    customKeyFilter: unknown extends Defaults["customKeyFilter"] ? import("vue").PropType<import("../../composables/filter.js").FilterKeyFunctions> : {
        type: import("vue").PropType<unknown extends Defaults["customKeyFilter"] ? import("../../composables/filter.js").FilterKeyFunctions : import("../../composables/filter.js").FilterKeyFunctions | Defaults["customKeyFilter"]>;
        default: unknown extends Defaults["customKeyFilter"] ? import("../../composables/filter.js").FilterKeyFunctions : import("../../composables/filter.js").FilterKeyFunctions | Defaults["customKeyFilter"];
    };
    filterKeys: unknown extends Defaults["filterKeys"] ? import("vue").PropType<import("../../composables/filter.js").FilterKeys> : {
        type: import("vue").PropType<unknown extends Defaults["filterKeys"] ? import("../../composables/filter.js").FilterKeys : import("../../composables/filter.js").FilterKeys | Defaults["filterKeys"]>;
        default: unknown extends Defaults["filterKeys"] ? import("../../composables/filter.js").FilterKeys : NonNullable<import("../../composables/filter.js").FilterKeys> | Defaults["filterKeys"];
    };
    filterMode: unknown extends Defaults["filterMode"] ? {
        type: import("vue").PropType<import("../../composables/filter.js").FilterMode>;
        default: string;
    } : Omit<{
        type: import("vue").PropType<import("../../composables/filter.js").FilterMode>;
        default: string;
    }, "type" | "default"> & {
        type: import("vue").PropType<unknown extends Defaults["filterMode"] ? import("../../composables/filter.js").FilterMode : import("../../composables/filter.js").FilterMode | Defaults["filterMode"]>;
        default: unknown extends Defaults["filterMode"] ? import("../../composables/filter.js").FilterMode : NonNullable<import("../../composables/filter.js").FilterMode> | Defaults["filterMode"];
    };
    noFilter: unknown extends Defaults["noFilter"] ? BooleanConstructor : {
        type: import("vue").PropType<unknown extends Defaults["noFilter"] ? boolean : boolean | Defaults["noFilter"]>;
        default: unknown extends Defaults["noFilter"] ? boolean : boolean | Defaults["noFilter"];
    };
    itemHeight: unknown extends Defaults["itemHeight"] ? {
        type: (StringConstructor | NumberConstructor)[];
        default: null;
    } : Omit<{
        type: (StringConstructor | NumberConstructor)[];
        default: null;
    }, "type" | "default"> & {
        type: import("vue").PropType<unknown extends Defaults["itemHeight"] ? string | number : string | number | Defaults["itemHeight"]>;
        default: unknown extends Defaults["itemHeight"] ? string | number : NonNullable<string | number> | Defaults["itemHeight"];
    };
    itemKey: unknown extends Defaults["itemKey"] ? {
        type: import("vue").PropType<SelectItemKey>;
        default: null;
    } : Omit<{
        type: import("vue").PropType<SelectItemKey>;
        default: null;
    }, "type" | "default"> & {
        type: import("vue").PropType<unknown extends Defaults["itemKey"] ? SelectItemKey : SelectItemKey | Defaults["itemKey"]>;
        default: unknown extends Defaults["itemKey"] ? SelectItemKey : NonNullable<SelectItemKey> | Defaults["itemKey"];
    };
    height: unknown extends Defaults["height"] ? (StringConstructor | NumberConstructor)[] : {
        type: import("vue").PropType<unknown extends Defaults["height"] ? string | number : string | number | Defaults["height"]>;
        default: unknown extends Defaults["height"] ? string | number : NonNullable<string | number> | Defaults["height"];
    };
    groupBy: unknown extends Defaults["groupBy"] ? {
        type: import("vue").PropType<readonly import("./composables/sort.js").SortItem[]>;
        default: () => never[];
    } : Omit<{
        type: import("vue").PropType<readonly import("./composables/sort.js").SortItem[]>;
        default: () => never[];
    }, "type" | "default"> & {
        type: import("vue").PropType<unknown extends Defaults["groupBy"] ? readonly import("./composables/sort.js").SortItem[] : readonly import("./composables/sort.js").SortItem[] | Defaults["groupBy"]>;
        default: unknown extends Defaults["groupBy"] ? readonly import("./composables/sort.js").SortItem[] : readonly import("./composables/sort.js").SortItem[] | Defaults["groupBy"];
    };
    search: unknown extends Defaults["search"] ? StringConstructor : {
        type: import("vue").PropType<unknown extends Defaults["search"] ? string : string | Defaults["search"]>;
        default: unknown extends Defaults["search"] ? string : string | Defaults["search"];
    };
    width: unknown extends Defaults["width"] ? (StringConstructor | NumberConstructor)[] : {
        type: import("vue").PropType<unknown extends Defaults["width"] ? string | number : string | number | Defaults["width"]>;
        default: unknown extends Defaults["width"] ? string | number : NonNullable<string | number> | Defaults["width"];
    };
    color: unknown extends Defaults["color"] ? StringConstructor : {
        type: import("vue").PropType<unknown extends Defaults["color"] ? string : string | Defaults["color"]>;
        default: unknown extends Defaults["color"] ? string : string | Defaults["color"];
    };
    expanded: unknown extends Defaults["expanded"] ? {
        type: import("vue").PropType<readonly string[]>;
        default: () => never[];
    } : Omit<{
        type: import("vue").PropType<readonly string[]>;
        default: () => never[];
    }, "type" | "default"> & {
        type: import("vue").PropType<unknown extends Defaults["expanded"] ? readonly string[] : readonly string[] | Defaults["expanded"]>;
        default: unknown extends Defaults["expanded"] ? readonly string[] : readonly string[] | Defaults["expanded"];
    };
    loading: unknown extends Defaults["loading"] ? (StringConstructor | BooleanConstructor)[] : {
        type: import("vue").PropType<unknown extends Defaults["loading"] ? string | boolean : string | boolean | Defaults["loading"]>;
        default: unknown extends Defaults["loading"] ? string | boolean : NonNullable<string | boolean> | Defaults["loading"];
    };
    style: unknown extends Defaults["style"] ? {
        type: import("vue").PropType<import("vue").StyleValue>;
        default: null;
    } : Omit<{
        type: import("vue").PropType<import("vue").StyleValue>;
        default: null;
    }, "type" | "default"> & {
        type: import("vue").PropType<unknown extends Defaults["style"] ? import("vue").StyleValue : import("vue").StyleValue | Defaults["style"]>;
        default: unknown extends Defaults["style"] ? import("vue").StyleValue : NonNullable<import("vue").StyleValue> | Defaults["style"];
    };
    mobile: unknown extends Defaults["mobile"] ? {
        type: import("vue").PropType<boolean | null>;
        default: boolean;
    } : Omit<{
        type: import("vue").PropType<boolean | null>;
        default: boolean;
    }, "type" | "default"> & {
        type: import("vue").PropType<unknown extends Defaults["mobile"] ? boolean | null : boolean | Defaults["mobile"] | null>;
        default: unknown extends Defaults["mobile"] ? boolean | null : NonNullable<boolean | null> | Defaults["mobile"];
    };
    class: unknown extends Defaults["class"] ? import("vue").PropType<any> : {
        type: import("vue").PropType<unknown extends Defaults["class"] ? any : any>;
        default: unknown extends Defaults["class"] ? any : any;
    };
    theme: unknown extends Defaults["theme"] ? StringConstructor : {
        type: import("vue").PropType<unknown extends Defaults["theme"] ? string : string | Defaults["theme"]>;
        default: unknown extends Defaults["theme"] ? string : string | Defaults["theme"];
    };
    headers: unknown extends Defaults["headers"] ? import("vue").PropType<readonly {
        readonly key?: "data-table-group" | "data-table-select" | "data-table-expand" | (string & {}) | undefined;
        readonly value?: SelectItemKey<Record<string, any>>;
        readonly title?: string | undefined;
        readonly fixed?: boolean | undefined;
        readonly align?: "start" | "end" | "center" | undefined;
        readonly width?: number | string | undefined;
        readonly minWidth?: number | string | undefined;
        readonly maxWidth?: number | string | undefined;
        readonly nowrap?: boolean | undefined;
        readonly headerProps?: {
            readonly [x: string]: any;
        } | undefined;
        readonly cellProps?: import("./types.js").HeaderCellPropsFunction | {
            readonly [x: string]: any;
        } | undefined;
        readonly sortable?: boolean | undefined;
        readonly sort?: import("./types.js").DataTableCompareFunction | undefined;
        readonly sortRaw?: import("./types.js").DataTableCompareFunction | undefined;
        readonly filter?: import("../../composables/filter.js").FilterFunction | undefined;
        readonly children?: readonly {
            readonly key?: "data-table-group" | "data-table-select" | "data-table-expand" | (string & {}) | undefined;
            readonly value?: SelectItemKey<Record<string, any>>;
            readonly title?: string | undefined;
            readonly fixed?: boolean | undefined;
            readonly align?: "start" | "end" | "center" | undefined;
            readonly width?: number | string | undefined;
            readonly minWidth?: number | string | undefined;
            readonly maxWidth?: number | string | undefined;
            readonly nowrap?: boolean | undefined;
            readonly headerProps?: {
                readonly [x: string]: any;
            } | undefined;
            readonly cellProps?: import("./types.js").HeaderCellPropsFunction | {
                readonly [x: string]: any;
            } | undefined;
            readonly sortable?: boolean | undefined;
            readonly sort?: import("./types.js").DataTableCompareFunction | undefined;
            readonly sortRaw?: import("./types.js").DataTableCompareFunction | undefined;
            readonly filter?: import("../../composables/filter.js").FilterFunction | undefined;
            readonly children?: readonly /*elided*/ any[] | undefined;
        }[] | undefined;
    }[]> : {
        type: import("vue").PropType<unknown extends Defaults["headers"] ? readonly {
            readonly key?: "data-table-group" | "data-table-select" | "data-table-expand" | (string & {}) | undefined;
            readonly value?: SelectItemKey<Record<string, any>>;
            readonly title?: string | undefined;
            readonly fixed?: boolean | undefined;
            readonly align?: "start" | "end" | "center" | undefined;
            readonly width?: number | string | undefined;
            readonly minWidth?: number | string | undefined;
            readonly maxWidth?: number | string | undefined;
            readonly nowrap?: boolean | undefined;
            readonly headerProps?: {
                readonly [x: string]: any;
            } | undefined;
            readonly cellProps?: import("./types.js").HeaderCellPropsFunction | {
                readonly [x: string]: any;
            } | undefined;
            readonly sortable?: boolean | undefined;
            readonly sort?: import("./types.js").DataTableCompareFunction | undefined;
            readonly sortRaw?: import("./types.js").DataTableCompareFunction | undefined;
            readonly filter?: import("../../composables/filter.js").FilterFunction | undefined;
            readonly children?: readonly {
                readonly key?: "data-table-group" | "data-table-select" | "data-table-expand" | (string & {}) | undefined;
                readonly value?: SelectItemKey<Record<string, any>>;
                readonly title?: string | undefined;
                readonly fixed?: boolean | undefined;
                readonly align?: "start" | "end" | "center" | undefined;
                readonly width?: number | string | undefined;
                readonly minWidth?: number | string | undefined;
                readonly maxWidth?: number | string | undefined;
                readonly nowrap?: boolean | undefined;
                readonly headerProps?: {
                    readonly [x: string]: any;
                } | undefined;
                readonly cellProps?: import("./types.js").HeaderCellPropsFunction | {
                    readonly [x: string]: any;
                } | undefined;
                readonly sortable?: boolean | undefined;
                readonly sort?: import("./types.js").DataTableCompareFunction | undefined;
                readonly sortRaw?: import("./types.js").DataTableCompareFunction | undefined;
                readonly filter?: import("../../composables/filter.js").FilterFunction | undefined;
                readonly children?: readonly /*elided*/ any[] | undefined;
            }[] | undefined;
        }[] : readonly {
            readonly key?: "data-table-group" | "data-table-select" | "data-table-expand" | (string & {}) | undefined;
            readonly value?: SelectItemKey<Record<string, any>>;
            readonly title?: string | undefined;
            readonly fixed?: boolean | undefined;
            readonly align?: "start" | "end" | "center" | undefined;
            readonly width?: number | string | undefined;
            readonly minWidth?: number | string | undefined;
            readonly maxWidth?: number | string | undefined;
            readonly nowrap?: boolean | undefined;
            readonly headerProps?: {
                readonly [x: string]: any;
            } | undefined;
            readonly cellProps?: import("./types.js").HeaderCellPropsFunction | {
                readonly [x: string]: any;
            } | undefined;
            readonly sortable?: boolean | undefined;
            readonly sort?: import("./types.js").DataTableCompareFunction | undefined;
            readonly sortRaw?: import("./types.js").DataTableCompareFunction | undefined;
            readonly filter?: import("../../composables/filter.js").FilterFunction | undefined;
            readonly children?: readonly {
                readonly key?: "data-table-group" | "data-table-select" | "data-table-expand" | (string & {}) | undefined;
                readonly value?: SelectItemKey<Record<string, any>>;
                readonly title?: string | undefined;
                readonly fixed?: boolean | undefined;
                readonly align?: "start" | "end" | "center" | undefined;
                readonly width?: number | string | undefined;
                readonly minWidth?: number | string | undefined;
                readonly maxWidth?: number | string | undefined;
                readonly nowrap?: boolean | undefined;
                readonly headerProps?: {
                    readonly [x: string]: any;
                } | undefined;
                readonly cellProps?: import("./types.js").HeaderCellPropsFunction | {
                    readonly [x: string]: any;
                } | undefined;
                readonly sortable?: boolean | undefined;
                readonly sort?: import("./types.js").DataTableCompareFunction | undefined;
                readonly sortRaw?: import("./types.js").DataTableCompareFunction | undefined;
                readonly filter?: import("../../composables/filter.js").FilterFunction | undefined;
                readonly children?: readonly /*elided*/ any[] | undefined;
            }[] | undefined;
        }[] | Defaults["headers"]>;
        default: unknown extends Defaults["headers"] ? readonly {
            readonly key?: "data-table-group" | "data-table-select" | "data-table-expand" | (string & {}) | undefined;
            readonly value?: SelectItemKey<Record<string, any>>;
            readonly title?: string | undefined;
            readonly fixed?: boolean | undefined;
            readonly align?: "start" | "end" | "center" | undefined;
            readonly width?: number | string | undefined;
            readonly minWidth?: number | string | undefined;
            readonly maxWidth?: number | string | undefined;
            readonly nowrap?: boolean | undefined;
            readonly headerProps?: {
                readonly [x: string]: any;
            } | undefined;
            readonly cellProps?: import("./types.js").HeaderCellPropsFunction | {
                readonly [x: string]: any;
            } | undefined;
            readonly sortable?: boolean | undefined;
            readonly sort?: import("./types.js").DataTableCompareFunction | undefined;
            readonly sortRaw?: import("./types.js").DataTableCompareFunction | undefined;
            readonly filter?: import("../../composables/filter.js").FilterFunction | undefined;
            readonly children?: readonly {
                readonly key?: "data-table-group" | "data-table-select" | "data-table-expand" | (string & {}) | undefined;
                readonly value?: SelectItemKey<Record<string, any>>;
                readonly title?: string | undefined;
                readonly fixed?: boolean | undefined;
                readonly align?: "start" | "end" | "center" | undefined;
                readonly width?: number | string | undefined;
                readonly minWidth?: number | string | undefined;
                readonly maxWidth?: number | string | undefined;
                readonly nowrap?: boolean | undefined;
                readonly headerProps?: {
                    readonly [x: string]: any;
                } | undefined;
                readonly cellProps?: import("./types.js").HeaderCellPropsFunction | {
                    readonly [x: string]: any;
                } | undefined;
                readonly sortable?: boolean | undefined;
                readonly sort?: import("./types.js").DataTableCompareFunction | undefined;
                readonly sortRaw?: import("./types.js").DataTableCompareFunction | undefined;
                readonly filter?: import("../../composables/filter.js").FilterFunction | undefined;
                readonly children?: readonly /*elided*/ any[] | undefined;
            }[] | undefined;
        }[] : readonly {
            readonly key?: "data-table-group" | "data-table-select" | "data-table-expand" | (string & {}) | undefined;
            readonly value?: SelectItemKey<Record<string, any>>;
            readonly title?: string | undefined;
            readonly fixed?: boolean | undefined;
            readonly align?: "start" | "end" | "center" | undefined;
            readonly width?: number | string | undefined;
            readonly minWidth?: number | string | undefined;
            readonly maxWidth?: number | string | undefined;
            readonly nowrap?: boolean | undefined;
            readonly headerProps?: {
                readonly [x: string]: any;
            } | undefined;
            readonly cellProps?: import("./types.js").HeaderCellPropsFunction | {
                readonly [x: string]: any;
            } | undefined;
            readonly sortable?: boolean | undefined;
            readonly sort?: import("./types.js").DataTableCompareFunction | undefined;
            readonly sortRaw?: import("./types.js").DataTableCompareFunction | undefined;
            readonly filter?: import("../../composables/filter.js").FilterFunction | undefined;
            readonly children?: readonly {
                readonly key?: "data-table-group" | "data-table-select" | "data-table-expand" | (string & {}) | undefined;
                readonly value?: SelectItemKey<Record<string, any>>;
                readonly title?: string | undefined;
                readonly fixed?: boolean | undefined;
                readonly align?: "start" | "end" | "center" | undefined;
                readonly width?: number | string | undefined;
                readonly minWidth?: number | string | undefined;
                readonly maxWidth?: number | string | undefined;
                readonly nowrap?: boolean | undefined;
                readonly headerProps?: {
                    readonly [x: string]: any;
                } | undefined;
                readonly cellProps?: import("./types.js").HeaderCellPropsFunction | {
                    readonly [x: string]: any;
                } | undefined;
                readonly sortable?: boolean | undefined;
                readonly sort?: import("./types.js").DataTableCompareFunction | undefined;
                readonly sortRaw?: import("./types.js").DataTableCompareFunction | undefined;
                readonly filter?: import("../../composables/filter.js").FilterFunction | undefined;
                readonly children?: readonly /*elided*/ any[] | undefined;
            }[] | undefined;
        }[] | Defaults["headers"];
    };
    tag: unknown extends Defaults["tag"] ? {
        type: import("vue").PropType<string | import("../../util/index.js").JSXComponent>;
        default: string;
    } : Omit<{
        type: import("vue").PropType<string | import("../../util/index.js").JSXComponent>;
        default: string;
    }, "type" | "default"> & {
        type: import("vue").PropType<unknown extends Defaults["tag"] ? string | import("../../util/index.js").JSXComponent : string | import("../../util/index.js").JSXComponent | Defaults["tag"]>;
        default: unknown extends Defaults["tag"] ? string | import("../../util/index.js").JSXComponent : NonNullable<string | import("../../util/index.js").JSXComponent> | Defaults["tag"];
    };
    sticky: unknown extends Defaults["sticky"] ? BooleanConstructor : {
        type: import("vue").PropType<unknown extends Defaults["sticky"] ? boolean : boolean | Defaults["sticky"]>;
        default: unknown extends Defaults["sticky"] ? boolean : boolean | Defaults["sticky"];
    };
    noDataText: unknown extends Defaults["noDataText"] ? {
        type: StringConstructor;
        default: string;
    } : Omit<{
        type: StringConstructor;
        default: string;
    }, "type" | "default"> & {
        type: import("vue").PropType<unknown extends Defaults["noDataText"] ? string : string | Defaults["noDataText"]>;
        default: unknown extends Defaults["noDataText"] ? string : string | Defaults["noDataText"];
    };
    loadingText: unknown extends Defaults["loadingText"] ? {
        type: StringConstructor;
        default: string;
    } : Omit<{
        type: StringConstructor;
        default: string;
    }, "type" | "default"> & {
        type: import("vue").PropType<unknown extends Defaults["loadingText"] ? string : string | Defaults["loadingText"]>;
        default: unknown extends Defaults["loadingText"] ? string : string | Defaults["loadingText"];
    };
    sortBy: unknown extends Defaults["sortBy"] ? {
        type: import("vue").PropType<readonly import("./composables/sort.js").SortItem[]>;
        default: () => never[];
    } : Omit<{
        type: import("vue").PropType<readonly import("./composables/sort.js").SortItem[]>;
        default: () => never[];
    }, "type" | "default"> & {
        type: import("vue").PropType<unknown extends Defaults["sortBy"] ? readonly import("./composables/sort.js").SortItem[] : readonly import("./composables/sort.js").SortItem[] | Defaults["sortBy"]>;
        default: unknown extends Defaults["sortBy"] ? readonly import("./composables/sort.js").SortItem[] : readonly import("./composables/sort.js").SortItem[] | Defaults["sortBy"];
    };
    mobileBreakpoint: unknown extends Defaults["mobileBreakpoint"] ? import("vue").PropType<number | import("../../types.js").DisplayBreakpoint> : {
        type: import("vue").PropType<unknown extends Defaults["mobileBreakpoint"] ? number | import("../../types.js").DisplayBreakpoint : number | import("../../types.js").DisplayBreakpoint | Defaults["mobileBreakpoint"]>;
        default: unknown extends Defaults["mobileBreakpoint"] ? number | import("../../types.js").DisplayBreakpoint : NonNullable<number | import("../../types.js").DisplayBreakpoint> | Defaults["mobileBreakpoint"];
    };
    modelValue: unknown extends Defaults["modelValue"] ? {
        type: import("vue").PropType<readonly any[]>;
        default: () => never[];
    } : Omit<{
        type: import("vue").PropType<readonly any[]>;
        default: () => never[];
    }, "type" | "default"> & {
        type: import("vue").PropType<unknown extends Defaults["modelValue"] ? readonly any[] : readonly any[] | Defaults["modelValue"]>;
        default: unknown extends Defaults["modelValue"] ? readonly any[] : readonly any[] | Defaults["modelValue"];
    };
    expandOnClick: unknown extends Defaults["expandOnClick"] ? BooleanConstructor : {
        type: import("vue").PropType<unknown extends Defaults["expandOnClick"] ? boolean : boolean | Defaults["expandOnClick"]>;
        default: unknown extends Defaults["expandOnClick"] ? boolean : boolean | Defaults["expandOnClick"];
    };
    showExpand: unknown extends Defaults["showExpand"] ? BooleanConstructor : {
        type: import("vue").PropType<unknown extends Defaults["showExpand"] ? boolean : boolean | Defaults["showExpand"]>;
        default: unknown extends Defaults["showExpand"] ? boolean : boolean | Defaults["showExpand"];
    };
    multiSort: unknown extends Defaults["multiSort"] ? BooleanConstructor : {
        type: import("vue").PropType<unknown extends Defaults["multiSort"] ? boolean : boolean | Defaults["multiSort"]>;
        default: unknown extends Defaults["multiSort"] ? boolean : boolean | Defaults["multiSort"];
    };
    mustSort: unknown extends Defaults["mustSort"] ? BooleanConstructor : {
        type: import("vue").PropType<unknown extends Defaults["mustSort"] ? boolean : boolean | Defaults["mustSort"]>;
        default: unknown extends Defaults["mustSort"] ? boolean : boolean | Defaults["mustSort"];
    };
    customKeySort: unknown extends Defaults["customKeySort"] ? import("vue").PropType<Record<string, import("./types.js").DataTableCompareFunction>> : {
        type: import("vue").PropType<unknown extends Defaults["customKeySort"] ? Record<string, import("./types.js").DataTableCompareFunction> : Record<string, import("./types.js").DataTableCompareFunction> | Defaults["customKeySort"]>;
        default: unknown extends Defaults["customKeySort"] ? Record<string, import("./types.js").DataTableCompareFunction> : Record<string, import("./types.js").DataTableCompareFunction> | Defaults["customKeySort"];
    };
    headerProps: unknown extends Defaults["headerProps"] ? {
        type: import("vue").PropType<Record<string, any>>;
    } : Omit<{
        type: import("vue").PropType<Record<string, any>>;
    }, "type" | "default"> & {
        type: import("vue").PropType<unknown extends Defaults["headerProps"] ? Record<string, any> : Record<string, any> | Defaults["headerProps"]>;
        default: unknown extends Defaults["headerProps"] ? Record<string, any> : Record<string, any> | Defaults["headerProps"];
    };
    cellProps: unknown extends Defaults["cellProps"] ? import("vue").PropType<CellProps<any>> : {
        type: import("vue").PropType<unknown extends Defaults["cellProps"] ? CellProps<any> : CellProps<any> | Defaults["cellProps"]>;
        default: unknown extends Defaults["cellProps"] ? CellProps<any> : NonNullable<CellProps<any>> | Defaults["cellProps"];
    };
    disableSort: unknown extends Defaults["disableSort"] ? BooleanConstructor : {
        type: import("vue").PropType<unknown extends Defaults["disableSort"] ? boolean : boolean | Defaults["disableSort"]>;
        default: unknown extends Defaults["disableSort"] ? boolean : boolean | Defaults["disableSort"];
    };
    items: unknown extends Defaults["items"] ? {
        type: import("vue").PropType<import("./composables/items.js").DataTableItemProps["items"]>;
        default: () => never[];
    } : Omit<{
        type: import("vue").PropType<import("./composables/items.js").DataTableItemProps["items"]>;
        default: () => never[];
    }, "type" | "default"> & {
        type: import("vue").PropType<unknown extends Defaults["items"] ? any[] : any[] | Defaults["items"]>;
        default: unknown extends Defaults["items"] ? any[] : any[] | Defaults["items"];
    };
    itemValue: unknown extends Defaults["itemValue"] ? {
        type: import("vue").PropType<SelectItemKey>;
        default: string;
    } : Omit<{
        type: import("vue").PropType<SelectItemKey>;
        default: string;
    }, "type" | "default"> & {
        type: import("vue").PropType<unknown extends Defaults["itemValue"] ? SelectItemKey : SelectItemKey | Defaults["itemValue"]>;
        default: unknown extends Defaults["itemValue"] ? SelectItemKey : NonNullable<SelectItemKey> | Defaults["itemValue"];
    };
    itemSelectable: unknown extends Defaults["itemSelectable"] ? {
        type: import("vue").PropType<SelectItemKey>;
        default: null;
    } : Omit<{
        type: import("vue").PropType<SelectItemKey>;
        default: null;
    }, "type" | "default"> & {
        type: import("vue").PropType<unknown extends Defaults["itemSelectable"] ? SelectItemKey : SelectItemKey | Defaults["itemSelectable"]>;
        default: unknown extends Defaults["itemSelectable"] ? SelectItemKey : NonNullable<SelectItemKey> | Defaults["itemSelectable"];
    };
    returnObject: unknown extends Defaults["returnObject"] ? BooleanConstructor : {
        type: import("vue").PropType<unknown extends Defaults["returnObject"] ? boolean : boolean | Defaults["returnObject"]>;
        default: unknown extends Defaults["returnObject"] ? boolean : boolean | Defaults["returnObject"];
    };
    rowProps: unknown extends Defaults["rowProps"] ? import("vue").PropType<RowProps<any>> : {
        type: import("vue").PropType<unknown extends Defaults["rowProps"] ? RowProps<any> : RowProps<any> | Defaults["rowProps"]>;
        default: unknown extends Defaults["rowProps"] ? RowProps<any> : NonNullable<RowProps<any>> | Defaults["rowProps"];
    };
    showSelect: unknown extends Defaults["showSelect"] ? BooleanConstructor : {
        type: import("vue").PropType<unknown extends Defaults["showSelect"] ? boolean : boolean | Defaults["showSelect"]>;
        default: unknown extends Defaults["showSelect"] ? boolean : boolean | Defaults["showSelect"];
    };
    selectStrategy: unknown extends Defaults["selectStrategy"] ? {
        type: import("vue").PropType<"single" | "page" | "all">;
        default: string;
    } : Omit<{
        type: import("vue").PropType<"single" | "page" | "all">;
        default: string;
    }, "type" | "default"> & {
        type: import("vue").PropType<unknown extends Defaults["selectStrategy"] ? "all" | "page" | "single" : "all" | "page" | "single" | Defaults["selectStrategy"]>;
        default: unknown extends Defaults["selectStrategy"] ? "all" | "page" | "single" : NonNullable<"all" | "page" | "single"> | Defaults["selectStrategy"];
    };
    valueComparator: unknown extends Defaults["valueComparator"] ? {
        type: import("vue").PropType<typeof import("../../util/index.js").deepEqual>;
        default: typeof import("../../util/index.js").deepEqual;
    } : Omit<{
        type: import("vue").PropType<typeof import("../../util/index.js").deepEqual>;
        default: typeof import("../../util/index.js").deepEqual;
    }, "type" | "default"> & {
        type: import("vue").PropType<unknown extends Defaults["valueComparator"] ? typeof import("../../util/index.js").deepEqual : typeof import("../../util/index.js").deepEqual | Defaults["valueComparator"]>;
        default: unknown extends Defaults["valueComparator"] ? typeof import("../../util/index.js").deepEqual : typeof import("../../util/index.js").deepEqual | Defaults["valueComparator"];
    };
    density: unknown extends Defaults["density"] ? {
        type: import("vue").PropType<import("../../composables/density.js").Density>;
        default: string;
        validator: (v: any) => boolean;
    } : Omit<{
        type: import("vue").PropType<import("../../composables/density.js").Density>;
        default: string;
        validator: (v: any) => boolean;
    }, "type" | "default"> & {
        type: import("vue").PropType<unknown extends Defaults["density"] ? import("../../composables/density.js").Density : import("../../composables/density.js").Density | Defaults["density"]>;
        default: unknown extends Defaults["density"] ? import("../../composables/density.js").Density : NonNullable<import("../../composables/density.js").Density> | Defaults["density"];
    };
    hideNoData: unknown extends Defaults["hideNoData"] ? BooleanConstructor : {
        type: import("vue").PropType<unknown extends Defaults["hideNoData"] ? boolean : boolean | Defaults["hideNoData"]>;
        default: unknown extends Defaults["hideNoData"] ? boolean : boolean | Defaults["hideNoData"];
    };
    hover: unknown extends Defaults["hover"] ? BooleanConstructor : {
        type: import("vue").PropType<unknown extends Defaults["hover"] ? boolean : boolean | Defaults["hover"]>;
        default: unknown extends Defaults["hover"] ? boolean : boolean | Defaults["hover"];
    };
    fixedHeader: unknown extends Defaults["fixedHeader"] ? BooleanConstructor : {
        type: import("vue").PropType<unknown extends Defaults["fixedHeader"] ? boolean : boolean | Defaults["fixedHeader"]>;
        default: unknown extends Defaults["fixedHeader"] ? boolean : boolean | Defaults["fixedHeader"];
    };
    sortAscIcon: unknown extends Defaults["sortAscIcon"] ? {
        type: import("vue").PropType<import("../../composables/icons.js").IconValue>;
        default: string;
    } : Omit<{
        type: import("vue").PropType<import("../../composables/icons.js").IconValue>;
        default: string;
    }, "type" | "default"> & {
        type: import("vue").PropType<unknown extends Defaults["sortAscIcon"] ? import("../../composables/icons.js").IconValue : import("../../composables/icons.js").IconValue | Defaults["sortAscIcon"]>;
        default: unknown extends Defaults["sortAscIcon"] ? import("../../composables/icons.js").IconValue : NonNullable<import("../../composables/icons.js").IconValue> | Defaults["sortAscIcon"];
    };
    sortDescIcon: unknown extends Defaults["sortDescIcon"] ? {
        type: import("vue").PropType<import("../../composables/icons.js").IconValue>;
        default: string;
    } : Omit<{
        type: import("vue").PropType<import("../../composables/icons.js").IconValue>;
        default: string;
    }, "type" | "default"> & {
        type: import("vue").PropType<unknown extends Defaults["sortDescIcon"] ? import("../../composables/icons.js").IconValue : import("../../composables/icons.js").IconValue | Defaults["sortDescIcon"]>;
        default: unknown extends Defaults["sortDescIcon"] ? import("../../composables/icons.js").IconValue : NonNullable<import("../../composables/icons.js").IconValue> | Defaults["sortDescIcon"];
    };
    fixedFooter: unknown extends Defaults["fixedFooter"] ? BooleanConstructor : {
        type: import("vue").PropType<unknown extends Defaults["fixedFooter"] ? boolean : boolean | Defaults["fixedFooter"]>;
        default: unknown extends Defaults["fixedFooter"] ? boolean : boolean | Defaults["fixedFooter"];
    };
    hideDefaultBody: unknown extends Defaults["hideDefaultBody"] ? BooleanConstructor : {
        type: import("vue").PropType<unknown extends Defaults["hideDefaultBody"] ? boolean : boolean | Defaults["hideDefaultBody"]>;
        default: unknown extends Defaults["hideDefaultBody"] ? boolean : boolean | Defaults["hideDefaultBody"];
    };
    hideDefaultHeader: unknown extends Defaults["hideDefaultHeader"] ? BooleanConstructor : {
        type: import("vue").PropType<unknown extends Defaults["hideDefaultHeader"] ? boolean : boolean | Defaults["hideDefaultHeader"]>;
        default: unknown extends Defaults["hideDefaultHeader"] ? boolean : boolean | Defaults["hideDefaultHeader"];
    };
};
type ItemType<T> = T extends readonly (infer U)[] ? U : never;
export declare const VDataTableVirtual: {
    new (...args: any[]): import("vue").CreateComponentPublicInstanceWithMixins<{
        expanded: readonly string[];
        style: import("vue").StyleValue;
        mobile: boolean | null;
        tag: string | import("../../util/index.js").JSXComponent;
        sticky: boolean;
        noDataText: string;
        loadingText: string;
        sortBy: readonly import("./composables/sort.js").SortItem[];
        filterMode: import("../../composables/filter.js").FilterMode;
        noFilter: boolean;
        expandOnClick: boolean;
        showExpand: boolean;
        multiSort: boolean;
        mustSort: boolean;
        groupBy: readonly import("./composables/sort.js").SortItem[];
        disableSort: boolean;
        returnObject: boolean;
        showSelect: boolean;
        selectStrategy: "all" | "page" | "single";
        valueComparator: typeof import("../../util/index.js").deepEqual;
        density: import("../../composables/density.js").Density;
        itemHeight: string | number;
        itemKey: SelectItemKey;
        hideNoData: boolean;
        hover: boolean;
        fixedHeader: boolean;
        sortAscIcon: import("../../composables/icons.js").IconValue;
        sortDescIcon: import("../../composables/icons.js").IconValue;
        fixedFooter: boolean;
        hideDefaultBody: boolean;
        hideDefaultHeader: boolean;
    } & {
        search?: string | undefined;
        height?: string | number | undefined;
        width?: string | number | undefined;
        color?: string | undefined;
        loading?: string | boolean | undefined;
        class?: any;
        theme?: string | undefined;
        mobileBreakpoint?: number | import("../../types.js").DisplayBreakpoint | undefined;
        customFilter?: import("../../composables/filter.js").FilterFunction | undefined;
        customKeyFilter?: import("../../composables/filter.js").FilterKeyFunctions | undefined;
        filterKeys?: import("../../composables/filter.js").FilterKeys | undefined;
        customKeySort?: Record<string, import("./types.js").DataTableCompareFunction> | undefined;
        headerProps?: Record<string, any> | undefined;
    } & {
        "onUpdate:expanded"?: ((value: any) => any) | undefined;
        "onUpdate:sortBy"?: ((value: any) => any) | undefined;
        "onUpdate:groupBy"?: ((value: any) => any) | undefined;
        "onUpdate:options"?: ((value: any) => any) | undefined;
    }, {
        calculateVisibleItems: () => void;
        scrollToIndex: (index: number) => void;
    }, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Omit<{
        'update:modelValue': (value: any[]) => true;
        'update:sortBy': (value: any) => true;
        'update:options': (value: any) => true;
        'update:groupBy': (value: any) => true;
        'update:expanded': (value: any) => true;
    }, "headers" | "$children" | "v-slots" | "modelValue" | "cellProps" | "items" | "itemValue" | "itemSelectable" | "rowProps" | "update:modelValue" | "v-slot:loader" | "v-slot:item" | "v-slot:no-data" | "v-slot:headers" | `v-slot:header.${string}` | "v-slot:data-table-group" | "v-slot:data-table-select" | `v-slot:item.${string}` | "v-slot:loading" | "v-slot:group-header" | "v-slot:expanded-row" | "v-slot:top" | "v-slot:bottom" | "v-slot:colgroup" | "v-slot:tbody" | "v-slot:tfoot" | "v-slot:thead" | "v-slot:body.prepend" | "v-slot:body.append">, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, {
        expanded: readonly string[];
        style: import("vue").StyleValue;
        mobile: boolean | null;
        tag: string | import("../../util/index.js").JSXComponent;
        sticky: boolean;
        noDataText: string;
        loadingText: string;
        sortBy: readonly import("./composables/sort.js").SortItem[];
        filterMode: import("../../composables/filter.js").FilterMode;
        noFilter: boolean;
        expandOnClick: boolean;
        showExpand: boolean;
        multiSort: boolean;
        mustSort: boolean;
        groupBy: readonly import("./composables/sort.js").SortItem[];
        disableSort: boolean;
        returnObject: boolean;
        showSelect: boolean;
        selectStrategy: "all" | "page" | "single";
        valueComparator: typeof import("../../util/index.js").deepEqual;
        density: import("../../composables/density.js").Density;
        itemHeight: string | number;
        itemKey: SelectItemKey;
        hideNoData: boolean;
        hover: boolean;
        fixedHeader: boolean;
        sortAscIcon: import("../../composables/icons.js").IconValue;
        sortDescIcon: import("../../composables/icons.js").IconValue;
        fixedFooter: boolean;
        hideDefaultBody: boolean;
        hideDefaultHeader: boolean;
    }, true, {}, import("vue").SlotsType<Partial<{
        [x: `item.${string}`]: (arg: import("./types.js").ItemKeySlot<any>) => import("vue").VNode[];
        [x: `header.${string}`]: (arg: import("./VDataTableHeaders.js").VDataTableHeaderCellColumnSlotProps) => import("vue").VNode[];
        'data-table-group': (arg: {
            item: import("./composables/group.js").Group;
            count: number;
            props: Record<string, unknown>;
        }) => import("vue").VNode[];
        'data-table-select': (arg: {
            props: Record<string, unknown>;
        }) => import("vue").VNode[];
        'item.data-table-select': (arg: import("./VDataTableRow.js").VDataTableItemCellColumnSlotProps<any>) => import("vue").VNode[];
        'item.data-table-expand': (arg: import("./VDataTableRow.js").VDataTableItemCellColumnSlotProps<any>) => import("vue").VNode[];
        'header.data-table-select': (arg: import("./VDataTableHeaders.js").VDataTableHeaderCellColumnSlotProps) => import("vue").VNode[];
        'header.data-table-expand': (arg: import("./VDataTableHeaders.js").VDataTableHeaderCellColumnSlotProps) => import("vue").VNode[];
        item: (arg: {
            index: number;
            item: any;
            internalItem: import("./types.js").DataTableItem<any>;
            isExpanded: ReturnType<typeof provideExpanded>["isExpanded"];
            toggleExpand: ReturnType<typeof provideExpanded>["toggleExpand"];
            isSelected: ReturnType<typeof provideSelection>["isSelected"];
            toggleSelect: ReturnType<typeof provideSelection>["toggleSelect"];
        } & {
            columns: import("./types.js").InternalDataTableHeader[];
        } & {
            props: Record<string, any>;
        } & {
            itemRef: TemplateRef;
        }) => import("vue").VNode[];
        loading: () => import("vue").VNode[];
        'group-header': (arg: import("./types.js").GroupHeaderSlot) => import("vue").VNode[];
        'no-data': () => import("vue").VNode[];
        'expanded-row': (arg: import("./types.js").ItemSlot<any>) => import("vue").VNode[];
        headers: (arg: import("./VDataTableHeaders.js").HeadersSlotProps) => import("vue").VNode[];
        loader: (arg: import("../../composables/loader.js").LoaderSlotProps) => import("vue").VNode[];
        colgroup: (arg: VDataTableVirtualSlotProps<any>) => import("vue").VNode[];
        top: (arg: VDataTableVirtualSlotProps<any>) => import("vue").VNode[];
        tbody: (arg: VDataTableVirtualSlotProps<any>) => import("vue").VNode[];
        thead: (arg: VDataTableVirtualSlotProps<any>) => import("vue").VNode[];
        tfoot: (arg: VDataTableVirtualSlotProps<any>) => import("vue").VNode[];
        bottom: (arg: VDataTableVirtualSlotProps<any>) => import("vue").VNode[];
        'body.prepend': (arg: VDataTableVirtualSlotProps<any>) => import("vue").VNode[];
        'body.append': (arg: VDataTableVirtualSlotProps<any>) => import("vue").VNode[];
    }>>, import("vue").GlobalComponents, import("vue").GlobalDirectives, string, {}, any, import("vue").ComponentProvideOptions, {
        P: {};
        B: {};
        D: {};
        C: {};
        M: {};
        Defaults: {};
    }, {
        expanded: readonly string[];
        style: import("vue").StyleValue;
        mobile: boolean | null;
        tag: string | import("../../util/index.js").JSXComponent;
        sticky: boolean;
        noDataText: string;
        loadingText: string;
        sortBy: readonly import("./composables/sort.js").SortItem[];
        filterMode: import("../../composables/filter.js").FilterMode;
        noFilter: boolean;
        expandOnClick: boolean;
        showExpand: boolean;
        multiSort: boolean;
        mustSort: boolean;
        groupBy: readonly import("./composables/sort.js").SortItem[];
        disableSort: boolean;
        returnObject: boolean;
        showSelect: boolean;
        selectStrategy: "all" | "page" | "single";
        valueComparator: typeof import("../../util/index.js").deepEqual;
        density: import("../../composables/density.js").Density;
        itemHeight: string | number;
        itemKey: SelectItemKey;
        hideNoData: boolean;
        hover: boolean;
        fixedHeader: boolean;
        sortAscIcon: import("../../composables/icons.js").IconValue;
        sortDescIcon: import("../../composables/icons.js").IconValue;
        fixedFooter: boolean;
        hideDefaultBody: boolean;
        hideDefaultHeader: boolean;
    } & {
        search?: string | undefined;
        height?: string | number | undefined;
        width?: string | number | undefined;
        color?: string | undefined;
        loading?: string | boolean | undefined;
        class?: any;
        theme?: string | undefined;
        mobileBreakpoint?: number | import("../../types.js").DisplayBreakpoint | undefined;
        customFilter?: import("../../composables/filter.js").FilterFunction | undefined;
        customKeyFilter?: import("../../composables/filter.js").FilterKeyFunctions | undefined;
        filterKeys?: import("../../composables/filter.js").FilterKeys | undefined;
        customKeySort?: Record<string, import("./types.js").DataTableCompareFunction> | undefined;
        headerProps?: Record<string, any> | undefined;
    } & {
        "onUpdate:expanded"?: ((value: any) => any) | undefined;
        "onUpdate:sortBy"?: ((value: any) => any) | undefined;
        "onUpdate:groupBy"?: ((value: any) => any) | undefined;
        "onUpdate:options"?: ((value: any) => any) | undefined;
    }, {
        calculateVisibleItems: () => void;
        scrollToIndex: (index: number) => void;
    }, {}, {}, {}, {
        expanded: readonly string[];
        style: import("vue").StyleValue;
        mobile: boolean | null;
        tag: string | import("../../util/index.js").JSXComponent;
        sticky: boolean;
        noDataText: string;
        loadingText: string;
        sortBy: readonly import("./composables/sort.js").SortItem[];
        filterMode: import("../../composables/filter.js").FilterMode;
        noFilter: boolean;
        expandOnClick: boolean;
        showExpand: boolean;
        multiSort: boolean;
        mustSort: boolean;
        groupBy: readonly import("./composables/sort.js").SortItem[];
        disableSort: boolean;
        returnObject: boolean;
        showSelect: boolean;
        selectStrategy: "all" | "page" | "single";
        valueComparator: typeof import("../../util/index.js").deepEqual;
        density: import("../../composables/density.js").Density;
        itemHeight: string | number;
        itemKey: SelectItemKey;
        hideNoData: boolean;
        hover: boolean;
        fixedHeader: boolean;
        sortAscIcon: import("../../composables/icons.js").IconValue;
        sortDescIcon: import("../../composables/icons.js").IconValue;
        fixedFooter: boolean;
        hideDefaultBody: boolean;
        hideDefaultHeader: boolean;
    }>;
    __isFragment?: never;
    __isTeleport?: never;
    __isSuspense?: never;
} & import("vue").ComponentOptionsBase<{
    expanded: readonly string[];
    style: import("vue").StyleValue;
    mobile: boolean | null;
    tag: string | import("../../util/index.js").JSXComponent;
    sticky: boolean;
    noDataText: string;
    loadingText: string;
    sortBy: readonly import("./composables/sort.js").SortItem[];
    filterMode: import("../../composables/filter.js").FilterMode;
    noFilter: boolean;
    expandOnClick: boolean;
    showExpand: boolean;
    multiSort: boolean;
    mustSort: boolean;
    groupBy: readonly import("./composables/sort.js").SortItem[];
    disableSort: boolean;
    returnObject: boolean;
    showSelect: boolean;
    selectStrategy: "all" | "page" | "single";
    valueComparator: typeof import("../../util/index.js").deepEqual;
    density: import("../../composables/density.js").Density;
    itemHeight: string | number;
    itemKey: SelectItemKey;
    hideNoData: boolean;
    hover: boolean;
    fixedHeader: boolean;
    sortAscIcon: import("../../composables/icons.js").IconValue;
    sortDescIcon: import("../../composables/icons.js").IconValue;
    fixedFooter: boolean;
    hideDefaultBody: boolean;
    hideDefaultHeader: boolean;
} & {
    search?: string | undefined;
    height?: string | number | undefined;
    width?: string | number | undefined;
    color?: string | undefined;
    loading?: string | boolean | undefined;
    class?: any;
    theme?: string | undefined;
    mobileBreakpoint?: number | import("../../types.js").DisplayBreakpoint | undefined;
    customFilter?: import("../../composables/filter.js").FilterFunction | undefined;
    customKeyFilter?: import("../../composables/filter.js").FilterKeyFunctions | undefined;
    filterKeys?: import("../../composables/filter.js").FilterKeys | undefined;
    customKeySort?: Record<string, import("./types.js").DataTableCompareFunction> | undefined;
    headerProps?: Record<string, any> | undefined;
} & {
    "onUpdate:expanded"?: ((value: any) => any) | undefined;
    "onUpdate:sortBy"?: ((value: any) => any) | undefined;
    "onUpdate:groupBy"?: ((value: any) => any) | undefined;
    "onUpdate:options"?: ((value: any) => any) | undefined;
}, {
    calculateVisibleItems: () => void;
    scrollToIndex: (index: number) => void;
}, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Omit<{
    'update:modelValue': (value: any[]) => true;
    'update:sortBy': (value: any) => true;
    'update:options': (value: any) => true;
    'update:groupBy': (value: any) => true;
    'update:expanded': (value: any) => true;
}, "headers" | "$children" | "v-slots" | "modelValue" | "cellProps" | "items" | "itemValue" | "itemSelectable" | "rowProps" | "update:modelValue" | "v-slot:loader" | "v-slot:item" | "v-slot:no-data" | "v-slot:headers" | `v-slot:header.${string}` | "v-slot:data-table-group" | "v-slot:data-table-select" | `v-slot:item.${string}` | "v-slot:loading" | "v-slot:group-header" | "v-slot:expanded-row" | "v-slot:top" | "v-slot:bottom" | "v-slot:colgroup" | "v-slot:tbody" | "v-slot:tfoot" | "v-slot:thead" | "v-slot:body.prepend" | "v-slot:body.append">, string, {
    expanded: readonly string[];
    style: import("vue").StyleValue;
    mobile: boolean | null;
    tag: string | import("../../util/index.js").JSXComponent;
    sticky: boolean;
    noDataText: string;
    loadingText: string;
    sortBy: readonly import("./composables/sort.js").SortItem[];
    filterMode: import("../../composables/filter.js").FilterMode;
    noFilter: boolean;
    expandOnClick: boolean;
    showExpand: boolean;
    multiSort: boolean;
    mustSort: boolean;
    groupBy: readonly import("./composables/sort.js").SortItem[];
    disableSort: boolean;
    returnObject: boolean;
    showSelect: boolean;
    selectStrategy: "all" | "page" | "single";
    valueComparator: typeof import("../../util/index.js").deepEqual;
    density: import("../../composables/density.js").Density;
    itemHeight: string | number;
    itemKey: SelectItemKey;
    hideNoData: boolean;
    hover: boolean;
    fixedHeader: boolean;
    sortAscIcon: import("../../composables/icons.js").IconValue;
    sortDescIcon: import("../../composables/icons.js").IconValue;
    fixedFooter: boolean;
    hideDefaultBody: boolean;
    hideDefaultHeader: boolean;
}, {}, string, import("vue").SlotsType<Partial<{
    [x: `item.${string}`]: (arg: import("./types.js").ItemKeySlot<any>) => import("vue").VNode[];
    [x: `header.${string}`]: (arg: import("./VDataTableHeaders.js").VDataTableHeaderCellColumnSlotProps) => import("vue").VNode[];
    'data-table-group': (arg: {
        item: import("./composables/group.js").Group;
        count: number;
        props: Record<string, unknown>;
    }) => import("vue").VNode[];
    'data-table-select': (arg: {
        props: Record<string, unknown>;
    }) => import("vue").VNode[];
    'item.data-table-select': (arg: import("./VDataTableRow.js").VDataTableItemCellColumnSlotProps<any>) => import("vue").VNode[];
    'item.data-table-expand': (arg: import("./VDataTableRow.js").VDataTableItemCellColumnSlotProps<any>) => import("vue").VNode[];
    'header.data-table-select': (arg: import("./VDataTableHeaders.js").VDataTableHeaderCellColumnSlotProps) => import("vue").VNode[];
    'header.data-table-expand': (arg: import("./VDataTableHeaders.js").VDataTableHeaderCellColumnSlotProps) => import("vue").VNode[];
    item: (arg: {
        index: number;
        item: any;
        internalItem: import("./types.js").DataTableItem<any>;
        isExpanded: ReturnType<typeof provideExpanded>["isExpanded"];
        toggleExpand: ReturnType<typeof provideExpanded>["toggleExpand"];
        isSelected: ReturnType<typeof provideSelection>["isSelected"];
        toggleSelect: ReturnType<typeof provideSelection>["toggleSelect"];
    } & {
        columns: import("./types.js").InternalDataTableHeader[];
    } & {
        props: Record<string, any>;
    } & {
        itemRef: TemplateRef;
    }) => import("vue").VNode[];
    loading: () => import("vue").VNode[];
    'group-header': (arg: import("./types.js").GroupHeaderSlot) => import("vue").VNode[];
    'no-data': () => import("vue").VNode[];
    'expanded-row': (arg: import("./types.js").ItemSlot<any>) => import("vue").VNode[];
    headers: (arg: import("./VDataTableHeaders.js").HeadersSlotProps) => import("vue").VNode[];
    loader: (arg: import("../../composables/loader.js").LoaderSlotProps) => import("vue").VNode[];
    colgroup: (arg: VDataTableVirtualSlotProps<any>) => import("vue").VNode[];
    top: (arg: VDataTableVirtualSlotProps<any>) => import("vue").VNode[];
    tbody: (arg: VDataTableVirtualSlotProps<any>) => import("vue").VNode[];
    thead: (arg: VDataTableVirtualSlotProps<any>) => import("vue").VNode[];
    tfoot: (arg: VDataTableVirtualSlotProps<any>) => import("vue").VNode[];
    bottom: (arg: VDataTableVirtualSlotProps<any>) => import("vue").VNode[];
    'body.prepend': (arg: VDataTableVirtualSlotProps<any>) => import("vue").VNode[];
    'body.append': (arg: VDataTableVirtualSlotProps<any>) => import("vue").VNode[];
}>>, import("vue").GlobalComponents, import("vue").GlobalDirectives, string, import("vue").ComponentProvideOptions> & import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps & (new <T extends readonly any[], V>(props: {
    items?: T;
    itemValue?: SelectItemKey<ItemType<T>>;
    rowProps?: RowProps<ItemType<T>>;
    cellProps?: CellProps<ItemType<T>>;
    itemSelectable?: SelectItemKey<ItemType<T>>;
    headers?: DeepReadonly<DataTableHeader<ItemType<T>>[]>;
    modelValue?: V;
    "onUpdate:modelValue"?: (value: V) => void;
}, slots: VDataTableVirtualSlots<ItemType<T>>) => GenericProps<typeof props, typeof slots>) & import("../../util/index.js").FilterPropsOptions<{
    customFilter: import("vue").PropType<import("../../composables/filter.js").FilterFunction>;
    customKeyFilter: import("vue").PropType<import("../../composables/filter.js").FilterKeyFunctions>;
    filterKeys: import("vue").PropType<import("../../composables/filter.js").FilterKeys>;
    filterMode: {
        type: import("vue").PropType<import("../../composables/filter.js").FilterMode>;
        default: string;
    };
    noFilter: BooleanConstructor;
    itemHeight: {
        type: (StringConstructor | NumberConstructor)[];
        default: null;
    };
    itemKey: {
        type: import("vue").PropType<SelectItemKey>;
        default: null;
    };
    height: (StringConstructor | NumberConstructor)[];
    groupBy: {
        type: import("vue").PropType<readonly import("./composables/sort.js").SortItem[]>;
        default: () => never[];
    };
    search: StringConstructor;
    width: (StringConstructor | NumberConstructor)[];
    color: StringConstructor;
    expanded: {
        type: import("vue").PropType<readonly string[]>;
        default: () => never[];
    };
    loading: (StringConstructor | BooleanConstructor)[];
    style: {
        type: import("vue").PropType<import("vue").StyleValue>;
        default: null;
    };
    mobile: {
        type: import("vue").PropType<boolean | null>;
        default: boolean;
    };
    class: import("vue").PropType<import("../../composables/component.js").ClassValue>;
    theme: StringConstructor;
    headers: import("vue").PropType<DeepReadonly<DataTableHeader[]>>;
    tag: {
        type: import("vue").PropType<string | import("../../util/index.js").JSXComponent>;
        default: string;
    };
    sticky: BooleanConstructor;
    noDataText: {
        type: StringConstructor;
        default: string;
    };
    loadingText: {
        type: StringConstructor;
        default: string;
    };
    sortBy: {
        type: import("vue").PropType<readonly import("./composables/sort.js").SortItem[]>;
        default: () => never[];
    };
    mobileBreakpoint: import("vue").PropType<number | import("../../types.js").DisplayBreakpoint>;
    modelValue: {
        type: import("vue").PropType<readonly any[]>;
        default: () => never[];
    };
    expandOnClick: BooleanConstructor;
    showExpand: BooleanConstructor;
    multiSort: BooleanConstructor;
    mustSort: BooleanConstructor;
    customKeySort: import("vue").PropType<Record<string, import("./types.js").DataTableCompareFunction>>;
    headerProps: {
        type: import("vue").PropType<Record<string, any>>;
    };
    cellProps: import("vue").PropType<CellProps<any>>;
    disableSort: BooleanConstructor;
    items: {
        type: import("vue").PropType<import("./composables/items.js").DataTableItemProps["items"]>;
        default: () => never[];
    };
    itemValue: {
        type: import("vue").PropType<SelectItemKey>;
        default: string;
    };
    itemSelectable: {
        type: import("vue").PropType<SelectItemKey>;
        default: null;
    };
    returnObject: BooleanConstructor;
    rowProps: import("vue").PropType<RowProps<any>>;
    showSelect: BooleanConstructor;
    selectStrategy: {
        type: import("vue").PropType<"single" | "page" | "all">;
        default: string;
    };
    valueComparator: {
        type: import("vue").PropType<typeof import("../../util/index.js").deepEqual>;
        default: typeof import("../../util/index.js").deepEqual;
    };
    density: {
        type: import("vue").PropType<import("../../composables/density.js").Density>;
        default: string;
        validator: (v: any) => boolean;
    };
    hideNoData: BooleanConstructor;
    hover: BooleanConstructor;
    fixedHeader: BooleanConstructor;
    sortAscIcon: {
        type: import("vue").PropType<import("../../composables/icons.js").IconValue>;
        default: string;
    };
    sortDescIcon: {
        type: import("vue").PropType<import("../../composables/icons.js").IconValue>;
        default: string;
    };
    fixedFooter: BooleanConstructor;
    hideDefaultBody: BooleanConstructor;
    hideDefaultHeader: BooleanConstructor;
}, import("vue").ExtractPropTypes<{
    customFilter: import("vue").PropType<import("../../composables/filter.js").FilterFunction>;
    customKeyFilter: import("vue").PropType<import("../../composables/filter.js").FilterKeyFunctions>;
    filterKeys: import("vue").PropType<import("../../composables/filter.js").FilterKeys>;
    filterMode: {
        type: import("vue").PropType<import("../../composables/filter.js").FilterMode>;
        default: string;
    };
    noFilter: BooleanConstructor;
    itemHeight: {
        type: (StringConstructor | NumberConstructor)[];
        default: null;
    };
    itemKey: {
        type: import("vue").PropType<SelectItemKey>;
        default: null;
    };
    height: (StringConstructor | NumberConstructor)[];
    groupBy: {
        type: import("vue").PropType<readonly import("./composables/sort.js").SortItem[]>;
        default: () => never[];
    };
    search: StringConstructor;
    width: (StringConstructor | NumberConstructor)[];
    color: StringConstructor;
    expanded: {
        type: import("vue").PropType<readonly string[]>;
        default: () => never[];
    };
    loading: (StringConstructor | BooleanConstructor)[];
    style: {
        type: import("vue").PropType<import("vue").StyleValue>;
        default: null;
    };
    mobile: {
        type: import("vue").PropType<boolean | null>;
        default: boolean;
    };
    class: import("vue").PropType<import("../../composables/component.js").ClassValue>;
    theme: StringConstructor;
    headers: import("vue").PropType<DeepReadonly<DataTableHeader[]>>;
    tag: {
        type: import("vue").PropType<string | import("../../util/index.js").JSXComponent>;
        default: string;
    };
    sticky: BooleanConstructor;
    noDataText: {
        type: StringConstructor;
        default: string;
    };
    loadingText: {
        type: StringConstructor;
        default: string;
    };
    sortBy: {
        type: import("vue").PropType<readonly import("./composables/sort.js").SortItem[]>;
        default: () => never[];
    };
    mobileBreakpoint: import("vue").PropType<number | import("../../types.js").DisplayBreakpoint>;
    modelValue: {
        type: import("vue").PropType<readonly any[]>;
        default: () => never[];
    };
    expandOnClick: BooleanConstructor;
    showExpand: BooleanConstructor;
    multiSort: BooleanConstructor;
    mustSort: BooleanConstructor;
    customKeySort: import("vue").PropType<Record<string, import("./types.js").DataTableCompareFunction>>;
    headerProps: {
        type: import("vue").PropType<Record<string, any>>;
    };
    cellProps: import("vue").PropType<CellProps<any>>;
    disableSort: BooleanConstructor;
    items: {
        type: import("vue").PropType<import("./composables/items.js").DataTableItemProps["items"]>;
        default: () => never[];
    };
    itemValue: {
        type: import("vue").PropType<SelectItemKey>;
        default: string;
    };
    itemSelectable: {
        type: import("vue").PropType<SelectItemKey>;
        default: null;
    };
    returnObject: BooleanConstructor;
    rowProps: import("vue").PropType<RowProps<any>>;
    showSelect: BooleanConstructor;
    selectStrategy: {
        type: import("vue").PropType<"single" | "page" | "all">;
        default: string;
    };
    valueComparator: {
        type: import("vue").PropType<typeof import("../../util/index.js").deepEqual>;
        default: typeof import("../../util/index.js").deepEqual;
    };
    density: {
        type: import("vue").PropType<import("../../composables/density.js").Density>;
        default: string;
        validator: (v: any) => boolean;
    };
    hideNoData: BooleanConstructor;
    hover: BooleanConstructor;
    fixedHeader: BooleanConstructor;
    sortAscIcon: {
        type: import("vue").PropType<import("../../composables/icons.js").IconValue>;
        default: string;
    };
    sortDescIcon: {
        type: import("vue").PropType<import("../../composables/icons.js").IconValue>;
        default: string;
    };
    fixedFooter: BooleanConstructor;
    hideDefaultBody: BooleanConstructor;
    hideDefaultHeader: BooleanConstructor;
}>>;
export type VDataTableVirtual = InstanceType<typeof VDataTableVirtual>;

