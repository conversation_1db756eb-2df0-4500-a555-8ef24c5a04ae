import axios from 'axios'

const API_BASE_URL = 'http://localhost:3000/api'

// 创建axios实例
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
})

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('admin_token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    return response
  },
  (error) => {
    if (error.response?.status === 401) {
      // Token过期或无效，清除本地存储并跳转到登录页
      localStorage.removeItem('admin_token')
      window.location.href = '/login'
    }
    return Promise.reject(error)
  }
)

// 用户管理API
export const usersApi = {
  getList: (params) => api.get('/users', { params }),
  getDetail: (id) => api.get(`/users/${id}`),
  update: (id, data) => api.put(`/users/${id}`, data),
  delete: (id) => api.delete(`/users/${id}`),
}

// 新闻管理API
export const newsApi = {
  getList: (params) => api.get('/news', { params }),
  getDetail: (id) => api.get(`/news/${id}`),
  getCategories: () => api.get('/news/categories'),
  create: (data) => api.post('/news', data),
  update: (id, data) => api.put(`/news/${id}`, data),
  delete: (id) => api.delete(`/news/${id}`),
  createCategory: (data) => api.post('/news/categories', data),
}

// 文章管理API
export const articlesApi = {
  getList: (params) => api.get('/articles', { params }),
  getDetail: (id) => api.get(`/articles/${id}`),
  getCategories: () => api.get('/articles/categories'),
  getPending: (params) => api.get('/articles/admin/pending', { params }),
  review: (id, data) => api.post(`/articles/${id}/review`, data),
  update: (id, data) => api.put(`/articles/${id}`, data),
  delete: (id) => api.delete(`/articles/${id}`),
}

// 认证API
export const authApi = {
  login: (data) => api.post('/auth/login', data),
  getCurrentUser: () => api.get('/auth/me'),
}

// 统计API
export const statsApi = {
  getDashboard: () => api.get('/stats/dashboard'),
}

export default api
