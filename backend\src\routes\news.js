const express = require('express');
const router = express.Router();
const {
  getNewsList,
  getNewsDetail,
  createNews,
  updateNews,
  deleteNews,
  getTopNews,
  getCategories,
  createCategory,
  newsValidation
} = require('../controllers/newsController');
const { authenticateToken, requireModerator } = require('../middleware/auth');

// 公开路由
router.get('/', getNewsList);
router.get('/top', getTopNews);
router.get('/categories', getCategories);
router.get('/:id', getNewsDetail);

// 需要审核员权限的路由
router.post('/', authenticateToken, requireModerator, newsValidation, createNews);
router.put('/:id', authenticateToken, requireModerator, updateNews);
router.delete('/:id', authenticateToken, requireModerator, deleteNews);
router.post('/categories', authenticateToken, requireModerator, createCategory);

module.exports = router;
