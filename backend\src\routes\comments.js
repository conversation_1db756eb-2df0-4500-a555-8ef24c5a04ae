const express = require('express');
const router = express.Router();
const {
  getComments,
  createComment,
  updateComment,
  deleteComment,
  getPendingComments,
  reviewComment,
  commentValidation
} = require('../controllers/commentController');
const { authenticateToken, requireModerator, optionalAuth } = require('../middleware/auth');

// 获取评论列表
router.get('/:target_type/:target_id', optionalAuth, getComments);

// 需要登录的路由
router.post('/', authenticateToken, commentValidation, createComment);
router.put('/:id', authenticateToken, updateComment);
router.delete('/:id', authenticateToken, deleteComment);

// 需要审核员权限的路由
router.get('/admin/pending', authenticateToken, requireModerator, getPendingComments);
router.post('/:id/review', authenticateToken, requireModerator, reviewComment);

module.exports = router;
