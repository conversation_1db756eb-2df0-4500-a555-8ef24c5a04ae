<template>
  <v-container class="fill-height" fluid>
    <v-row align="center" justify="center">
      <v-col cols="12" sm="8" md="6" lg="4">
        <v-card class="elevation-12">
          <v-card-title class="text-center pa-6">
            <h1 class="text-h4">登录爱生网</h1>
          </v-card-title>
          
          <v-card-text>
            <v-form ref="form" v-model="valid" @submit.prevent="handleLogin">
              <v-text-field
                v-model="loginForm.username"
                :rules="usernameRules"
                label="用户名"
                prepend-inner-icon="mdi-account"
                variant="outlined"
                required
              ></v-text-field>

              <v-text-field
                v-model="loginForm.password"
                :rules="passwordRules"
                :type="showPassword ? 'text' : 'password'"
                label="密码"
                prepend-inner-icon="mdi-lock"
                :append-inner-icon="showPassword ? 'mdi-eye' : 'mdi-eye-off'"
                @click:append-inner="showPassword = !showPassword"
                variant="outlined"
                required
              ></v-text-field>

              <v-alert
                v-if="errorMessage"
                type="error"
                class="mb-4"
                dismissible
                @click:close="errorMessage = ''"
              >
                {{ errorMessage }}
              </v-alert>

              <v-btn
                :loading="authStore.loading"
                :disabled="!valid"
                color="primary"
                size="large"
                type="submit"
                block
                class="mb-4"
              >
                登录
              </v-btn>

              <div class="text-center">
                <span class="text-body-2">还没有账户？</span>
                <router-link to="/register" class="text-primary text-decoration-none">
                  立即注册
                </router-link>
              </div>
            </v-form>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '../stores/auth'

const router = useRouter()
const authStore = useAuthStore()

const form = ref()
const valid = ref(false)
const showPassword = ref(false)
const errorMessage = ref('')

const loginForm = reactive({
  username: '',
  password: ''
})

const usernameRules = [
  (v: string) => !!v || '用户名不能为空',
  (v: string) => v.length >= 3 || '用户名至少3个字符'
]

const passwordRules = [
  (v: string) => !!v || '密码不能为空',
  (v: string) => v.length >= 6 || '密码至少6个字符'
]

const handleLogin = async () => {
  if (!valid.value) return

  const result = await authStore.login(loginForm.username, loginForm.password)
  
  if (result.success) {
    // 登录成功，跳转到首页或之前的页面
    const redirect = router.currentRoute.value.query.redirect as string
    router.push(redirect || '/')
  } else {
    errorMessage.value = result.message || '登录失败'
  }
}
</script>

<style scoped>
.text-decoration-none {
  text-decoration: none;
}
</style>
