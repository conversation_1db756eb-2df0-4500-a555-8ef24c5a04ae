<template>
  <v-container class="py-8" max-width="1200">
    <!-- 页面标题和操作按钮 -->
    <v-row class="mb-6" align="center">
      <v-col cols="12" md="8">
        <h1 class="text-h3 mb-2">爱生文苑</h1>
        <p class="text-body-1 text-grey-darken-1">
          分享学习心得，展示原创作品，交流学术思想
        </p>
      </v-col>
      <v-col cols="12" md="4" class="text-center text-md-right">
        <v-btn
          v-if="authStore.isLoggedIn"
          to="/submit-article"
          color="primary"
          size="large"
          prepend-icon="mdi-pencil"
          class="px-6"
        >
          投稿文章
        </v-btn>
        <v-btn
          v-else
          to="/login"
          color="primary"
          variant="outlined"
          size="large"
          prepend-icon="mdi-login"
          class="px-6"
        >
          登录后投稿
        </v-btn>
      </v-col>
    </v-row>

    <!-- 筛选和搜索 -->
    <v-card class="mb-6 pa-4">
      <v-row align="center">
        <v-col cols="12" sm="6" md="3">
          <v-select
            v-model="selectedCategory"
            :items="categoryItems"
            label="文章分类"
            variant="outlined"
            density="comfortable"
            clearable
            @update:model-value="fetchArticles"
          ></v-select>
        </v-col>
        <v-col cols="12" sm="6" md="6">
          <v-text-field
            v-model="searchQuery"
            label="搜索文章标题或内容"
            prepend-inner-icon="mdi-magnify"
            variant="outlined"
            density="comfortable"
            clearable
            @keyup.enter="fetchArticles"
          ></v-text-field>
        </v-col>
        <v-col cols="12" md="3">
          <v-btn
            @click="fetchArticles"
            color="primary"
            size="large"
            block
            prepend-icon="mdi-magnify"
          >
            搜索
          </v-btn>
        </v-col>
      </v-row>
    </v-card>

    <!-- 文章列表 -->
    <v-row v-if="loading">
      <v-col cols="12" class="text-center">
        <v-progress-circular indeterminate color="primary"></v-progress-circular>
      </v-col>
    </v-row>

    <v-row v-else-if="articles.length === 0">
      <v-col cols="12" class="text-center">
        <v-icon size="64" color="grey">mdi-file-document-outline</v-icon>
        <p class="text-h6 text-grey mt-4">暂无文章</p>
      </v-col>
    </v-row>

    <v-row v-else>
      <v-col
        v-for="article in articles"
        :key="article.id"
        cols="12"
        sm="6"
        lg="4"
        xl="3"
      >
        <v-card
          class="article-card h-100"
          @click="$router.push(`/articles/${article.id}`)"
          hover
          elevation="2"
        >
          <v-img
            v-if="article.cover_image"
            :src="article.cover_image"
            height="200"
            cover
          ></v-img>
          
          <v-card-title class="text-h6">
            {{ article.title }}
          </v-card-title>
          
          <v-card-text>
            <p class="text-body-2 text-grey-darken-1 mb-3">
              {{ article.summary || article.content.substring(0, 100) + '...' }}
            </p>
            
            <div class="d-flex align-center mb-2">
              <v-chip size="small" color="primary" variant="outlined">
                {{ article.category_name || '未分类' }}
              </v-chip>
              <v-spacer></v-spacer>
              <span class="text-caption text-grey">
                {{ article.author_name }}
              </span>
            </div>
            
            <div class="d-flex align-center">
              <v-icon size="small" class="mr-1">mdi-eye</v-icon>
              <span class="text-caption mr-3">{{ article.view_count }}</span>
              
              <v-icon size="small" class="mr-1">mdi-heart</v-icon>
              <span class="text-caption mr-3">{{ article.like_count }}</span>
              
              <v-spacer></v-spacer>
              
              <span class="text-caption text-grey">
                {{ formatDate(article.published_at) }}
              </span>
            </div>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <!-- 分页 -->
    <v-row v-if="totalPages > 1" class="mt-6">
      <v-col cols="12" class="text-center">
        <v-pagination
          v-model="currentPage"
          :length="totalPages"
          @update:model-value="fetchArticles"
        ></v-pagination>
      </v-col>
    </v-row>
  </v-container>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useAuthStore } from '../stores/auth'
import { articlesApi } from '../services/api'

const authStore = useAuthStore()

const loading = ref(true)
const articles = ref([])
const categories = ref([])
const selectedCategory = ref(null)
const searchQuery = ref('')
const currentPage = ref(1)
const totalPages = ref(1)

const categoryItems = ref([
  { title: '全部分类', value: null }
])

const fetchCategories = async () => {
  try {
    const response = await articlesApi.getCategories()
    categories.value = response.data.categories
    
    categoryItems.value = [
      { title: '全部分类', value: null },
      ...categories.value.map((cat: any) => ({
        title: cat.name,
        value: cat.id
      }))
    ]
  } catch (error) {
    console.error('获取文章分类失败:', error)
  }
}

const fetchArticles = async () => {
  loading.value = true
  try {
    const params = {
      page: currentPage.value,
      limit: 12,
      status: 'published'
    }
    
    if (selectedCategory.value) {
      params.category_id = selectedCategory.value
    }
    
    if (searchQuery.value) {
      params.search = searchQuery.value
    }

    const response = await articlesApi.getList(params)
    articles.value = response.data.articles
    totalPages.value = response.data.totalPages
  } catch (error) {
    console.error('获取文章列表失败:', error)
  } finally {
    loading.value = false
  }
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('zh-CN')
}

onMounted(() => {
  fetchCategories()
  fetchArticles()
})
</script>

<style scoped>
.article-card {
  cursor: pointer;
  transition: transform 0.2s;
  height: 100%;
}

.article-card:hover {
  transform: translateY(-4px);
}
</style>
