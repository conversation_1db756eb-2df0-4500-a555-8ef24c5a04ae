<template>
  <v-container class="py-8">
    <v-row v-if="loading">
      <v-col cols="12" class="text-center">
        <v-progress-circular indeterminate color="primary"></v-progress-circular>
      </v-col>
    </v-row>

    <div v-else-if="article">
      <!-- 文章内容 -->
      <v-row>
        <v-col cols="12" md="8">
          <v-card>
            <v-card-title class="text-h4 pa-6">
              {{ article.title }}
            </v-card-title>
            
            <v-card-text>
              <div class="d-flex align-center mb-4">
                <v-chip color="primary" variant="outlined">
                  {{ article.category_name || '未分类' }}
                </v-chip>
                <v-spacer></v-spacer>
                <span class="text-body-2 text-grey">
                  作者: {{ article.author_name }}
                </span>
                <span class="text-body-2 text-grey ml-4">
                  发布时间: {{ formatDate(article.published_at) }}
                </span>
              </div>

              <div class="d-flex align-center mb-6">
                <v-icon size="small" class="mr-1">mdi-eye</v-icon>
                <span class="text-caption mr-4">{{ article.view_count }} 浏览</span>
                
                <v-icon size="small" class="mr-1">mdi-heart</v-icon>
                <span class="text-caption mr-4">{{ article.like_count }} 点赞</span>
                
                <v-btn
                  v-if="authStore.isLoggedIn"
                  @click="likeArticle"
                  :loading="liking"
                  color="red"
                  variant="outlined"
                  size="small"
                  prepend-icon="mdi-heart"
                >
                  点赞
                </v-btn>
              </div>

              <v-img
                v-if="article.cover_image"
                :src="article.cover_image"
                class="mb-6"
                max-height="400"
              ></v-img>

              <div class="article-content" v-html="article.content"></div>
            </v-card-text>
          </v-card>
        </v-col>

        <!-- 侧边栏 -->
        <v-col cols="12" md="4">
          <v-card class="mb-4">
            <v-card-title>作者信息</v-card-title>
            <v-card-text>
              <div class="d-flex align-center">
                <v-avatar size="48" class="mr-3">
                  <v-icon>mdi-account-circle</v-icon>
                </v-avatar>
                <div>
                  <div class="text-h6">{{ article.author_name }}</div>
                  <div class="text-caption text-grey">作者</div>
                </div>
              </div>
            </v-card-text>
          </v-card>

          <v-card>
            <v-card-title>相关文章</v-card-title>
            <v-card-text>
              <p class="text-body-2 text-grey">暂无相关文章</p>
            </v-card-text>
          </v-card>
        </v-col>
      </v-row>
    </div>

    <v-row v-else>
      <v-col cols="12" class="text-center">
        <v-icon size="64" color="grey">mdi-file-document-remove</v-icon>
        <p class="text-h6 text-grey mt-4">文章不存在</p>
        <v-btn to="/articles" color="primary" class="mt-4">
          返回文章列表
        </v-btn>
      </v-col>
    </v-row>
  </v-container>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { useAuthStore } from '../stores/auth'
import { articlesApi } from '../services/api'

const route = useRoute()
const authStore = useAuthStore()

const loading = ref(true)
const liking = ref(false)
const article = ref(null)

const fetchArticle = async () => {
  try {
    const id = parseInt(route.params.id as string)
    const response = await articlesApi.getDetail(id)
    article.value = response.data.article
  } catch (error) {
    console.error('获取文章详情失败:', error)
    article.value = null
  } finally {
    loading.value = false
  }
}

const likeArticle = async () => {
  if (!article.value) return
  
  liking.value = true
  try {
    await articlesApi.like(article.value.id)
    article.value.like_count += 1
  } catch (error) {
    console.error('点赞失败:', error)
  } finally {
    liking.value = false
  }
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

onMounted(() => {
  fetchArticle()
})
</script>

<style scoped>
.article-content {
  line-height: 1.8;
  font-size: 16px;
}

.article-content :deep(p) {
  margin-bottom: 1rem;
}

.article-content :deep(h1),
.article-content :deep(h2),
.article-content :deep(h3),
.article-content :deep(h4),
.article-content :deep(h5),
.article-content :deep(h6) {
  margin: 1.5rem 0 1rem 0;
  font-weight: 600;
}

.article-content :deep(img) {
  max-width: 100%;
  height: auto;
  margin: 1rem 0;
}
</style>
