const { query } = require('../config/database');

class News {
  // 创建新闻
  static async create(newsData) {
    const {
      title,
      content,
      summary,
      cover_image,
      category_id,
      author_id,
      is_top = false,
      is_featured = false,
      status = 'draft'
    } = newsData;

    const result = await query(
      `INSERT INTO news (title, content, summary, cover_image, category_id, author_id, is_top, is_featured, status, published_at) 
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        title,
        content,
        summary,
        cover_image,
        category_id,
        author_id,
        is_top,
        is_featured,
        status,
        status === 'published' ? new Date() : null
      ]
    );

    return result.insertId;
  }

  // 根据ID获取新闻
  static async findById(id) {
    const news = await query(
      `SELECT n.*, nc.name as category_name, u.nickname as author_name
       FROM news n
       LEFT JOIN news_categories nc ON n.category_id = nc.id
       LEFT JOIN users u ON n.author_id = u.id
       WHERE n.id = ?`,
      [id]
    );
    return news[0] || null;
  }

  // 获取新闻列表
  static async getList(page = 1, limit = 10, filters = {}) {
    const offset = (page - 1) * limit;
    let whereClause = 'WHERE 1=1';
    const params = [];

    if (filters.category_id) {
      whereClause += ' AND n.category_id = ?';
      params.push(filters.category_id);
    }

    if (filters.status) {
      whereClause += ' AND n.status = ?';
      params.push(filters.status);
    }

    if (filters.is_top !== undefined) {
      whereClause += ' AND n.is_top = ?';
      params.push(filters.is_top);
    }

    if (filters.search) {
      whereClause += ' AND (n.title LIKE ? OR n.content LIKE ?)';
      const searchTerm = `%${filters.search}%`;
      params.push(searchTerm, searchTerm);
    }

    // 获取总数
    const countResult = await query(
      `SELECT COUNT(*) as total FROM news n ${whereClause}`,
      params
    );
    const total = countResult[0].total;

    // 获取新闻列表
    const news = await query(
      `SELECT n.*, nc.name as category_name, u.nickname as author_name
       FROM news n
       LEFT JOIN news_categories nc ON n.category_id = nc.id
       LEFT JOIN users u ON n.author_id = u.id
       ${whereClause}
       ORDER BY n.is_top DESC, n.published_at DESC, n.created_at DESC
       LIMIT ? OFFSET ?`,
      [...params, limit, offset]
    );

    return {
      news,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit)
    };
  }

  // 获取置顶新闻
  static async getTopNews(limit = 5) {
    const news = await query(
      `SELECT n.*, nc.name as category_name, u.nickname as author_name
       FROM news n
       LEFT JOIN news_categories nc ON n.category_id = nc.id
       LEFT JOIN users u ON n.author_id = u.id
       WHERE n.status = 'published' AND n.is_top = true
       ORDER BY n.published_at DESC
       LIMIT ?`,
      [limit]
    );
    return news;
  }

  // 更新新闻
  static async update(id, updateData) {
    const fields = [];
    const values = [];

    Object.keys(updateData).forEach(key => {
      if (updateData[key] !== undefined) {
        fields.push(`${key} = ?`);
        values.push(updateData[key]);
      }
    });

    if (fields.length === 0) return false;

    // 如果状态改为published，设置发布时间
    if (updateData.status === 'published') {
      fields.push('published_at = ?');
      values.push(new Date());
    }

    values.push(id);

    const result = await query(
      `UPDATE news SET ${fields.join(', ')} WHERE id = ?`,
      values
    );

    return result.affectedRows > 0;
  }

  // 增加浏览次数
  static async incrementViewCount(id) {
    const result = await query(
      'UPDATE news SET view_count = view_count + 1 WHERE id = ?',
      [id]
    );
    return result.affectedRows > 0;
  }

  // 删除新闻
  static async delete(id) {
    const result = await query('DELETE FROM news WHERE id = ?', [id]);
    return result.affectedRows > 0;
  }

  // 获取新闻分类
  static async getCategories() {
    const categories = await query(
      'SELECT * FROM news_categories WHERE status = "active" ORDER BY sort_order ASC, id ASC'
    );
    return categories;
  }

  // 创建新闻分类
  static async createCategory(categoryData) {
    const { name, description, sort_order = 0 } = categoryData;
    
    const result = await query(
      'INSERT INTO news_categories (name, description, sort_order) VALUES (?, ?, ?)',
      [name, description, sort_order]
    );
    
    return result.insertId;
  }

  // 更新新闻分类
  static async updateCategory(id, updateData) {
    const fields = [];
    const values = [];

    Object.keys(updateData).forEach(key => {
      if (updateData[key] !== undefined) {
        fields.push(`${key} = ?`);
        values.push(updateData[key]);
      }
    });

    if (fields.length === 0) return false;

    values.push(id);

    const result = await query(
      `UPDATE news_categories SET ${fields.join(', ')} WHERE id = ?`,
      values
    );

    return result.affectedRows > 0;
  }

  // 删除新闻分类
  static async deleteCategory(id) {
    const result = await query('DELETE FROM news_categories WHERE id = ?', [id]);
    return result.affectedRows > 0;
  }
}

module.exports = News;
