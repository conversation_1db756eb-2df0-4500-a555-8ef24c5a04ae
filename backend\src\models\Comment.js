const { query, transaction } = require('../config/database');

class Comment {
  // 创建评论
  static async create(commentData) {
    const {
      content,
      user_id,
      target_type,
      target_id,
      parent_id = null,
      status = 'pending'
    } = commentData;

    const result = await query(
      `INSERT INTO comments (content, user_id, target_type, target_id, parent_id, status) 
       VALUES (?, ?, ?, ?, ?, ?)`,
      [content, user_id, target_type, target_id, parent_id, status]
    );

    return result.insertId;
  }

  // 根据ID获取评论
  static async findById(id) {
    const comments = await query(
      `SELECT c.*, u.nickname as user_nickname, u.avatar as user_avatar
       FROM comments c
       LEFT JOIN users u ON c.user_id = u.id
       WHERE c.id = ?`,
      [id]
    );
    return comments[0] || null;
  }

  // 获取目标的评论列表
  static async getByTarget(targetType, targetId, page = 1, limit = 10, status = 'approved') {
    const offset = (page - 1) * limit;

    // 获取总数
    const countResult = await query(
      'SELECT COUNT(*) as total FROM comments WHERE target_type = ? AND target_id = ? AND status = ? AND parent_id IS NULL',
      [targetType, targetId, status]
    );
    const total = countResult[0].total;

    // 获取顶级评论
    const comments = await query(
      `SELECT c.*, u.nickname as user_nickname, u.avatar as user_avatar
       FROM comments c
       LEFT JOIN users u ON c.user_id = u.id
       WHERE c.target_type = ? AND c.target_id = ? AND c.status = ? AND c.parent_id IS NULL
       ORDER BY c.created_at DESC
       LIMIT ? OFFSET ?`,
      [targetType, targetId, status, limit, offset]
    );

    // 获取每个顶级评论的回复
    for (let comment of comments) {
      const replies = await query(
        `SELECT c.*, u.nickname as user_nickname, u.avatar as user_avatar
         FROM comments c
         LEFT JOIN users u ON c.user_id = u.id
         WHERE c.parent_id = ? AND c.status = ?
         ORDER BY c.created_at ASC`,
        [comment.id, status]
      );
      comment.replies = replies;
    }

    return {
      comments,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit)
    };
  }

  // 获取用户的评论列表（包括待审核的）
  static async getUserComments(userId, targetType, targetId, status = null) {
    let whereClause = 'WHERE c.user_id = ? AND c.target_type = ? AND c.target_id = ?';
    const params = [userId, targetType, targetId];

    if (status) {
      whereClause += ' AND c.status = ?';
      params.push(status);
    }

    const comments = await query(
      `SELECT c.*, u.nickname as user_nickname, u.avatar as user_avatar
       FROM comments c
       LEFT JOIN users u ON c.user_id = u.id
       ${whereClause}
       ORDER BY c.created_at DESC`,
      params
    );

    return comments;
  }

  // 获取待审核评论列表
  static async getPendingList(page = 1, limit = 10) {
    const offset = (page - 1) * limit;

    // 获取总数
    const countResult = await query(
      'SELECT COUNT(*) as total FROM comments WHERE status = "pending"'
    );
    const total = countResult[0].total;

    // 获取待审核评论列表
    const comments = await query(
      `SELECT c.*, u.nickname as user_nickname, u.avatar as user_avatar
       FROM comments c
       LEFT JOIN users u ON c.user_id = u.id
       WHERE c.status = 'pending'
       ORDER BY c.created_at ASC
       LIMIT ? OFFSET ?`,
      [limit, offset]
    );

    return {
      comments,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit)
    };
  }

  // 审核评论
  static async review(id, reviewData) {
    const { status, reject_reason, reviewed_by } = reviewData;
    
    return await transaction(async (connection) => {
      // 更新评论状态
      const updateFields = ['status = ?', 'reviewed_by = ?', 'reviewed_at = ?'];
      const updateValues = [status, reviewed_by, new Date()];

      if (reject_reason) {
        updateFields.push('reject_reason = ?');
        updateValues.push(reject_reason);
      }

      updateValues.push(id);

      await connection.execute(
        `UPDATE comments SET ${updateFields.join(', ')} WHERE id = ?`,
        updateValues
      );

      // 如果审核通过，给用户加积分
      if (status === 'approved') {
        const comment = await this.findById(id);
        if (comment) {
          await connection.execute(
            'UPDATE users SET points = points + ? WHERE id = ?',
            [2, comment.user_id] // 评论通过获得2积分
          );

          await connection.execute(
            `INSERT INTO point_logs (user_id, points, type, source, source_id, description) 
             VALUES (?, ?, ?, ?, ?, ?)`,
            [comment.user_id, 2, 'earn', 'comment', id, '评论通过获得积分']
          );
        }
      }

      return true;
    });
  }

  // 更新评论
  static async update(id, updateData) {
    const fields = [];
    const values = [];

    Object.keys(updateData).forEach(key => {
      if (updateData[key] !== undefined) {
        fields.push(`${key} = ?`);
        values.push(updateData[key]);
      }
    });

    if (fields.length === 0) return false;

    values.push(id);

    const result = await query(
      `UPDATE comments SET ${fields.join(', ')} WHERE id = ?`,
      values
    );

    return result.affectedRows > 0;
  }

  // 删除评论
  static async delete(id) {
    return await transaction(async (connection) => {
      // 先删除所有回复
      await connection.execute('DELETE FROM comments WHERE parent_id = ?', [id]);
      
      // 再删除评论本身
      const result = await connection.execute('DELETE FROM comments WHERE id = ?', [id]);
      
      return result.affectedRows > 0;
    });
  }
}

module.exports = Comment;
