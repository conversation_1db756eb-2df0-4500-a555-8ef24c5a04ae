<template>
  <div>
    <!-- 轮播图 -->
    <v-carousel
      v-if="banners.length > 0"
      height="400"
      cycle
      interval="5000"
      hide-delimiter-background
      show-arrows-on-hover
    >
      <v-carousel-item
        v-for="banner in banners"
        :key="banner.id"
        :src="banner.image"
        @click="handleBannerClick(banner)"
        class="cursor-pointer"
      >
        <div class="carousel-content">
          <v-container>
            <v-row align="end" class="fill-height">
              <v-col>
                <h2 class="text-white text-h4 mb-2">{{ banner.title }}</h2>
              </v-col>
            </v-row>
          </v-container>
        </div>
      </v-carousel-item>
    </v-carousel>

    <v-container class="py-8">
      <!-- 置顶新闻 -->
      <v-row>
        <v-col cols="12">
          <h2 class="text-h4 mb-6">最新资讯</h2>
        </v-col>
      </v-row>

      <v-row v-if="loading">
        <v-col cols="12" class="text-center">
          <v-progress-circular indeterminate color="primary"></v-progress-circular>
        </v-col>
      </v-row>

      <v-row v-else>
        <!-- 置顶新闻 -->
        <v-col
          v-for="news in topNews"
          :key="news.id"
          cols="12"
          md="6"
          lg="4"
        >
          <v-card
            class="news-card"
            @click="$router.push(`/news/${news.id}`)"
            hover
          >
            <v-img
              v-if="news.cover_image"
              :src="news.cover_image"
              height="200"
              cover
            >
              <v-chip
                v-if="news.is_top"
                color="red"
                size="small"
                class="ma-2"
              >
                置顶
              </v-chip>
            </v-img>
            
            <v-card-title class="text-h6">
              {{ news.title }}
            </v-card-title>
            
            <v-card-text>
              <p class="text-body-2 text-grey-darken-1 mb-2">
                {{ news.summary || news.content.substring(0, 100) + '...' }}
              </p>
              
              <div class="d-flex align-center">
                <v-chip size="small" color="primary" variant="outlined">
                  {{ news.category_name }}
                </v-chip>
                <v-spacer></v-spacer>
                <span class="text-caption text-grey">
                  {{ formatDate(news.published_at) }}
                </span>
              </div>
            </v-card-text>
          </v-card>
        </v-col>
      </v-row>

      <!-- 更多新闻按钮 -->
      <v-row class="mt-6">
        <v-col cols="12" class="text-center">
          <v-btn
            to="/news"
            color="primary"
            size="large"
            variant="outlined"
          >
            查看更多新闻
            <v-icon right>mdi-arrow-right</v-icon>
          </v-btn>
        </v-col>
      </v-row>

      <!-- 快速导航 -->
      <v-row class="mt-12">
        <v-col cols="12">
          <h2 class="text-h4 mb-6">快速导航</h2>
        </v-col>
      </v-row>

      <v-row>
        <v-col
          v-for="item in quickNavItems"
          :key="item.title"
          cols="6"
          md="3"
        >
          <v-card
            :to="item.to"
            class="text-center pa-6 quick-nav-card"
            hover
          >
            <v-icon
              :icon="item.icon"
              size="48"
              color="primary"
              class="mb-4"
            ></v-icon>
            <v-card-title class="text-h6">{{ item.title }}</v-card-title>
            <v-card-text>{{ item.description }}</v-card-text>
          </v-card>
        </v-col>
      </v-row>
    </v-container>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { newsApi } from '../services/api'

const router = useRouter()

const loading = ref(true)
const topNews = ref([])
const banners = ref([
  {
    id: 1,
    title: '欢迎来到爱生网',
    image: 'https://picsum.photos/1200/400?random=1',
    link_type: 'external',
    link_url: '/'
  },
  {
    id: 2,
    title: '新学期开学通知',
    image: 'https://picsum.photos/1200/400?random=2',
    link_type: 'external',
    link_url: '/'
  },
  {
    id: 3,
    title: '在线学习指南',
    image: 'https://picsum.photos/1200/400?random=3',
    link_type: 'external',
    link_url: '/'
  }
])

const quickNavItems = [
  {
    title: '爱生文苑',
    description: '分享学习心得和原创文章',
    icon: 'mdi-book-open-page-variant',
    to: '/articles'
  },
  {
    title: '爱生图书馆',
    description: '丰富的电子图书资源',
    icon: 'mdi-library',
    to: '/library'
  },
  {
    title: '爱生教室',
    description: '优质的在线视频课程',
    icon: 'mdi-school',
    to: '/classroom'
  },
  {
    title: '爱生积分',
    description: '积分兑换精美礼品',
    icon: 'mdi-star',
    to: '/points'
  }
]

const fetchTopNews = async () => {
  try {
    const response = await newsApi.getTop(6)
    topNews.value = response.data.news
  } catch (error) {
    console.error('获取置顶新闻失败:', error)
  } finally {
    loading.value = false
  }
}

const handleBannerClick = (banner: any) => {
  if (banner.link_url) {
    if (banner.link_type === 'external') {
      window.open(banner.link_url, '_blank')
    } else {
      router.push(banner.link_url)
    }
  }
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('zh-CN')
}

onMounted(() => {
  fetchTopNews()
})
</script>

<style scoped>
.carousel-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  padding: 2rem 0;
}

.news-card {
  cursor: pointer;
  transition: transform 0.2s;
}

.news-card:hover {
  transform: translateY(-4px);
}

.quick-nav-card {
  transition: transform 0.2s;
}

.quick-nav-card:hover {
  transform: translateY(-4px);
}

.cursor-pointer {
  cursor: pointer;
}
</style>
