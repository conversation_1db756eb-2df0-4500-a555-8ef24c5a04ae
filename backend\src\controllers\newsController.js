const { body, validationResult } = require('express-validator');
const News = require('../models/News');

// 新闻验证规则
const newsValidation = [
  body('title')
    .isLength({ min: 1, max: 200 })
    .withMessage('标题长度必须在1-200个字符之间'),
  body('content')
    .isLength({ min: 1 })
    .withMessage('内容不能为空'),
  body('category_id')
    .optional()
    .isInt({ min: 1 })
    .withMessage('分类ID必须是正整数')
];

// 获取新闻列表
const getNewsList = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const filters = {
      category_id: req.query.category_id,
      status: req.query.status || 'published',
      search: req.query.search,
      is_top: req.query.is_top
    };

    const result = await News.getList(page, limit, filters);
    res.json(result);

  } catch (error) {
    console.error('获取新闻列表失败:', error);
    res.status(500).json({ message: '服务器内部错误' });
  }
};

// 获取新闻详情
const getNewsDetail = async (req, res) => {
  try {
    const { id } = req.params;
    const news = await News.findById(id);

    if (!news) {
      return res.status(404).json({ message: '新闻不存在' });
    }

    // 如果是已发布的新闻，增加浏览次数
    if (news.status === 'published') {
      await News.incrementViewCount(id);
      news.view_count += 1;
    }

    res.json({ news });

  } catch (error) {
    console.error('获取新闻详情失败:', error);
    res.status(500).json({ message: '服务器内部错误' });
  }
};

// 创建新闻
const createNews = async (req, res) => {
  try {
    // 检查验证结果
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        message: '输入数据验证失败',
        errors: errors.array()
      });
    }

    const newsData = {
      ...req.body,
      author_id: req.user.id
    };

    const newsId = await News.create(newsData);
    const news = await News.findById(newsId);

    res.status(201).json({
      message: '新闻创建成功',
      news
    });

  } catch (error) {
    console.error('创建新闻失败:', error);
    res.status(500).json({ message: '服务器内部错误' });
  }
};

// 更新新闻
const updateNews = async (req, res) => {
  try {
    const { id } = req.params;
    const news = await News.findById(id);

    if (!news) {
      return res.status(404).json({ message: '新闻不存在' });
    }

    // 检查权限：只有作者或管理员可以编辑
    if (news.author_id !== req.user.id && req.user.role !== 'admin') {
      return res.status(403).json({ message: '权限不足' });
    }

    const success = await News.update(id, req.body);
    if (!success) {
      return res.status(400).json({ message: '更新失败' });
    }

    const updatedNews = await News.findById(id);
    res.json({
      message: '新闻更新成功',
      news: updatedNews
    });

  } catch (error) {
    console.error('更新新闻失败:', error);
    res.status(500).json({ message: '服务器内部错误' });
  }
};

// 删除新闻
const deleteNews = async (req, res) => {
  try {
    const { id } = req.params;
    const news = await News.findById(id);

    if (!news) {
      return res.status(404).json({ message: '新闻不存在' });
    }

    // 检查权限：只有作者或管理员可以删除
    if (news.author_id !== req.user.id && req.user.role !== 'admin') {
      return res.status(403).json({ message: '权限不足' });
    }

    const success = await News.delete(id);
    if (!success) {
      return res.status(400).json({ message: '删除失败' });
    }

    res.json({ message: '新闻删除成功' });

  } catch (error) {
    console.error('删除新闻失败:', error);
    res.status(500).json({ message: '服务器内部错误' });
  }
};

// 获取置顶新闻
const getTopNews = async (req, res) => {
  try {
    const limit = parseInt(req.query.limit) || 5;
    const news = await News.getTopNews(limit);
    res.json({ news });

  } catch (error) {
    console.error('获取置顶新闻失败:', error);
    res.status(500).json({ message: '服务器内部错误' });
  }
};

// 获取新闻分类
const getCategories = async (req, res) => {
  try {
    const categories = await News.getCategories();
    res.json({ categories });

  } catch (error) {
    console.error('获取新闻分类失败:', error);
    res.status(500).json({ message: '服务器内部错误' });
  }
};

// 创建新闻分类
const createCategory = async (req, res) => {
  try {
    const { name, description, sort_order } = req.body;

    if (!name) {
      return res.status(400).json({ message: '分类名称不能为空' });
    }

    const categoryId = await News.createCategory({ name, description, sort_order });
    res.status(201).json({
      message: '分类创建成功',
      category: { id: categoryId, name, description, sort_order }
    });

  } catch (error) {
    console.error('创建新闻分类失败:', error);
    res.status(500).json({ message: '服务器内部错误' });
  }
};

module.exports = {
  getNewsList,
  getNewsDetail,
  createNews,
  updateNews,
  deleteNews,
  getTopNews,
  getCategories,
  createCategory,
  newsValidation
};
