{"version": 3, "file": "zh-Hant.js", "names": ["badge", "open", "close", "dismiss", "confirmEdit", "ok", "cancel", "dataIterator", "noResultsText", "loadingText", "dataTable", "itemsPerPageText", "aria<PERSON><PERSON><PERSON>", "sortDescending", "sortAscending", "sortNone", "activateNone", "activateDescending", "activateAscending", "sortBy", "dataFooter", "itemsPerPageAll", "nextPage", "prevPage", "firstPage", "lastPage", "pageText", "dateRangeInput", "divider", "datePicker", "itemsSelected", "range", "title", "header", "input", "placeholder", "noDataText", "carousel", "prev", "next", "delimiter", "calendar", "moreEvents", "today", "clear", "prependAction", "appendAction", "otp", "fileInput", "counter", "counterSize", "fileUpload", "browse", "timePicker", "am", "pm", "pagination", "root", "previous", "page", "currentPage", "first", "last", "stepper", "rating", "item", "loading", "infiniteScroll", "loadMore", "empty", "rules", "required", "email", "number", "integer", "capital", "max<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "strictLength", "exclude", "notEmpty", "pattern"], "sources": ["../../src/locale/zh-Hant.ts"], "sourcesContent": ["export default {\n  badge: '徽章',\n  open: '開啟',\n  close: '關閉',\n  dismiss: '關閉',\n  confirmEdit: {\n    ok: '確定',\n    cancel: '取消',\n  },\n  dataIterator: {\n    noResultsText: '沒有符合條件的結果',\n    loadingText: '讀取中...',\n  },\n  dataTable: {\n    itemsPerPageText: '每頁列數：',\n    ariaLabel: {\n      sortDescending: '：降序排列。',\n      sortAscending: '：升序排列。',\n      sortNone: '無排序方式。點擊以升序排列。',\n      activateNone: '點擊以移除排序方式。',\n      activateDescending: '點擊以降序排列。',\n      activateAscending: '點擊以移除排序方式。',\n    },\n    sortBy: '排序方式',\n  },\n  dataFooter: {\n    itemsPerPageText: '每頁項目：',\n    itemsPerPageAll: '全部',\n    nextPage: '下一頁',\n    prevPage: '上一頁',\n    firstPage: '第一頁',\n    lastPage: '最後頁',\n    pageText: '{2} 條中的 {0}~{1} 條',\n  },\n  dateRangeInput: {\n    divider: '至',\n  },\n  datePicker: {\n    itemsSelected: '已選擇 {0} 個日期',\n    range: {\n      title: '選擇日期範圍',\n      header: '輸入日期範圍',\n    },\n    title: '選擇日期',\n    header: '輸入日期',\n    input: {\n      placeholder: '請輸入日期',\n    },\n  },\n  noDataText: '沒有資料',\n  carousel: {\n    prev: '上一張',\n    next: '下一張',\n    ariaLabel: {\n      delimiter: '第 {0} 張 / 共 {1} 張',\n    },\n  },\n  calendar: {\n    moreEvents: '還有其他 {0} 項',\n    today: '今天',\n  },\n  input: {\n    clear: '清除 {0}',\n    prependAction: '{0} 前置操作',\n    appendAction: '{0} 附加操作',\n    otp: '請輸入第 {0} 個 OTP 字元',\n  },\n  fileInput: {\n    counter: '{0} 個檔案',\n    counterSize: '{0} 個檔案（共 {1}）',\n  },\n  fileUpload: {\n    title: '拖曳檔案至此',\n    divider: '或',\n    browse: '瀏覽檔案',\n  },\n  timePicker: {\n    am: '上午',\n    pm: '下午',\n    title: '選擇時間',\n  },\n  pagination: {\n    ariaLabel: {\n      root: '分頁導航',\n      next: '下一頁',\n      previous: '上一頁',\n      page: '轉到頁面 {0}',\n      currentPage: '當前頁 {0}',\n      first: '第一頁',\n      last: '最後一頁',\n    },\n  },\n  stepper: {\n    next: '下一步',\n    prev: '上一步',\n  },\n  rating: {\n    ariaLabel: {\n      item: '評分 {0} / {1}',\n    },\n  },\n  loading: '載入中...',\n  infiniteScroll: {\n    loadMore: '載入更多',\n    empty: '沒有更多內容',\n  },\n  rules: {\n    required: '此欄位為必填項',\n    email: '請輸入有效的電子郵件地址',\n    number: '此欄位只能包含數字',\n    integer: '此欄位只能包含整數',\n    capital: '此欄位只能包含大寫字母',\n    maxLength: '您最多可以輸入{0}個字符',\n    minLength: '您必須至少輸入{0}個字符',\n    strictLength: '輸入欄位的長度無效',\n    exclude: '字符{0}是不允許的',\n    notEmpty: '請至少選擇一個值',\n    pattern: '格式無效',\n  },\n}\n"], "mappings": "AAAA,eAAe;EACbA,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE,IAAI;EACVC,KAAK,EAAE,IAAI;EACXC,OAAO,EAAE,IAAI;EACbC,WAAW,EAAE;IACXC,EAAE,EAAE,IAAI;IACRC,MAAM,EAAE;EACV,CAAC;EACDC,YAAY,EAAE;IACZC,aAAa,EAAE,WAAW;IAC1BC,WAAW,EAAE;EACf,CAAC;EACDC,SAAS,EAAE;IACTC,gBAAgB,EAAE,OAAO;IACzBC,SAAS,EAAE;MACTC,cAAc,EAAE,QAAQ;MACxBC,aAAa,EAAE,QAAQ;MACvBC,QAAQ,EAAE,gBAAgB;MAC1BC,YAAY,EAAE,YAAY;MAC1BC,kBAAkB,EAAE,UAAU;MAC9BC,iBAAiB,EAAE;IACrB,CAAC;IACDC,MAAM,EAAE;EACV,CAAC;EACDC,UAAU,EAAE;IACVT,gBAAgB,EAAE,OAAO;IACzBU,eAAe,EAAE,IAAI;IACrBC,QAAQ,EAAE,KAAK;IACfC,QAAQ,EAAE,KAAK;IACfC,SAAS,EAAE,KAAK;IAChBC,QAAQ,EAAE,KAAK;IACfC,QAAQ,EAAE;EACZ,CAAC;EACDC,cAAc,EAAE;IACdC,OAAO,EAAE;EACX,CAAC;EACDC,UAAU,EAAE;IACVC,aAAa,EAAE,aAAa;IAC5BC,KAAK,EAAE;MACLC,KAAK,EAAE,QAAQ;MACfC,MAAM,EAAE;IACV,CAAC;IACDD,KAAK,EAAE,MAAM;IACbC,MAAM,EAAE,MAAM;IACdC,KAAK,EAAE;MACLC,WAAW,EAAE;IACf;EACF,CAAC;EACDC,UAAU,EAAE,MAAM;EAClBC,QAAQ,EAAE;IACRC,IAAI,EAAE,KAAK;IACXC,IAAI,EAAE,KAAK;IACX3B,SAAS,EAAE;MACT4B,SAAS,EAAE;IACb;EACF,CAAC;EACDC,QAAQ,EAAE;IACRC,UAAU,EAAE,YAAY;IACxBC,KAAK,EAAE;EACT,CAAC;EACDT,KAAK,EAAE;IACLU,KAAK,EAAE,QAAQ;IACfC,aAAa,EAAE,UAAU;IACzBC,YAAY,EAAE,UAAU;IACxBC,GAAG,EAAE;EACP,CAAC;EACDC,SAAS,EAAE;IACTC,OAAO,EAAE,SAAS;IAClBC,WAAW,EAAE;EACf,CAAC;EACDC,UAAU,EAAE;IACVnB,KAAK,EAAE,QAAQ;IACfJ,OAAO,EAAE,GAAG;IACZwB,MAAM,EAAE;EACV,CAAC;EACDC,UAAU,EAAE;IACVC,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRvB,KAAK,EAAE;EACT,CAAC;EACDwB,UAAU,EAAE;IACV5C,SAAS,EAAE;MACT6C,IAAI,EAAE,MAAM;MACZlB,IAAI,EAAE,KAAK;MACXmB,QAAQ,EAAE,KAAK;MACfC,IAAI,EAAE,UAAU;MAChBC,WAAW,EAAE,SAAS;MACtBC,KAAK,EAAE,KAAK;MACZC,IAAI,EAAE;IACR;EACF,CAAC;EACDC,OAAO,EAAE;IACPxB,IAAI,EAAE,KAAK;IACXD,IAAI,EAAE;EACR,CAAC;EACD0B,MAAM,EAAE;IACNpD,SAAS,EAAE;MACTqD,IAAI,EAAE;IACR;EACF,CAAC;EACDC,OAAO,EAAE,QAAQ;EACjBC,cAAc,EAAE;IACdC,QAAQ,EAAE,MAAM;IAChBC,KAAK,EAAE;EACT,CAAC;EACDC,KAAK,EAAE;IACLC,QAAQ,EAAE,SAAS;IACnBC,KAAK,EAAE,cAAc;IACrBC,MAAM,EAAE,WAAW;IACnBC,OAAO,EAAE,WAAW;IACpBC,OAAO,EAAE,aAAa;IACtBC,SAAS,EAAE,eAAe;IAC1BC,SAAS,EAAE,eAAe;IAC1BC,YAAY,EAAE,WAAW;IACzBC,OAAO,EAAE,YAAY;IACrBC,QAAQ,EAAE,UAAU;IACpBC,OAAO,EAAE;EACX;AACF,CAAC", "ignoreList": []}