const { query, transaction } = require('../config/database');

class Article {
  // 创建文章
  static async create(articleData) {
    const {
      title,
      content,
      summary,
      cover_image,
      category_id,
      author_id,
      status = 'pending'
    } = articleData;

    const result = await query(
      `INSERT INTO articles (title, content, summary, cover_image, category_id, author_id, status) 
       VALUES (?, ?, ?, ?, ?, ?, ?)`,
      [title, content, summary, cover_image, category_id, author_id, status]
    );

    return result.insertId;
  }

  // 根据ID获取文章
  static async findById(id) {
    const articles = await query(
      `SELECT a.*, ac.name as category_name, u.nickname as author_name
       FROM articles a
       LEFT JOIN article_categories ac ON a.category_id = ac.id
       LEFT JOIN users u ON a.author_id = u.id
       WHERE a.id = ?`,
      [id]
    );
    return articles[0] || null;
  }

  // 获取文章列表
  static async getList(page = 1, limit = 10, filters = {}) {
    const offset = (page - 1) * limit;
    let whereClause = 'WHERE 1=1';
    const params = [];

    if (filters.category_id) {
      whereClause += ' AND a.category_id = ?';
      params.push(filters.category_id);
    }

    if (filters.status) {
      whereClause += ' AND a.status = ?';
      params.push(filters.status);
    }

    if (filters.author_id) {
      whereClause += ' AND a.author_id = ?';
      params.push(filters.author_id);
    }

    if (filters.search) {
      whereClause += ' AND (a.title LIKE ? OR a.content LIKE ?)';
      const searchTerm = `%${filters.search}%`;
      params.push(searchTerm, searchTerm);
    }

    // 获取总数
    const countResult = await query(
      `SELECT COUNT(*) as total FROM articles a ${whereClause}`,
      params
    );
    const total = countResult[0].total;

    // 获取文章列表
    const articles = await query(
      `SELECT a.*, ac.name as category_name, u.nickname as author_name
       FROM articles a
       LEFT JOIN article_categories ac ON a.category_id = ac.id
       LEFT JOIN users u ON a.author_id = u.id
       ${whereClause}
       ORDER BY a.published_at DESC, a.created_at DESC
       LIMIT ? OFFSET ?`,
      [...params, limit, offset]
    );

    return {
      articles,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit)
    };
  }

  // 更新文章
  static async update(id, updateData) {
    const fields = [];
    const values = [];

    Object.keys(updateData).forEach(key => {
      if (updateData[key] !== undefined) {
        fields.push(`${key} = ?`);
        values.push(updateData[key]);
      }
    });

    if (fields.length === 0) return false;

    // 如果状态改为published，设置发布时间
    if (updateData.status === 'published') {
      fields.push('published_at = ?');
      values.push(new Date());
    }

    values.push(id);

    const result = await query(
      `UPDATE articles SET ${fields.join(', ')} WHERE id = ?`,
      values
    );

    return result.affectedRows > 0;
  }

  // 审核文章
  static async review(id, reviewData) {
    const { status, reject_reason, reviewed_by } = reviewData;
    
    return await transaction(async (connection) => {
      // 更新文章状态
      const updateFields = ['status = ?', 'reviewed_by = ?', 'reviewed_at = ?'];
      const updateValues = [status, reviewed_by, new Date()];

      if (status === 'published') {
        updateFields.push('published_at = ?');
        updateValues.push(new Date());
      }

      if (reject_reason) {
        updateFields.push('reject_reason = ?');
        updateValues.push(reject_reason);
      }

      updateValues.push(id);

      await connection.execute(
        `UPDATE articles SET ${updateFields.join(', ')} WHERE id = ?`,
        updateValues
      );

      // 如果审核通过，给作者加积分
      if (status === 'published') {
        const article = await this.findById(id);
        if (article) {
          await connection.execute(
            'UPDATE users SET points = points + ? WHERE id = ?',
            [10, article.author_id] // 发布文章获得10积分
          );

          await connection.execute(
            `INSERT INTO point_logs (user_id, points, type, source, source_id, description) 
             VALUES (?, ?, ?, ?, ?, ?)`,
            [article.author_id, 10, 'earn', 'article_publish', id, '文章发布获得积分']
          );
        }
      }

      return true;
    });
  }

  // 增加浏览次数
  static async incrementViewCount(id) {
    const result = await query(
      'UPDATE articles SET view_count = view_count + 1 WHERE id = ?',
      [id]
    );
    return result.affectedRows > 0;
  }

  // 增加点赞次数
  static async incrementLikeCount(id) {
    const result = await query(
      'UPDATE articles SET like_count = like_count + 1 WHERE id = ?',
      [id]
    );
    return result.affectedRows > 0;
  }

  // 删除文章
  static async delete(id) {
    const result = await query('DELETE FROM articles WHERE id = ?', [id]);
    return result.affectedRows > 0;
  }

  // 获取文章分类
  static async getCategories() {
    const categories = await query(
      'SELECT * FROM article_categories WHERE status = "active" ORDER BY sort_order ASC, id ASC'
    );
    return categories;
  }

  // 创建文章分类
  static async createCategory(categoryData) {
    const { name, description, sort_order = 0 } = categoryData;
    
    const result = await query(
      'INSERT INTO article_categories (name, description, sort_order) VALUES (?, ?, ?)',
      [name, description, sort_order]
    );
    
    return result.insertId;
  }

  // 获取待审核文章列表
  static async getPendingList(page = 1, limit = 10) {
    const offset = (page - 1) * limit;

    // 获取总数
    const countResult = await query(
      'SELECT COUNT(*) as total FROM articles WHERE status = "pending"'
    );
    const total = countResult[0].total;

    // 获取待审核文章列表
    const articles = await query(
      `SELECT a.*, ac.name as category_name, u.nickname as author_name
       FROM articles a
       LEFT JOIN article_categories ac ON a.category_id = ac.id
       LEFT JOIN users u ON a.author_id = u.id
       WHERE a.status = 'pending'
       ORDER BY a.created_at ASC
       LIMIT ? OFFSET ?`,
      [limit, offset]
    );

    return {
      articles,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit)
    };
  }
}

module.exports = Article;
